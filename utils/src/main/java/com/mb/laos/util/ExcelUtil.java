package com.mb.laos.util;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Iterator;

public class ExcelUtil {
    public static final Double DOUBLE_0 = 0D;
    public static final Double DOUBLE_1 = 1D;
    public static final Double DOUBLE_2 = 2D;

    public static String readCellContent(Cell cell) {
        String content;

        switch (cell.getCellType()) {
            case STRING:
                content = StringUtil.getString(cell.getStringCellValue());

                break;
            case NUMERIC:
                content = BigDecimal.valueOf(cell.getNumericCellValue()).toString();

                break;
            case BOOLEAN:
                content = cell.getBooleanCellValue() + StringPool.BLANK;

                break;
            case FORMULA:
                content = cell.getCellFormula() + StringPool.BLANK;

                break;
            default:
                content = StringPool.BLANK;
        }

        return content;
    }

    public static boolean isEmptyAllCell(Row checkRow) {
        for (Cell cell : checkRow) {
            String value = ExcelUtil.readCellContent(cell);
            if (Validator.isNotNull(value)) {
                return false;
            }
        }
        return true;
    }

    public static boolean isSameTemplate(MultipartFile file, InputStream excelTemplate) throws IOException {
        XSSFWorkbook templateFile = new XSSFWorkbook(file.getInputStream());
        XSSFWorkbook uploadFile = new XSSFWorkbook(excelTemplate);

        if (templateFile.getNumberOfSheets() != uploadFile.getNumberOfSheets()) {
            return false;
        }

        // Chỉ so sánh sheet đầu tiên
        for (int i = 0; i < 1; i++) {
            Sheet templateSheet = templateFile.getSheetAt(i);
            Sheet uploadedSheet = uploadFile.getSheetAt(i);

            if (!templateSheet.getSheetName().equals(uploadedSheet.getSheetName())) {
                return false;
            }

            Iterator<Row> templateRowIterator = templateSheet.rowIterator();
            Iterator<Row> uploadedRowIterator = uploadedSheet.rowIterator();

            if (templateRowIterator.hasNext() && uploadedRowIterator.hasNext()) {
                Row templateRow = templateRowIterator.next();
                Row uploadedRow = uploadedRowIterator.next();

                if (templateRow.getPhysicalNumberOfCells() != uploadedRow.getPhysicalNumberOfCells()) {
                    return false;
                }

                for (int j = 0; j < templateRow.getPhysicalNumberOfCells(); j++) {
                    Cell templateCell = templateRow.getCell(j);
                    Cell uploadedCell = uploadedRow.getCell(j);

                    if (!templateCell.getStringCellValue().equals(uploadedCell.getStringCellValue())) {
                        return false;
                    }
                }
            }
        }

        templateFile.close();
        uploadFile.close();

        return true;
    }
}
