package com.mb.laos.util;

import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;

import java.util.UUID;

public class DiagnosticContextUtil {

    public static String getClientMessageId() {
        return ThreadContext.get(Constants.Common.CLIENT_MESSAGE_ID) != null ? ThreadContext.get(Constants.Common.CLIENT_MESSAGE_ID) : UUID.randomUUID().toString();
    }

    public static String getOutboundClientMessageId() {
        Integer currentOutBoundId = ThreadContext.get(Constants.Common.OUT_BOUND_ID) != null ? Integer.parseInt(ThreadContext.get(Constants.Common.OUT_BOUND_ID)) : 0;
        Integer outBoundId = currentOutBoundId + 1;
        ThreadContext.put(Constants.Common.OUT_BOUND_ID, outBoundId.toString());
        return getClientMessageId() + ":" + outBoundId;
    }

    public static String getUnitelMessageId() {
        String messId = getClientMessageId().split("-")[0];
        long messIdToNumber = Long.parseLong(messId, 16);

        if (messIdToNumber <= 999_999_999_999_999L) {
            return String.format("%015d", messIdToNumber);
        } else {
            return String.valueOf(messIdToNumber).substring(0, 15);
        }
    }
}
