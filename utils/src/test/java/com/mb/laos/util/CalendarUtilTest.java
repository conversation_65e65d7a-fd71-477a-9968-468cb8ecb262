package com.mb.laos.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import org.junit.jupiter.api.Test;

public class CalendarUtilTest {

	@Test
	public void afterBeforeEqualByDayTest() {
		Date date1 = CalendarUtil.getDate(2000, 6, 25);
		Date date2 = CalendarUtil.getDate(2000, 6, 27);
		Date date3 = CalendarUtil.getDate(2000, 6, 25);

		assertTrue(CalendarUtil.afterByDay(date2, date1));
		assertTrue(CalendarUtil.beforeByDay(date1, date2));
		assertTrue(CalendarUtil.equalsByDay(date1, date3));
	}

	@Test
	public void getTimestampTest() {
		Date date = new Date();

		assertEquals(new Timestamp(date.getTime()), CalendarUtil.getTimestamp(date));
	}

	@Test
	public void isGregorianDateTest() {
		assertTrue(CalendarUtil.isGregorianDate(11, 31, 2022));
	}

	@Test
	public void isJulianDateTest() {
		assertEquals(1922, CalendarUtil.getAge(CalendarUtil.getDate(100, 5, 25), TimeZone.getTimeZone("UTC")));
	}

	@Test
	public void getAgeTest() {
		assertTrue(CalendarUtil.isJulianDate(11, 31, 2022));
	}

	@Test
	public void isDateTest() {
		assertTrue(CalendarUtil.isDate(11, 31, 2022));
	}

	@Test
	public void isFutureTest() {
		assertTrue(CalendarUtil.isFuture(11, 31, 2122, 23, 12, 12, TimeZone.getTimeZone("UTC"), Locale.getDefault()));

		assertTrue(CalendarUtil.isFuture(11, 31, 2122, TimeZone.getTimeZone("UTC"), Locale.getDefault()));

		assertTrue(CalendarUtil.isFuture(11, 2122, TimeZone.getTimeZone("UTC"), Locale.getDefault()));
	}

	@Test
	public void isAfterTest() {
		assertTrue(CalendarUtil.isAfter(11, 31, 2022, 23, 12, 12, 11, 31, 2022, 23, 11, 11, TimeZone.getTimeZone("UTC"),
				Locale.getDefault()));
	}
}
