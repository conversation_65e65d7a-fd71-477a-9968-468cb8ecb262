package com.mb.laos.util;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import org.junit.jupiter.api.Test;

public class DateUtilTest {

	@Test
	public void equalsTest() {
		assertTrue(DateUtil.equals(new Date(), new Date()));
	}

	@Test
	public void equalIgnoreMilisecondsTest() {
		assertTrue(DateUtil.equals(new Date(), new Date(), true));
	}

	@Test
	public void compareTimeTest() throws ParseException {

		String date1String = "26-09-1989";
		SimpleDateFormat formatter1 = new SimpleDateFormat("dd-MM-yyyy");
		Date date1 = formatter1.parse(date1String);

		String date2String = "26-09-1989";
		SimpleDateFormat formatter2 = new SimpleDateFormat("dd-MM-yyyy");
		Date date2 = formatter2.parse(date2String);

		assertThat(0 == DateUtil.compareTo(date1, date2));

		assertThat(0 == DateUtil.compareTo(null, null));

		assertThat(-1 == DateUtil.compareTo(date1, new Date()));
		assertThat(-1 == DateUtil.compareTo(date1, null));

		assertThat(1 == DateUtil.compareTo(new Date(), date1));

		assertThat(1 == DateUtil.compareTo(null, date1));
	}

	@Test
	public void formatDateDateTest() {
		assertNotNull(DateUtil.formatDateDate(new Date(), "dd-MM-yyyy"));
	}

	@Test
	public void formatDateLongDateTest() {
		assertNotNull(DateUtil.formatDateLongDate(new Date()));
	}

	@Test
	public void formatDateLongTimeTest() {
		assertNotNull(DateUtil.formatDateLongTime(new Date()));
	}

	@Test
	public void formatDateShortDateTest() {
		assertNotNull(DateUtil.formatDateShortDate(new Date()));

		assertNull(DateUtil.formatDateShortDate(null));
	}

	@Test
	public void formatDateStringTest() {
		assertNotNull(DateUtil.formatDateString(new Date()));

		assertEquals(DateUtil.formatDateString(null), null);
	}

	@Test
	public void formatStringLongDateTest() {
		assertNotNull(DateUtil.formatStringLongDate(new Date()));
	}

	@Test
	public void formatStringLongTimeTest() {
		assertNotNull(DateUtil.formatStringLongTime(new Date()));
	}

	@Test
	public void formatStringLongTimestampTest() {
		assertNotNull(DateUtil.formatStringLongTimestamp(new Date()));
	}

	@Test
	public void formatStringShortDateTest() {
		assertNotNull(DateUtil.formatStringShortDate(new Date()));
	}

	@Test
	public void formatStringShortTimestampTest() {
		assertNotNull(DateUtil.formatStringShortTimestamp(new Date()));
	}

	@Test
	public void getCurrentDateTest() {
		String date = DateUtil.getCurrentDate(DateUtil.SHORT_DATE_PATTERN, Locale.ENGLISH);

		assertNotNull(date);
	}

	@Test
	public void getCurrentDateTimezoneTest() {
		String date = DateUtil.getCurrentDate(DateUtil.SHORT_DATE_PATTERN, Locale.ENGLISH, TimeZone.getTimeZone("UTC"));

		assertNotNull(date);
	}

	@Test
	public void getDateAfterTest() {
		Date date = DateUtil.getDateAfter(new Date(), 5);

		Date date1 = DateUtil.getDateAfter(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.DATE, 5);
		
		int currentDate = cal.get(Calendar.DATE);

		assertEquals(currentDate, CalendarUtil.getCalendar(date).get(Calendar.DATE));

		assertEquals(currentDate, CalendarUtil.getCalendar(date1).get(Calendar.DATE));

	}

	@Test
	public void getDateAfterMinuteTest() {
		Date date = DateUtil.getDateAfterMinute(new Date(), 5);

		Date date1 = DateUtil.getDateAfterMinute(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.MINUTE, 5);
		
		int currentMinute = cal.get(Calendar.MINUTE);

		assertEquals(currentMinute, CalendarUtil.getCalendar(date).get(Calendar.MINUTE));

		assertEquals(currentMinute, CalendarUtil.getCalendar(date1).get(Calendar.MINUTE));

	}

	@Test
	public void getDateAfterSecondTest() {
		Date date = DateUtil.getDateAfterSecond(new Date(), 5);

		Date date1 = DateUtil.getDateAfterSecond(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.SECOND, 5);
		
		int currentSecond = cal.get(Calendar.SECOND);

		assertEquals(currentSecond, CalendarUtil.getCalendar(date).get(Calendar.SECOND));

		assertEquals(currentSecond, CalendarUtil.getCalendar(date1).get(Calendar.SECOND));

	}

	@Test
	public void getDateBeforeTest() {
		Date date = DateUtil.getDateBefore(new Date(), 5);

		Date date1 = DateUtil.getDateBefore(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.DATE, -5);
		
		int currentDate = cal.get(Calendar.DATE);

		assertEquals(currentDate, CalendarUtil.getCalendar(date).get(Calendar.DATE));

		assertEquals(currentDate, CalendarUtil.getCalendar(date1).get(Calendar.DATE));

	}

	@Test
	public void getDateBeforeMinuteTest() {
		Date date = DateUtil.getDateBeforeMinute(new Date(), 5);

		Date date1 = DateUtil.getDateBeforeMinute(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.MINUTE, -5);
		
		int currentMinute = cal.get(Calendar.MINUTE);

		assertEquals(currentMinute, CalendarUtil.getCalendar(date).get(Calendar.MINUTE));

		assertEquals(currentMinute, CalendarUtil.getCalendar(date1).get(Calendar.MINUTE));

	}

	@Test
	public void getDateBeforeSecondTest() {
		Date date = DateUtil.getDateBeforeSecond(new Date(), 5);

		Date date1 = DateUtil.getDateBeforeSecond(5);

		Calendar cal = Calendar.getInstance();

		cal.add(Calendar.SECOND, -5);
		
		int currentSecond = cal.get(Calendar.SECOND);

		assertEquals(currentSecond, CalendarUtil.getCalendar(date).get(Calendar.SECOND));

		assertEquals(currentSecond, CalendarUtil.getCalendar(date1).get(Calendar.SECOND));

	}

	@Test
	public void getDaysBetweenTest() {
		Date date = DateUtil.getDateAfter(new Date(), 5);

		assertEquals(5, DateUtil.getDaysBetween(new Date(), date));

	}

	@Test
	public void getDaysBetweenTimezoneTest() {
		Date date = DateUtil.getDateAfter(new Date(), 5);

		assertEquals(5, DateUtil.getDaysBetween(new Date(), date, TimeZone.getTimeZone("UTC")));
	}

}
