package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.messages.LabelKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TelcoCardSearch extends Parameter {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;

    private String name;

    @NotNull(message = LabelKey.ERROR_TELCO_CARD_ID_IS_REQUIRED)
    private Long telcoId;
}
