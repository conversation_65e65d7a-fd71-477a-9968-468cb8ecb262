package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Indexed
@Entity
@Table(name = "EX_TELCO_CARD")
@Data
@EqualsAndHashCode(callSuper = false)
public class TelcoCard extends AbstractAuditingEntity {
    private static final long serialVersionUID = 1257876475317306901L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TELCO_CARD_SEQ")
    @SequenceGenerator(sequenceName = "TELCO_CARD_SEQ", name = "TELCO_CARD_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "TELCO_CARD_ID")
    private long telcoCardId;

    @Column(name = "DENOMINATION")
    private long denomination;

    @Column(name = "DISCOUNT")
    private long discount;

    @Field
    @Column(name = "STATUS")
    private int status;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "TELCO_ID")
    private long telcoId;
}
