package com.mb.laos.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.web.bind.annotation.Mapping;

/**
 * 08/07/2021 - LinhLH: Create new
 *
 * <AUTHOR> <PERSON><PERSON><PERSON> tra bản ghi có phải do user tạo ra không
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Mapping
@Documented
public @interface PermissionWrite {
	PermissionLevel level();
	
	boolean allowAdministrator() default true;
}
