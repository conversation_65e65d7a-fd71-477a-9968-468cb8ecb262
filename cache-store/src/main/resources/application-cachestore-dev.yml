cache-store:
  enabled: true
  type: 'javax.sql.DataSource'
  factory: 'com.zaxxer.hikari.HikariJNDIFactory'
  url: '***************************************'
  driver-class-name: 'oracle.jdbc.OracleDriver'
  data-source-class-name: 'oracle.jdbc.pool.OracleDataSource'
  username: mb_laos_store_dev
  password: '123456'
  minimumIdle: 5
  maximumPoolSize: 10
  connectionTimeout: 300000
  implicit-caching-enabled: true
  jndiName: jdbc/CacheStoreDatasource
  change-log: classpath:/db-store/changelog-master.xml
spring:
  jpa:
    properties:
      #hibernate.search.default.indexmanager: org.infinispan.query.indexmanager.InfinispanIndexManager
      hibernate.search.default.exclusive_index_use: true
      hibernate.search.default.reader.strategy: shared
      hibernate.search.default.infinispan.cachemanager_jndiname: java:comp/env/jdbc/CacheStoreDatasource
      hibernate.search.default.infinispan.configuration_resourcename: hibernatesearch-infinispan.xml

