/*
 * ActuatorController.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved. This software is the confidential and proprietary information of
 * Evotek
 */
package com.mb.laos.api.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 02/11/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/public/actuator")
public class ActuatorController {
    
    @GetMapping("/health")
    public ResponseEntity<Boolean> health() {
        return ResponseEntity.ok().build();
    }
}
