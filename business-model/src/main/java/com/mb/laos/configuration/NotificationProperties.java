package com.mb.laos.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "notification")
public class NotificationProperties {
    private String entryPrefix;
    private String closurePrefix;
    private String smsBalanceMsg;
    private String salaryMsg;
    private TransactionType transactionType;

    @Getter
    @Setter
    public static class TransactionType {
        private String interBank;
        private String cashInCounter;
        private String internalBank;
    }
}
