package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.Customer;
import com.mb.laos.model.Referral;
import com.mb.laos.model.Referral;
import com.mb.laos.model.search.ReferralSearch;
import com.mb.laos.repository.extend.ReferralRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.apache.commons.collections4.ListUtils;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReferralRepositoryImpl implements ReferralRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Referral> search(ReferralSearch referralSearch, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e from Referral e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(referralSearch, values));
        sql.append(QueryUtil.createOrderQuery(Referral.class, referralSearch.getOrderByType(),
                referralSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Referral.class);

        values.forEach(query::setParameter);

        if (referralSearch.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            }
        }

        return query.getResultList();
    }

    @Override
    public Long count(ReferralSearch referralSearch) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM Referral e ");

        sql.append(createWhereQuery(referralSearch, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(ReferralSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.userFullName LIKE :keyword OR e.userCode LIKE :keyword OR e.referralCode LIKE :keyword ");
            sql.append(" OR e.phoneNumber LIKE :keyword OR e.rmCode LIKE :keyword) ");

            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
        }

        if (Validator.isNotNull(params.getTypes())) {
            sql.append(" AND e.type IN :type ");

            values.put("type", params.getTypes());
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }

    @Override
    public List<Referral> findAllByReferralCodeInAndStatus(List<String> referralCodes, Integer status) {
        String sql = "SELECT r FROM Referral r where r.referralCode in :referralCodes and r.status = :status";
        List<Referral> result = new ArrayList<>();
        //tach ra vi oracle chi cho phep find in toi da 1000 phan tu
        List<List<String>> referrerCodeSubList = ListUtils.partition(referralCodes, 900);
        for (List<String> ids : referrerCodeSubList) {
            Query query = entityManager.createQuery(sql.toString(), Referral.class);
            Map<String, Object> values = new HashMap<>();
            values.put("referralCodes", ids);
            values.put("status", status);
            values.forEach(query::setParameter);
            result.addAll(query.getResultList());
        }
        return result;
    }

}
