package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.ReceiveTransferMoney;
import com.mb.laos.model.search.ReceiveTransferMoneySearch;
import com.mb.laos.repository.extend.ReceiveTransferMoneyRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReceiveTransferMoneyRepositoryImpl implements ReceiveTransferMoneyRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<ReceiveTransferMoney> search(ReceiveTransferMoneySearch search) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e from ReceiveTransferMoney e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(ReceiveTransferMoney.class, search.getOrderByType(),
                search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), ReceiveTransferMoney.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    private String createWhereQuery(ReceiveTransferMoneySearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.transactionId LIKE :keyword) ");

            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
        }

        if (Validator.isNotNull(params.getFromDate())) {
            sql.append(" AND e.transactionFinishTime >= :fromDate ");

            values.put("fromDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getStartOfDay(params.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getToDate())) {
            sql.append(" AND e.transactionFinishTime <= :toDate ");

            values.put("toDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getEndOfDay(params.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getMerchantId())) {
            sql.append(" AND e.merchantId = :merchantId ");

            values.put("merchantId", params.getMerchantId());
        }

        if (Validator.isNotNull(params.getCustomerAccountNumber())) {
            sql.append(" AND e.customerAccountNumber = :customerAccountNumber ");

            values.put("customerAccountNumber", params.getCustomerAccountNumber());
        }

        return sql.toString();
    }
}
