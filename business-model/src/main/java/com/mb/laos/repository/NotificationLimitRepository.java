
package com.mb.laos.repository;

import com.mb.laos.model.NotificationLimit;
import com.mb.laos.repository.extend.NotificationLimitRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface NotificationLimitRepository extends JpaRepository<NotificationLimit, Long>, NotificationLimitRepositoryExtend {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    default NotificationLimit saveAnyway(NotificationLimit entity){
        return save(entity);
    }

    List<NotificationLimit> findAllByTransactionId(String transactionId);
}
