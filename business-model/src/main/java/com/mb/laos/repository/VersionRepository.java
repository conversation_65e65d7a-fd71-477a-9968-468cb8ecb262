package com.mb.laos.repository;

import com.mb.laos.enums.OperatingSystemType;
import com.mb.laos.model.Version;
import com.mb.laos.model.dto.VersionDTO;
import com.mb.laos.repository.extend.VersionRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface VersionRepository extends JpaRepository<Version, VersionDTO>, VersionRepositoryExtend {

    Optional<Version> findByVersionId(Long versionId);

    Optional<Version> findByNameAndCodeAndOperatingSystem(String versionName, Long versionCode, OperatingSystemType type);

    @Modifying
    @Query("UPDATE Version v SET v.status = :activeStatus WHERE v.status = :inactiveStatus AND v.releaseDate <= :time")
    void updateVersionStatus(int activeStatus, int inactiveStatus, Instant time);

    Optional<Version> findTopByOperatingSystemAndStatusOrderByReleaseDateDesc(OperatingSystemType type, int activeStatus);

    List<Version> findByOperatingSystemAndStatus(OperatingSystemType type, int activeStatus);
}
