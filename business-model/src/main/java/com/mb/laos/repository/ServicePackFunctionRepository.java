
package com.mb.laos.repository;

import com.mb.laos.model.ServicePackFunction;
import com.mb.laos.repository.extend.ServicePackFunctionRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServicePackFunctionRepository extends JpaRepository<ServicePackFunction, Long>, ServicePackFunctionRepositoryExtend {

    List<ServicePackFunction> findAllByServicePackIdAndStatusNot(long servicePackId, int status);

    List<ServicePackFunction> findAllByServicePackIdInAndStatusNot(List<Long> servicePackIds, int status);

}
