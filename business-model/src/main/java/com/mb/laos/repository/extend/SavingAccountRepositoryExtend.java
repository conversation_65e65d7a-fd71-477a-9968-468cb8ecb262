package com.mb.laos.repository.extend;

import com.mb.laos.model.dto.SavingAccountDTO;
import com.mb.laos.model.search.SavingAccountSearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SavingAccountRepositoryExtend {

    /**
     * Search
     *
     * @param savingAccountSearch SavingAccountSearch
     * @param pageable       Pageable
     * @return List<SavingAccount>
     */
    List<SavingAccountDTO> search(SavingAccountSearch savingAccountSearch, Pageable pageable);

    /**
     * Count
     *
     * @param savingAccountSearch SavingAccountSearch
     * @return Long
     */
    Long count(SavingAccountSearch savingAccountSearch);

    List<SavingAccountDTO> search(SavingAccountSearch savingAccountSearch);
}
