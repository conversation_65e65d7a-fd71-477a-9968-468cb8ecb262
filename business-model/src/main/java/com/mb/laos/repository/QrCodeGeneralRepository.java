package com.mb.laos.repository;

import com.mb.laos.model.QrCodeGeneral;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface QrCodeGeneralRepository extends JpaRepository<QrCodeGeneral, Long> {
    Optional<QrCodeGeneral> findByQrCodeValueAndStatus(String qrCodeValue, int status);

    Optional<QrCodeGeneral> findByQrCodeGeneralIdAndStatus(Long qrCodeId, int status);

    Optional<QrCodeGeneral> findByQrCodeGeneralIdAndStatusNot(Long qrCodeId, int status);

    boolean existsByDomainIdAndStatusNot(Long domainId, int status);

    Optional<QrCodeGeneral> findByDomainIdAndStatus(Long domainId, Integer status);

    Optional<QrCodeGeneral> findByDomainIdAndStatusNot(Long domainId, Integer status);
}
