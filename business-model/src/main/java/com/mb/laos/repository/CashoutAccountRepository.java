package com.mb.laos.repository;

import com.mb.laos.model.CashoutAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CashoutAccountRepository extends JpaRepository<CashoutAccount, Long> {
    Optional<CashoutAccount> findFirstByStatusIs(int status);

    List<CashoutAccount> findAllByStatusIsNotOrderByIdDesc(int status);

    Optional<CashoutAccount> findByIdAndStatusIsNot(long id, int status);

    Optional<CashoutAccount> findByAccountNumberAndStatusNot(String accountNumber, int status);

    @Modifying
    @Query(value = "update cashout_account set AMOUNT_TRANSFERRED = AMOUNT_TRANSFERRED + :amount where id = :id", nativeQuery = true)
    void addTransferedAmount(long amount, long id);
}
