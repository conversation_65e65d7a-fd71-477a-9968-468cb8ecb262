package com.mb.laos.repository.impl;

import com.mb.laos.messages.Labels;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.Referral;
import com.mb.laos.model.dto.PremiumAccNumberDTO;
import com.mb.laos.model.search.PremiumAccNumberSearch;
import com.mb.laos.repository.extend.PremiumAccNumberRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PremiumAccNumberRepositoryImpl implements PremiumAccNumberRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;


    @Override
    public List<PremiumAccNumberDTO> search(PremiumAccNumberSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT new com.mb.laos.model.dto.PremiumAccNumberDTO(e.premiumAccNumberId, " +
                "e.premiumAccNumberStructureId, e.premiumAccNumber, e.discount, e.originalPrice, e.totalPrice, " +
                "e.referralId, e.customerId, e.status, e.createdDate, c.cif, c.fullname, c.phoneNumber, r.rmCode, pr.numberStructure, r.phoneNumber) " +
                "FROM PremiumAccNumber e " +
                "inner join Customer c on c.customerId = e.customerId " +
                "left join Referral r on r.referralId = e.referralId " +
                "left join PremiumAccountNumberStructure pr on pr.premiumAccountNumberStructureId = e.premiumAccNumberStructureId");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(PremiumAccNumberDTO.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), PremiumAccNumberDTO.class);

        values.forEach(query::setParameter);

        if (search.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        return query.getResultList();
    }

    @Override
    public Long count(PremiumAccNumberSearch search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM PremiumAccNumber e " +
                "inner join Customer c on c.customerId = e.customerId " +
                "left join Referral r on r.referralId = e.referralId ");

        sql.append(createWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(PremiumAccNumberSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE 1 = 1 AND e.status != -1 ");

        if (Validator.isNotNull(params.getReferralIds())) {
            sql.append(" AND e.referralId IN :referralId ");

            values.put("referralId", params.getReferralIds());
        }

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.premiumAccNumber LIKE :keyword OR c.fullname LIKE :keyword");
            sql.append(" OR lower(c.cif) = lower(:keywordEncrypt)");
            sql.append(" OR lower(c.phoneNumber) = lower(:keywordEncrypt)) ");

            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
            values.put("keywordEncrypt", params.getEncryptedKeyword());
        }

        if (Validator.isNotNull(params.getStartDate())) {

            sql.append(" AND e.createdDate >= :startDate ");

            values.put("startDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getStartOfDay(params.getStartDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getEndDate())) {

            sql.append(" AND e.createdDate <= :endDate ");

            values.put("endDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getEndOfDay(params.getEndDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
