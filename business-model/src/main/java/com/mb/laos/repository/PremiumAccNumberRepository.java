package com.mb.laos.repository;

import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.PremiumAccNumberCacheDTO;
import com.mb.laos.repository.extend.PremiumAccNumberRepositoryExtend;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PremiumAccNumberRepository extends JpaRepository<PremiumAccNumber, Long>, PremiumAccNumberRepositoryExtend {

    PremiumAccNumber findByPremiumAccNumberIdAndStatus(Long premiumAccNumberId, int status);

    boolean existsByPremiumAccNumberAndStatusNot(String premiumAccountNumber, int status);

    List<PremiumAccNumber> findAllByPremiumAccNumberInAndStatusNot(List<String> premiumAccNumbers, int status);

    List<PremiumAccNumber> findAllByPremiumAccNumberInAndStatus(List<String> premiumAccNumbers, int status);

    List<PremiumAccNumber> findAllByStatus(int status);

    Optional<PremiumAccNumber> findByPremiumAccNumberAndStatusNot(String premiumAccNumber, int status);

    PremiumAccNumber findByPremiumAccNumberAndStatus(String premiumAccNumber, int status);

    PremiumAccNumber findByCustomerIdAndStatus(Long customer, int status);

    List<PremiumAccNumber> findAllByCustomerIdAndStatus(Long customer, int status);

    PremiumAccNumber findByCustomerIdAndStatusNotIn(Long customerId, List<Integer> status);

    List<PremiumAccNumber> findAllByCustomerIdAndStatusNotIn(Long customerId, List<Integer> status);

    PremiumAccNumber findByPremiumAccNumberAndCustomerIdAndStatus(String premiumAccNumber, Long customer, int status);

    List<PremiumAccNumber> findAllByPremiumAccNumberAndStatusNot(String premiumAccNumber, int status);

    List<PremiumAccNumber> findAllByPremiumAccNumberIn(List<String> premiumAccNumber);

    @CachePut(cacheNames = {BusinessCacheConstants.PremiumAccNumber.OTP_PREMIUM_ACC_NUMBER}, key = "#key", unless = "#result == null")
    default PremiumAccNumberCacheDTO put(String key, PremiumAccNumberCacheDTO value) {
        return value;
    }

    @Cacheable(cacheNames = BusinessCacheConstants.PremiumAccNumber.OTP_PREMIUM_ACC_NUMBER, key = "#key", unless = "#result == null")
    default PremiumAccNumberCacheDTO get(String key) {
        return null;
    }

    @CacheEvict(cacheNames = BusinessCacheConstants.PremiumAccNumber.OTP_PREMIUM_ACC_NUMBER, key = "#key")
    default String evict(String key) {
        return key;
    }
}
