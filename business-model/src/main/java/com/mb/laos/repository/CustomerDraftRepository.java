package com.mb.laos.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.mb.laos.model.CustomerDraft;
import com.mb.laos.repository.extend.CustomerDraftRepositoryExtend;

import java.util.Optional;

@Repository
public interface CustomerDraftRepository extends JpaRepository<CustomerDraft, Long>, CustomerDraftRepositoryExtend {
    
    CustomerDraft findByTransactionId(String transactionId);

    /**
     * findByIdCardNumberAndPhoneNumberAndStatus
     *
     * @param idCardNumber String
     * @param phoneNumber String
     * @return Optional<CustomerDraft>
     */
    Optional<CustomerDraft> findTopByIdCardNumberAndPhoneNumberOrderByLastModifiedByDesc(String idCardNumber, String phoneNumber);

    /**
     * findByCustomerDraftIdAndStatus
     *
     * @param customerDraftId Long
     * @param status Integer
     * @return Optional<CustomerDraft>
     */
    Optional<CustomerDraft> findByCustomerDraftIdAndStatus(Long customerDraftId, Integer status);

    /**
     * findByReferenceIdAndStatus
     *
     * @param referenceId Long
     * @param status Integer
     * @return Optional<CustomerDraft>
     */
    Optional<CustomerDraft> findByReferenceIdAndStatus(Long referenceId, Integer status);
}
