package com.mb.laos.repository;

import com.mb.laos.cache.util.CacheConstants;
import com.mb.laos.model.Common;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface CommonRepository extends JpaRepository<Common, Long> {
    @Cacheable(cacheNames = CacheConstants.Others.COMMON_CATEGORY, key = "#p0", unless = "#result.isEmpty()")
    List<Common> findAllByCategory(String category);

    List<Common> findAllByCategoryAndStatus(String category, Integer status);

    @Cacheable(cacheNames = CacheConstants.Others.INTERNATIONAL_PAYMENT, key = "#code", unless = "#result == null")
    Optional<Common> findByCategoryAndCodeAndStatus(String category, String code, Integer status);
}
