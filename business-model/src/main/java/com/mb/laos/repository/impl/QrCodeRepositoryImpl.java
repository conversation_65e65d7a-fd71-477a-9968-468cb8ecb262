package com.mb.laos.repository.impl;

import com.mb.laos.model.QrCode;
import com.mb.laos.model.search.QrSearch;
import com.mb.laos.repository.extend.QrCodeRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QrCodeRepositoryImpl implements QrCodeRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<QrCode> search(QrSearch qrSearch, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM QrCode e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(qrSearch, values));
        sql.append(QueryUtil.createOrderQuery(QrCode.class, qrSearch.getOrderByType(), qrSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), QrCode.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public Long count(QrSearch qrSearch) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM QrCode e ");

        sql.append(createWhereQuery(qrSearch, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(QrSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE 1 = 1 AND e.status != -1 ");

        if (Validator.isNotNull(params.getCustomerId())) {
            sql.append(" AND e.customerId LIKE :customerId ");

            values.put("customerId", params.getCustomerId());
        }

        if (Validator.isNotNull(params.getType())) {
            sql.append(" AND e.type LIKE :type ");

            values.put("type", params.getType());
        }

        if (Validator.isNotNull(params.getQrType())) {
            sql.append(" AND e.qrType LIKE :qrType ");

            values.put("qrType", params.getQrType());
        }

        if (Validator.isNotNull(params.getAccountNumber())) {
            sql.append(" AND e.accountNumber = :accountNumber ");

            values.put("accountNumber", params.getAccountNumber());
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
