/**
 *
 */
package com.mb.laos.cache.util;

/**
 * <AUTHOR>
 *
 */
public interface BusinessCacheConstants extends CacheConstants {

    interface Customer {
        public static final String FIND_BY_EMAIL = "customer_findByEmail";

        public static final String FIND_BY_ID = "customer_findById";

        public static final String FIND_BY_PHONE_NUMBER = "customer_findByPhoneNumber";

        public static final String FIND_BY_USERNAME = "customer_findByUsername";

        public static final String DRAFT_PASSWORD = "customer-draft-password";

        public static final String VERIFY_PASSWORD_FAILED = "customer-verify-password-failed";
    }

    interface ContentTemplate {
        public static final String FIND_BY_TEMPLATE_CODE = "contentTemplate_findByTemplateCode";
    }

    interface IdCardType {
        public static final String FIND_ALL = "idCardType_findAll";

        public static final String FIND_BY_ID = "idCardType_findById";

        public static final String FIND_BY_ID_AND_STATUS = "idCardType_findByIdAndStaus";

        public static final String FIND_BY_CODE = "idCardType_findByCode";
    }

    interface LoanProduct {
        public static final String FIND_ALL = "loanProduct_findAll";
        
        public static final String FIND_BY_ID = "loanProduct_findById";
        
        public static final String EXITED_BY_ID_AND_STATUS = "loanProduct_existedByIdAndStatus";
    }

    interface LoanOnline {
        public static final String FIND_BY_ID = "loanOnline_findById";
    }

    interface Sector {
        public static final String FIND_BY_ID = "sector_findById";

        public static final String EXIST_BY_ID = "sector_existById";
    }

    interface Bank {
        public static final String FIND_BY_ID = "bank_findById";
        
        public static final String FIND_BY_ID_AND_STATUS_NOT = "bank_findByIdAndStatusNot";
        
        public static final String FIND_BY_CODE = "bank_findByCode";
        
        public static final String FIND_BY_BANK_NUMBER_CODE = "bank_findByBankCodeNumber";

        public static final String FIND_BY_CODE_AND_STATUS_NOT = "bank_findByCodeAndStatusNot";

        public static final String FIND_BY_BANK_NUMBER_CODE_AND_STATUS_NOT = "bank_findByBankCodeNumberAndStatusNot";
    }

    interface Campaign {
        public static final String FIND_BY_ID = "campaign_findById";
        
        public static final String FIND_BY_STATUS = "campaign_findByStatus";

        public static final String FIND_BY_POSITION_AND_STATUS = "campaign_findByPositionAndStatus";
    }
    
    interface Position {
        public static final String FIND_BY_ID = "position_findById";
        
        public static final String FIND_BY_ID_AND_STATUS = "position_findByIdAndStatus";
        
        public static final String FIND_BY_CODE = "position_findByCode";
    }
    
    interface Merchant {
        public static final String FIND_BY_CODE_AND_STATUS = "merchant_find_by_codeandstatus";
        
        public static final String FIND_BY_CHILD_CODE_AND_STATUS = "merchant_find_by_childcodeandstatus";
        
        public static final String FIND_BY_ID_AND_STATUS_NOT = "merchant_find_by_idandstatusnot";
        
        public static final String FIND_BY_ID_AND_STATUS = "merchant_find_by_idandstatus";
    }
    
    interface TransferTransaction {
        public static final String FIND_BY_TRANSACTION_ID_AND_CUSTOMER_ID = "transfertrans_find_by_transidandcusid";
    }

    public interface CustomerLoginFailed {
        public static final String FIND_BY_CUSTOMER_ID = "customerLoginFailed_findByCustomerId";
    }

    public interface Structure {
        public static final String FIND_BY_LENGTH = "structure_findByLength";
    }

    public interface Notification {
        public static final String NOTIFICATION_TRANSFER = "notification_transfer";
    }

    public interface PremiumAccNumber {
        public static final String OTP_PREMIUM_ACC_NUMBER = "otp-premium-acc-number";
    }
}
