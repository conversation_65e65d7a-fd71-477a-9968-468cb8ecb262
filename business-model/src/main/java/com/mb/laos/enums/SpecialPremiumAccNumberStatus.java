package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SpecialPremiumAccNumberStatus {
    ACTIVE(1),
    INACTIVE(0),
    // trạng thái đang treo
    PENDING(2),
    // trạng thái thu hồi
    REVERT(3),
    SOLD(4);
    private int value;

    /**
     * Dùng làm input cho annotation ValueOfEnum
     *
     * @return List<Integer>
     */
    public static List<Integer> getValues() {
        return Stream.of(values()).map(e -> e.value).collect(Collectors.toList());
    }

    public static SpecialPremiumAccNumberStatus valueOfId(int id) {
        for (SpecialPremiumAccNumberStatus s : values()) {
            if (s.getValue() == id) {
                return s;
            }
        }

        return null;
    }
}
