package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum TransferTransactionType {
    QR_CODE_LAPNET_TRANSFER,
    QR_CODE_LAPNET_MERCHANT,
    QR_CODE_INTERNATIONAL_MERCHANT,
    QR_CODE_INTERNAL,
    QR_CODE_INTERNAL_MERCHANT,
    INTER_BANK,
    INTERNAL_BANK,
    CASH_OUT,
    CASH_IN,
    FTTH,
    LEAD_LINE,
    ETL,
    UNITEL,
    LAO_TELECOM;

    public static List<TransferTransactionType> getQrTransaction() {
        List<TransferTransactionType> result = new ArrayList<>();
        result.add(QR_CODE_LAPNET_TRANSFER);
        result.add(QR_CODE_INTERNAL);
        result.add(QR_CODE_LAPNET_MERCHANT);
        result.add(QR_CODE_INTERNAL_MERCHANT);
        result.add(QR_CODE_INTERNATIONAL_MERCHANT);
        return result;
    }

    public static List<TransferTransactionType> getQrLapnetTransaction() {
        List<TransferTransactionType> result = new ArrayList();
        result.add(QR_CODE_LAPNET_TRANSFER);
        result.add(QR_CODE_LAPNET_MERCHANT);
        result.add(QR_CODE_INTERNATIONAL_MERCHANT);
        return result;
    }

    public static List<TransferTransactionType> getTransferLapnet() {
        List<TransferTransactionType> result = new ArrayList();
        result.add(QR_CODE_LAPNET_TRANSFER);
        result.add(QR_CODE_LAPNET_MERCHANT);
        result.add(INTER_BANK);
        result.add(CASH_OUT);
        return result;
    }


    public static List<TransferTransactionType> getInterBank() {
        List<TransferTransactionType> result = new ArrayList();
        result.add(QR_CODE_LAPNET_TRANSFER);
        result.add(INTER_BANK);
        result.add(QR_CODE_LAPNET_MERCHANT);
        return result;
    }

    public static List<TransferTransactionType> getInternalBank() {
        List<TransferTransactionType> result = new ArrayList();
        result.add(QR_CODE_INTERNAL);
        result.add(INTERNAL_BANK);
        result.add(QR_CODE_INTERNAL_MERCHANT);
        return result;
    }

    public static List<TransferTransactionType> getTransfer() {
        List<TransferTransactionType> result = new ArrayList();
        result.add(QR_CODE_INTERNAL);
        result.add(INTERNAL_BANK);
        result.add(QR_CODE_INTERNAL_MERCHANT);
        result.add(QR_CODE_LAPNET_TRANSFER);
        result.add(INTER_BANK);
        result.add(QR_CODE_LAPNET_MERCHANT);
        return result;
    }
}
