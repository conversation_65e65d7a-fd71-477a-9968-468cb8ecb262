/**
 *
 */
package com.mb.laos.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.mb.laos.messages.Labels;
import com.mb.laos.model.IdCardType;
import com.mb.laos.model.dto.IdCardTypeDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.Validator;

/**
 * <AUTHOR>
 *
 */
@Mapper(componentModel = "spring")
public interface IdCardTypeMapper extends EntityMapper<IdCardTypeDTO, IdCardType>{
	@Override
	IdCardType toEntity(IdCardTypeDTO dto);

	@Override
	@Mapping(source = "name", target = "name", qualifiedByName = "toIdCardName")
	IdCardTypeDTO toDto(IdCardType entity);
	
	@Named("toIdCardName")
	default String toIdCardName(String langKey) {
	    return Validator.isNotNull(lang<PERSON><PERSON>) ? Labels.getLabels(langKey) : StringPool.BLANK;
	}
}
