package com.mb.laos.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.mb.laos.model.Customer;
import com.mb.laos.model.CustomerDraft;
import com.mb.laos.request.CustomerRegistrationRequest;

@Mapper(componentModel = "spring")
public interface CustomerDraftMapper extends EntityMapper<CustomerRegistrationRequest, CustomerDraft>{
    @Override
    CustomerDraft toEntity(CustomerRegistrationRequest dto);

	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdDate", ignore = true)
	@Mapping(target = "lastModifiedBy", ignore = true)
	@Mapping(target = "lastModifiedDate", ignore = true)
	@Mapping(target = "status", ignore = true)
    Customer toCustomer(CustomerDraft draft);
}
