package com.mb.laos.service.mapper;


import com.mb.laos.model.ReferralHistory;
import com.mb.laos.model.dto.ReferralHistoryDTO;
import com.mb.laos.util.Validator;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;


@Mapper(componentModel = "spring")
public interface ReferralHistoryMapper extends EntityMapper<ReferralHistoryDTO, ReferralHistory> {

    @Override
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "lastModifiedDate", ignore = true)
    ReferralHistory toEntity(ReferralHistoryDTO referralHistoryDTO);

    @Override
    @Mapping(source = "createdDate", target = "createdDate", qualifiedByName = "toLocalDate")
    @Mapping(source = "lastModifiedDate", target = "lastModifiedDate", qualifiedByName = "toLocalDate")
    ReferralHistoryDTO toDto(ReferralHistory referralHistory);

    @Named("toLocalDate")
    default LocalDateTime toLocalDate(Instant instant) {
        return Validator.isNotNull(instant) ? LocalDateTime.ofInstant(instant, ZoneOffset.UTC) : null;
    }
}
