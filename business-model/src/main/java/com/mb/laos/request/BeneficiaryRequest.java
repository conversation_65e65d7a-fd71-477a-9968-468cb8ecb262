package com.mb.laos.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class BeneficiaryRequest extends Request {
    private static final long serialVersionUID = -8340388451714375195L;

    @NotNull(message = LabelKey.ERROR_BENEFICIARY_IS_REQUIRED)
    private Long beneficiaryId;

    private String reminiscentName;
}
