package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.SaveStatus;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeneficiarySearch extends Parameter {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 859603045286636102L;

    private Long customerId;

    private String beneficiaryCustomerName;

    private String beneficiaryCustomerAccount;

    private String reminiscentName;

    private TransactionStatus transactionStatus;

    private TransferType transferType;

    private SaveStatus isSaved;

    private TransferTransactionType type;
}
