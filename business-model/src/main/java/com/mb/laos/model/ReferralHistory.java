package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "REFERRAL_HISTORY")
@Data
@EqualsAndHashCode(callSuper = false)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class ReferralHistory extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7020271006196714571L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "REFERRAL_HISTORY_SEQ")
    @SequenceGenerator(sequenceName = "REFERRAL_HISTORY_SEQ", name = "REFERRAL_HISTORY_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "REFERRAL_HISTORY_ID")
    private long referralHistoryId;

    @Column(name = "REFERRAL_ID")
    private Long referralId;

    @Column(name = "TARGET_USER_ID")
    private Long targetUserId;

    @Column(name = "TARGET_USER_FULL_NAME")
    private String targetUserFullName;

    @Column(name = "TARGET_USER_PHONE_NUMBER")
    private String targetUserPhoneNumber;

    @Column(name = "STATUS")
    private Integer status;
}
