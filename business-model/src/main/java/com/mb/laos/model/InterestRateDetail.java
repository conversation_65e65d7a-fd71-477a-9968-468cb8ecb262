package com.mb.laos.model;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "INTEREST_RATE_DETAIL")
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InterestRateDetail extends AbstractAuditingEntity {

    private static final long serialVersionUID = -7906586163519009380L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INTEREST_RATE_DETAIL_SEQ")
    @SequenceGenerator(sequenceName = "INTEREST_RATE_DETAIL_SEQ", name = "INTEREST_RATE_DETAIL_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "INTEREST_RATE_DETAIL_ID")
    private long interestRateDetailId;

    @Column(name = "INTEREST_RATE_ID")
    private long interestRateId;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "LANGUAGE")
    private String language;
}
