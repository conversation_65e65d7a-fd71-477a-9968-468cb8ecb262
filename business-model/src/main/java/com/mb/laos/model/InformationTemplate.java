package com.mb.laos.model;

import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.search.annotations.Analyze;
import org.hibernate.search.annotations.Analyzer;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Index;
import org.hibernate.search.annotations.Indexed;
import org.hibernate.search.annotations.Normalizer;
import org.hibernate.search.annotations.SortableField;
import org.hibernate.search.annotations.Store;
import org.hibernate.search.annotations.TermVector;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "INFORMATION_TEMPLATE")
@Indexed
public class InformationTemplate extends AbstractAuditingEntity {
    
    private static final long serialVersionUID = -1722612555689173159L;

    @Id
    @Column(name = "INFORMATION_TEMPLATE_ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INFORMATION_TEMPLATE_SEQ")
    @SequenceGenerator(name = "INFORMATION_TEMPLATE_SEQ", sequenceName = "INFORMATION_TEMPLATE_SEQ", allocationSize = 1,
                    initialValue = 1)
    private long informationTemplateId;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
	@Field(name = SortName.INFORMATION_TEMPLATE_CODE, termVector = TermVector.YES, index = Index.YES, 
			analyze = Analyze.NO, normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE), 
			store = Store.YES)
    @SortableField(forField = SortName.INFORMATION_TEMPLATE_CODE)
    @Column(name = "INFORMATION_TEMPLATE_CODE", length = 75)
    private String informationTemplateCode;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
	@Field(name = SortName.IMFORMATION_TEMPLATE_NAME_SORT, termVector = TermVector.YES, index = Index.YES, 
			analyze = Analyze.NO, normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE), 
			store = Store.YES)
    @SortableField(forField = SortName.IMFORMATION_TEMPLATE_NAME_SORT)
    @Column(name = "IMFORMATION_TEMPLATE_NAME", length = 255)
    private String informationTemplateName;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
	@Field(name = SortName.DISPLAY_NAME_SORT, termVector = TermVector.YES, index = Index.YES, 
			analyze = Analyze.NO, normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE), 
			store = Store.YES)
    @SortableField(forField = SortName.DISPLAY_NAME_SORT)
    @Column(name = "DISPLAY_NAME", length = 75)
    private String displayName;
    
    @Lob
    @Column(name = "TEMPLATE", length = 999999999)
    private String template;
    
    @Field
    @Column(name = "STATUS")
    private int status;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, store = Store.YES)
	@Field(name = "lastModifiedDate", index = Index.YES, analyze = Analyze.NO, store = Store.YES)
    @SortableField(forField = "lastModifiedDate")
    @Column(name = "LAST_MODIFIED_DATE", updatable = false, insertable = false)
    private LocalDateTime lastModifiedDateTime;
    
    public interface FieldName {
        String STATUS = "status";
        
        String IMFORMATION_TEMPLATE_NAME = "informationTemplateName";
        
        String DISPLAY_NAME = "displayName";
        
        String INFORMATION_TEMPLATE_CODE = "informationTemplateCode";
    }
    
    public interface SortName {
        String IMFORMATION_TEMPLATE_NAME_SORT = "informationTemplateNameSort";

        String DISPLAY_NAME_SORT = "displayNameSort";
        
        String INFORMATION_TEMPLATE_CODE = "informationTemplateCodeSort";
    }
}
