package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.TransactionFeeTypeCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionFeeTypeDTO implements Serializable {
    private static final long serialVersionUID = 2361109832412055518L;

    private long transactionFeeTypeId;

    private String transactionFeeTypeName;

    private String transactionFeeTypeCode;

    private String description;

    private int status;
}
