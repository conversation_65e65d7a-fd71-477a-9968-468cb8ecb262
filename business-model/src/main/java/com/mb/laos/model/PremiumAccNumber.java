package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 17/02/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */

@Getter
@Setter
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "PREMIUM_ACCOUNT_NUMBER")
public class PremiumAccNumber extends AbstractAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1055474784403867165L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PREMIUM_ACCOUNT_NUMBER_SEQ")
    @SequenceGenerator(sequenceName = "PREMIUM_ACCOUNT_NUMBER_SEQ", name = "PREMIUM_ACCOUNT_NUMBER_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "PREMIUM_ACCOUNT_NUMBER_ID")
    private long premiumAccNumberId;

    @Column(name = "PREMIUM_ACCOUNT_NUMBER")
    private String premiumAccNumber;

    @Column(name = "DISCOUNT")
    private double discount;

    @Column(name = "ORIGINAL_PRICE")
    private Long originalPrice;

    @Column(name = "TOTAL_PRICE")
    private Long totalPrice;

    @Column(name = "REFERRAL_ID")
    private Long referralId;

    @Column(name = "REFERRAL_CODE")
    private String referralCode;

    @Column(name = "CUSTOMER_ID")
    private long customerId;

    @Column(name = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_ID")
    private Long premiumAccNumberStructureId;

    @Column(name = "PAYMENT_DATE")
    private Instant paymentDate;

    @Column(name = "PAYMENT_DUE_DATE", columnDefinition = "TIMESTAMP")
    private Instant paymentDueDate;

    @Column(name = "TRANSACTION_ID")
    private Long transactionId;

    @Column(name = "STATUS")
    private int status;
}
