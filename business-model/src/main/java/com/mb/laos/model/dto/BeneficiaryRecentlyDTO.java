package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.util.Validator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeneficiaryRecentlyDTO implements Serializable {
    private static final long serialVersionUID = 6742918453629834517L;

    @Exclude
    private Long customerId;

    private String beneficiaryCustomerName;

    @Exclude
    private String customerAccNumber;

    @Exclude
    private String beneficiaryAccountNumber;

    @Exclude
    private String target;

    @Exclude
    private String qrValue;

    private String beneficiaryBankCode;

    private String beneficiaryBankName;

    private Long beneficiaryBankId;

    private Integer status;

    private TransferTransactionType type;

    private String description;

    private DocumentDTO icon;

    private String beneficiaryCurrency;

    private String nation;

    private TransferType transferType;

    private Long transferTransactionId;

    private int isSaved;

    public BeneficiaryRecentlyDTO(TransferType transferType, Long customerId, String target, String customerAccNumber,
                                  String qrValue, String transactionCurrency, String beneficiaryCustomerName,
                                  String beneficiaryAccountNumber, TransferTransactionType type,
                                  Long bankId, String bankName, String bankCode, String nation,
                                  String beneficiaryCurrency) {
        this.transferType = transferType;
        this.customerId = customerId;
        this.customerAccNumber = customerAccNumber;
        this.beneficiaryCustomerName = beneficiaryCustomerName;
        this.beneficiaryAccountNumber = beneficiaryAccountNumber;
        this.target = target;
        this.qrValue = qrValue;
        this.beneficiaryBankId = bankId;
        this.beneficiaryBankName = bankName;
        this.type = type;
        this.beneficiaryBankCode = bankCode;
        this.nation = nation;
        this.beneficiaryCurrency = Validator.isNull(beneficiaryCurrency) ? transactionCurrency : beneficiaryCurrency;
    }
}
