package com.mb.laos.model;

import com.mb.laos.enums.ConfigurationFeeType;
import com.mb.laos.enums.NotificationHistoryEnum;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "T_NOTIFICATION_HISTORY")
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class NotificationHistory extends AbstractAuditingEntity {

	private static final long serialVersionUID = 4645812679199461202L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NOTIFICATION_HISTORY_SEQ")
	@SequenceGenerator(sequenceName = "NOTIFICATION_HISTORY_SEQ", name = "NOTIFICATION_HISTORY_SEQ", initialValue = 1,
			allocationSize = 1)
	@Column(name = "NOTIFICATION_HISTORY_ID")
	private long notificationHistoryId;

	@Column(name = "TRANSFER_TRANSACTION_ID")
	private long transferTransactionId;

	@Column(name = "TRANSFER_TYPE")
	@Enumerated(EnumType.STRING)
	private TransferType transferType;

	@Column(name = "CUSTOMER_ID")
	private long customerId;

	@Column(name = "CUSTOMER_ACCOUNT_NUMBER")
	private String customerAccNumber;

	@Column(name = "TARGET")
	private String target;

	@Column(name = "BANK_CODE")
	private String bankCode;

	@Column(name = "TRANSACTION_ID")
	private String transactionId;

	@Column(name = "TRANSACTION_AMOUNT")
	private Long transactionAmount;

	@Column(name = "PAY_DATE")
	private LocalDate payDate;

	@Column(name = "MESSAGE")
	private String message;

	@Column(name = "TRANSACTION_STATUS", nullable = false)
	@Enumerated(EnumType.STRING)
	private TransactionStatus transactionStatus;

	@Column(name = "TRANSACTION_FEE")
	private double transactionFee;

	@Column(name = "TRANSACTION_START_TIME")
	private Instant transactionStartTime;

	@Column(name = "TRANSACTION_FINISH_TIME")
	private Instant transactionFinishTime;

	@Column(name = "TRANSACTION_CURRENCY")
	private String transactionCurrency;

	@Column(name = "PHONE_NUMBER")
	@Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
	private String phoneNumber;

	@Column(name = "BRANCH_CODE")
	private String branchCode;

	@Column(name = "DESCRIPTION")
	private String description;

	@Column(name = "TRANSACTION_TYPE")
	@Enumerated(EnumType.STRING)
	private TransactionType transactionType;

	@Column(name = "TRANSACTION_CODE")
	private String transactionCode;

	// lưu lại tên trong trường hợp chuyển tiền T24
	@Column(name = "BENEFICIARY_CUSTOMER_NAME")
	private String beneficiaryCustomerName;

	@Column(name = "DISCOUNT")
	private double discount;

	@Column(name = "CONFIGURATION_FEE_TYPE")
	@Enumerated(EnumType.STRING)
	private ConfigurationFeeType configurationFeeType;

	@Column(name = "BENEFICIARY_ACCOUNT_NUMBER")
	private String beneficiaryAccountNumber;

	@Column(name = "EXTRA_TRANSACTION_CODE") // mã giao dịch trả về từ các bên như: Umoney, LTC
	private String extraTransactionCode;

	@Column(name = "TYPE")
	@Enumerated(EnumType.STRING)
	private TransferTransactionType type;

	@Column(name = "QR_CODE_VALUE", columnDefinition = "VARCHAR2(255)")
	private String qrCodeValue;

	@Column(name = "OTP_TYPE")
	@Enumerated(EnumType.STRING)
	private OtpConfirmType otpType;

	@Column(name = "BILLING_TYPE")
	private String billingType;

	@Column(name = "SAVING_ACCOUNT_TYPE")
	private String savingAccountType;

	@Column(name = "TOTAL_AMOUNT")
	private Double totalAmount;

	@Column(name = "STATUS")
	private int status;

	@Column(name = "ACTUAL_TRANSACTION_AMOUNT")
	private double actualTransactionAmount;

	@Column(name = "CLIENT_MESSAGE_ID")
	private String clientMessageId;

	@Column(name = "NOTIFICATION_TYPE")
	@Enumerated(EnumType.STRING)
	private NotificationHistoryEnum notificationType;

	@Column(name = "REFERRAL_CODE")
	private String referralCode;

	@Column(name = "CIF")
	private String cif;

}
