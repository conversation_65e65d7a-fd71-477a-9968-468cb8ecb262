package com.mb.laos.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Builder
@AllArgsConstructor
@Setter
public class PremiumAccNumberCacheDTO implements Serializable {
    private static final long serialVersionUID = 2792855453274042318L;

    private String premiumAccNumber;

    private String cif;

    private String staffCode;

    private String accountNo;

    private String currency;

    private String deviceId;

    private String transData;

    private Long price;

    private Double discount;

    private String referralCode;

    private Long premiumAccNumberStructureId;

    private boolean isSpecialNumber;

    private Long referralId;

    public PremiumAccNumberCacheDTO(String premiumAccNumber, String currency, Long referralId,
                                    Long price, Double discount, String referralCode,
                                    Long premiumAccNumberStructureId, boolean isSpecialNumber) {
        this.premiumAccNumber = premiumAccNumber;
        this.currency = currency;
        this.referralId = referralId;
        this.price = price;
        this.discount = discount;
        this.referralCode = referralCode;
        this.premiumAccNumberStructureId = premiumAccNumberStructureId;
        this.isSpecialNumber = isSpecialNumber;
    }

    public PremiumAccNumberCacheDTO(String premiumAccNumber, String cif, String paymentAccNumber, String currency, Long price,
                                    Double discount, Long premiumAccNumberStructureId, boolean isSpecialNumber) {
        this.premiumAccNumber = premiumAccNumber;
        this.cif = cif;
        this.accountNo = paymentAccNumber;
        this.currency = currency;
        this.price = price;
        this.discount = discount;
        this.premiumAccNumberStructureId = premiumAccNumberStructureId;
        this.isSpecialNumber = isSpecialNumber;
    }

    public PremiumAccNumberCacheDTO(String premiumAccNumber,
                                    Long price, Double discount,
                                    Long premiumAccNumberStructureId, boolean isSpecialNumber) {
        this.premiumAccNumber = premiumAccNumber;
        this.price = price;
        this.discount = discount;
        this.premiumAccNumberStructureId = premiumAccNumberStructureId;
        this.isSpecialNumber = isSpecialNumber;
    }

    public PremiumAccNumberCacheDTO() {

    }
}
