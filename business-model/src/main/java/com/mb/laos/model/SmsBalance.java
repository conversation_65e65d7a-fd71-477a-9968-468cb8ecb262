package com.mb.laos.model;


import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.Instant;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name ="SMS_BALANCE")
public class SmsBalance extends AbstractAuditingEntity implements Serializable {

    @Id
    @Column(name = "SMS_BALANCE_ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SMS_BALANCE_SEQ")
    @SequenceGenerator(sequenceName = "SMS_BALANCE_SEQ", name = "SMS_BALANCE_SEQ", initialValue = 1,
            allocationSize = 1)
    private Long smsBalanceId;

    @Column(name = "CUSTOMER_ACCOUNT_NUMBER")
    private String customerAccountNumber;

    @Column(name = "CUSTOMER_ID")
    private Long customerId;

    @Column(name = "PHONE_NUMBER")
    private String phoneNumber;

    @Column(name = "REGISTRATION_DATE")
    private Instant registrationDate;

    @Column(name = "CANCELLATION_DATE")
    private Instant cancellationDate;

    @Column(name = "SERVICE_FEE")
    private Double serviceFee;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "CURRENCY")
    private String currency;

}
