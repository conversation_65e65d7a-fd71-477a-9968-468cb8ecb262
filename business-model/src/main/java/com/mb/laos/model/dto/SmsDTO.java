package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmsDTO implements Serializable {
    private static final long serialVersionUID = -3443883564477620451L;

    private long smsId;

    private String from;

    private String to;

    private String content;

    private String type;

    private int status;
}
