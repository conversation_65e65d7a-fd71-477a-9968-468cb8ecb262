package com.mb.laos.model.dto;

import com.mb.laos.model.InformationTemplateContent;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
public class InformationTemplateDTO implements Serializable {

    private static final long serialVersionUID = -2987212579797960627L;
    
    private String displayName;
    
    private String template;

    private String informationTemplateName;

    private String informationTemplateCode;

    private long informationTemplateId;

    private Instant lastModifiedDate;

    private Instant createdDate;

    private String createdBy;

    private String lastModifiedBy;

    private int status;

    private InformationTemplateContent informationTemplateContent;

    private InformationTemplateContentDTO informationTemplateContentDTO;

    public InformationTemplateDTO(String informationTemplateCode, String informationTemplateName, String displayName, long informationTemplateId, Instant lastModifiedDate, Instant createdDate, String createdBy, String lastModifiedBy, int status, InformationTemplateContent informationTemplateContent) {
        this.informationTemplateCode = informationTemplateCode;
        this.informationTemplateName = informationTemplateName;
        this.displayName = displayName;
        this.informationTemplateId = informationTemplateId;
        this.lastModifiedDate = lastModifiedDate;
        this.createdDate = createdDate;
        this.createdBy = createdBy;
        this.lastModifiedBy = lastModifiedBy;
        this.status = status;
        this.informationTemplateContent = informationTemplateContent;
    }
}
