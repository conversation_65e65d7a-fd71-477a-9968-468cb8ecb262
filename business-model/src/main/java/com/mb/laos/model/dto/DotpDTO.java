package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DotpDTO {

    private static final long serialVersionUID = 2361109732412055517L;

    private Long dotpId;

    @NotBlank(message = LabelKey.ERROR_DEVICE_ID_MUST_NOT_BE_EMPTY)
    private String deviceId;

    private String deviceName;

    private String phoneNumber;

    private Long customerId;

    private String encKey;

    private String token;

    private Integer status;

    private String transData;

    public DotpDTO(Long dotpId, String deviceId, String deviceName, String phoneNumber, Long customerId, Integer status, String token) {
        this.dotpId = dotpId;
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.phoneNumber = phoneNumber;
        this.customerId = customerId;
        this.status = status;
        this.token = token;
    }
}
