package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.messages.LabelKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantTransactionHistorySearch extends Parameter {

    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;

    @NotNull(message = LabelKey.ERROR_INVALID_INPUT_DATA)
    private Long merchantId;

    private String customerAccountNumber;

    private String transactionId;

    private LocalDate transactionDateStart;

    private LocalDate transactionDateEnd;

    private List<Long> merchantIds;

    private ZoneId sourceZone;

    private boolean hasPageable;

    private List<TransactionStatus> transactionStatuses;
}
