package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * DTO for {@link com.mb.laos.model.InterestRateDetail}
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class InterestRateDetailDTO implements Serializable {
    private static final long serialVersionUID = -8212927866139630113L;

    private long interestRateDetailId;

    private long interestRateId;

    private String language;

    private String title;

    private DocumentDTO icon;

    private Instant createdDate;

    private Instant lastModifiedDate;

    private String createdDateStr;
}
