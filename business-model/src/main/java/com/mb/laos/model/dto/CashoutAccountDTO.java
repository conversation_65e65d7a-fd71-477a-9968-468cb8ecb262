package com.mb.laos.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.Value;

import java.io.Serializable;
import java.time.Instant;

/**
 * DTO for {@link com.mb.laos.model.CashoutAccount}
 */
@Getter
@Setter
@Value
public class CashoutAccountDTO implements Serializable {
    String createdBy;
    Instant createdDate;
    String lastModifiedBy;
    Instant lastModifiedDate;
    long id;
    String accountBank;
    String accountNumber;
    String accountName;
    String accountType;
    String accountCurrency;
    int status;
    Long transactionLimit;
    String cif;
    Long amountTransferred;
    String configurationType;
    Long accountBalance;
}
