package com.mb.laos.service;

import java.io.InputStream;

public interface EmailService {
    public void sendEmail(String to, String subject, String message);
    
    public boolean sendHtmlEmail(String to, String toCc, String toBcc, String subject,
                    String htmlContent);

    public boolean sendHtmlEmailAttached(String[] to, String toCc, String toBcc, String subject,
                                         String htmlContent, InputStream inputStream, String fileName);
}
