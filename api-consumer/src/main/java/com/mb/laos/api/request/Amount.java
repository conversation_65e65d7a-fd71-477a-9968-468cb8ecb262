package com.mb.laos.api.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class Amount implements Serializable {
    private static final long serialVersionUID = -2340283143967599470L;

    private String amount;

    private String currency;

    private InternationalPaymentInfo internationalPaymentInfo;

    @Getter
    @Setter
    @Builder
    public static class InternationalPaymentInfo implements Serializable {
        private static final long serialVersionUID = 8083413971285594516L;

        private String toNation;
        private String originalAmount;
        private String exchangeRate;
        private String exchangeCurrency;
    }
}
