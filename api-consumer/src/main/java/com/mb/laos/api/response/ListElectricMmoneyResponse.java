package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ListElectricMmoneyResponse extends ListMmoneyResponse {
    private static final long serialVersionUID = -6617157567351535161L;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("EWid")
    private Integer providerId;
}
