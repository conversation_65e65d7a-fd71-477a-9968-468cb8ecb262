package com.mb.laos.api.response;

import com.mb.laos.model.dto.QueryBeautifulAccountDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class QueryBeautifulAccountResponse extends ApiGeeResponse {
    private static final long serialVersionUID = -8533492684087998661L;

    private List<String> errorDesc = new ArrayList<>();

    private List<QueryBeautifulAccountDTO> data;

    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
