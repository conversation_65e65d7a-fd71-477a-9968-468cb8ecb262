package com.mb.laos.api.response;

import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.model.dto.BeautifulAccountDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class CreateBeautifulAccountResponse extends ApiGeeResponse {
    private static final long serialVersionUID = -8533492684087998661L;

    private List<String> errorDesc = new ArrayList<>();

    private BeautifulAccountDTO data;

    private TransactionStatus transactionStatus;

    private T24ErrorCode t24ErrorCode;

    @Override
    public Object getData() {
        return null;
    }

    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
