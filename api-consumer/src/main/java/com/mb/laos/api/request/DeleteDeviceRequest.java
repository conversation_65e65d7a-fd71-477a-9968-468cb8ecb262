package com.mb.laos.api.request;

import com.mb.laos.model.dto.DeviceInfoDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
public class DeleteDeviceRequest extends Request{
    private static final long serialVersionUID = 4990000980614746607L;

    private String phoneNumber;

    private String deviceId;

    private String customerId;

    private String token;

    private String dotp;

    private String userId;

    private String transData;

    private List<DeviceInfoDTO> deviceCancel;
}
