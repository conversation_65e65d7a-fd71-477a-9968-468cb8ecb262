package com.mb.laos.api.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VerifyPstnLtcResponse extends MmoneyResponse {
    private static final long serialVersionUID = 7959744969360751657L;

    @JsonProperty("Name")
    private String name;

    @JsonProperty("Balance")
    private String balance;
}
