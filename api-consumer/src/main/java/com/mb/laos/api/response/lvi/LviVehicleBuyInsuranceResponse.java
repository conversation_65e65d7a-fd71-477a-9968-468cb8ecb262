package com.mb.laos.api.response.lvi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.response.SoapResponse;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class LviVehicleBuyInsuranceResponse extends LviSoapResponse {
    @XmlElement(name = "identification_no")
    private String identificationNo;

    @XmlElement(name = "certificate_image")
    private String certificateImage;
}

