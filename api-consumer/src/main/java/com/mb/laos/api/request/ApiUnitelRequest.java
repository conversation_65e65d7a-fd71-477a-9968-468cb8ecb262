package com.mb.laos.api.request;


import com.mb.laos.enums.BillingType;
import lombok.*;

import javax.xml.bind.annotation.XmlElement;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApiUnitelRequest extends SoapRequest{

    private static final long serialVersionUID = 2650345178412603719L;
    
    private BillingType billingType;

    @XmlElement(name = "MTI")
    private String mti;

    @XmlElement(name = "MSISDN")
    private String msisdn;

    @XmlElement(name = "PROCESS_CODE")
    private String processCode;

    @XmlElement(name = "TRANS_AMOUNT")
    private String transAmount;

    @XmlElement(name = "TRANS_TIME")
    private String transTime;

    @XmlElement(name = "CUST_CODE")
    private String customerCode;

    @XmlElement(name = "SYSTEM_TRACE")
    private String systemTrace;

    @XmlElement(name = "CLIENT_ID")
    private String clientId;

    @XmlElement(name = "SIGNATURE")
    private String signature;


}
