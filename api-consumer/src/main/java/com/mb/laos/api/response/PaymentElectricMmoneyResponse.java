package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class PaymentElectricMmoneyResponse extends PaymentMmoneyResponse {
    private static final long serialVersionUID = -8603345258757335773L;

    @JsonProperty("ProCode")
    private String proCode;

    @JsonProperty("Title_LA")
    private String titleLa;

    @JsonProperty("CreateDate")
    private String createDate;
}
