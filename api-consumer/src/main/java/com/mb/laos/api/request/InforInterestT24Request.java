package com.mb.laos.api.request;

import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InforInterestT24Request extends Request {
    private static final long serialVersionUID = -3948364027891347294L;

    @NotNull(message = LabelKey.ERROR_ACCOUNT_TYPE_INVALID)
    private String productCode;

    @NotBlank(message = LabelKey.ERROR_CURRENCY_IS_REQUIRED)
    private String currency;

    @NotBlank(message = LabelKey.ERROR_PRODUCT_CATEGORY_IS_REQUIRED)
    private String category;

    private String period;

    private Integer subProduct;
}
