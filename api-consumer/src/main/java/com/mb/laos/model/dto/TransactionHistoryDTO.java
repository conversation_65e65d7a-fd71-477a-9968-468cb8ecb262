package com.mb.laos.model.dto;

import java.io.Serializable;

import com.mb.laos.annotation.Exclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TransactionHistoryDTO implements Serializable {
	
    /**
	 * 
	 */
	private static final long serialVersionUID = 3257224907810828973L;

	/**
     * Mã giao dịch
     */
    private String referenceNumber;

    /**
     *Số tài khoản
     */
    @Exclude
    private String accountNumber;

    /**
     * Loại giao dịch
     */
    private String transactionType;

    /**
     * Số tiền
     */
    private String amount;

    /**
     * Loại tiền
     */
    private String currency;

    /**
     * Nội dung giao dịch
     */
    private String paymentDetail;

    /**
     * Ng<PERSON>y hiệu lực
     */
    private String transactionDate;

    /**
     * Trạng thái giao dịch
     */
    private String transactionStatus;

    /**
     * Tên ngân hàng thụ hưởng
     */
    private String toBankName;

    /**
     * Mã ngân hàng thụ hưởng
     */
    private String toBankCode;

    /**
     * Tên tài khoản thụ hưởng
     */
    private String toBeneficiaryName;

    /**
     * Số tài khoản thụ hưởng
     */
    private String toBeneficiaryAccount;

    /**
     * Tên tài khoản gửi tiền
     */
    private String fromAccountName;

    /**
     * Số tài khoản gửi tiền
     */
    private String fromAccount;

    /**
     * Loại giao dịch (topup, transfer, billing,...)
     */
    private String transferType;
}
