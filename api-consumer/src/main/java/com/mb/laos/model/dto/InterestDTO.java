package com.mb.laos.model.dto;


import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.enums.TransactionStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class InterestDTO implements Serializable {
    private static final long serialVersionUID = 5721636384217463336L;

    private String savingAcctId;
    private String valueDate;
    private String maturityDate;
    private TransactionStatus transactionStatus;
    private T24ErrorCode t24ErrorCode;
}
