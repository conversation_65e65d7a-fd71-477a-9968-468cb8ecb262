package com.mb.laos.model;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * A user.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "INBOUND_REQ_LOG")

public class InboundReqLog extends AbstractRequestLogEntity implements Serializable {
	private static final long serialVersionUID = 7020271006196714571L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INBOUND_REQ_LOG_SEQ")
	@SequenceGenerator(sequenceName = "INBOUND_REQ_LOG_SEQ", name = "INBOUND_REQ_LOG_SEQ", initialValue = 1,
			allocationSize = 1)
	@Column(name = "INBOUND_REQ_LOG_ID", length = 19)
	private long id;

	@Column(name = "DEVICE_TOKEN", length = 255)
	private String deviceToken;
	
	@Column(name = "DEVICE_ID", length = 255)
	private String deviceId;
}
