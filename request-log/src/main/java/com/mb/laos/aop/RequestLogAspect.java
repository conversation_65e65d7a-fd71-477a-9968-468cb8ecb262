/**
 *
 */
package com.mb.laos.aop;

import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import com.mb.laos.util.DiagnosticContextUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import com.google.gson.Gson;
import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.annotation.OutboundRequestLog;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.exception.NoPermissionException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.Request;
import com.mb.laos.api.response.ConsumerResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.api.util.HttpUtil;
import com.mb.laos.configuration.ValidationProperties;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.AbstractRequestLogEntity;
import com.mb.laos.model.InboundReqLog;
import com.mb.laos.model.OutboundReqLog;
import com.mb.laos.repository.InboundReqLogRepository;
import com.mb.laos.repository.OutboundReqLogRepository;
import com.mb.laos.security.util.SecurityConstants;
import com.mb.laos.util.CalendarUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;

import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RequestLogAspect {
	private final Gson gson;

	private final InboundReqLogRepository inboundReqLogRepository;

	private final OutboundReqLogRepository outboundReqLogRepository;

	private final ValidationProperties validation;

	private static final String CLIENT_PATH = "/client";

	@Pointcut("execution(@com.mb.laos.annotation.*RequestLog * *(..))")
    public void requestLogPointcut() {
		// Create point cut
	}

	@Around("@annotation(inboundRequestLog)")
	public Object addInboundRequestLog(final ProceedingJoinPoint joinPoint, InboundRequestLog inboundRequestLog)
			throws Throwable {
		_log.debug("addInboundRequestLog is called");

		Object[] args = joinPoint.getArgs();

		if (Validator.isNull(args) || args.length < 2) {
			_log.warn("addInboundRequestLog: no parameter to process");

			return joinPoint.proceed();
		}

		args = Arrays.stream(args).filter(Validator::isNotNull).toArray();

		HttpServletRequest request = null;

		Request req = null;

		for (Object ob : args) {
			Class<?> clazz = ob.getClass();

			if (HttpServletRequest.class.isAssignableFrom(clazz)) {
				request = (HttpServletRequest) ob;
			} else if (Request.class.isAssignableFrom(clazz)) {
				req = (Request) ob;
			}
		}

		if (request != null && req != null) {
		    String deviceToken = request.getHeader(SecurityConstants.Header.DEVICE_TOKEN);
	        String clientMessageId = request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID);

            if (_log.isDebugEnabled()) {
                _log.debug("Request deviceToken: {}", deviceToken);
                _log.debug("Request clientMessageId: {}", clientMessageId);
            }

            //nhung request tu phia client thi ko can check deviceToken
	        if (Validator.isNull(deviceToken) && !request.getServletPath().contains(CLIENT_PATH)) {
	            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INVALID_DEVICE),
	                            ErrorCode.MSG1024.name(), LabelKey.ERROR_INVALID_DEVICE);
	        }

	        if (Validator.isNull(clientMessageId)) {
	            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INVALID_REQUEST),
	                            ErrorCode.MSG1025.name(), LabelKey.ERROR_INVALID_REQUEST);
	        }

	        // not ok, check deviceToken có match ko


			// create uuid
			req.setClientMessageId(clientMessageId);

			// init inbound log
			InboundReqLog reqLog = new InboundReqLog();

			reqLog.setClientMessageId(DiagnosticContextUtil.getClientMessageId());
			reqLog.setSourceIp(HttpUtil.getClientIP(request));
			reqLog.setDestIp(HttpUtil.getHostAddress());
			reqLog.setServiceName(joinPoint.getSignature().getName());
			reqLog.setRequestData(
					StringUtil.substring(this.gson.toJson(req), 0, this.validation.getSuperTextMaxLength()));
			reqLog.setRequestTime(Instant.now());
			reqLog.setMethod(request.getMethod());
			reqLog.setRequestUri(request.getRequestURI());
			reqLog.setDeviceToken(deviceToken);

			req.setRequestLog(reqLog);
		}

		return joinPoint.proceed();
	}

	@AfterReturning(pointcut = "requestLogPointcut()",
			returning = "returnValue")
	public void afterRequestReturing(final JoinPoint joinPoint, Object returnValue) {
		_log.debug("afterRequestReturing is called");

		if (Validator.isNull(returnValue)) {
			_log.warn("afterRequestReturing: nothing to check, returnValue is null");

			return;
		}

		AbstractRequestLogEntity reqLog = this.getRequestLog(joinPoint.getArgs());

		if (reqLog == null) {
			_log.warn("afterRequestReturing: nothing to check, reqLog is null");

			return;
		}

		reqLog.setResponseTime(Instant.now());
		reqLog.setHttpStatus(Status.OK.getStatusCode());
		reqLog.setResponseData(
				StringUtil.substring(this.gson.toJson(returnValue), 0, this.validation.getSuperTextMaxLength()));
		reqLog.setDuration(CalendarUtil.getDurationInMillis(reqLog.getRequestTime(), reqLog.getResponseTime()));

		if (ConsumerResponse.class.isAssignableFrom(returnValue.getClass())) {
			ConsumerResponse returnRes = (ConsumerResponse) returnValue;

			reqLog.setErrorCode(returnRes.getErrorCode());
			reqLog.setErrorDescription(returnRes.getErrorDesccription());
			reqLog.setErrorDetail(returnRes.getErrorDetail());
		}

		// save request log
		this.saveRequestLog(reqLog);
	}

	@AfterThrowing(value = "requestLogPointcut()", throwing = "ex")
	public <T extends Exception> void afterRequestThrowing(JoinPoint joinPoint, T ex) {
		_log.info("afterRequestThrowing is called with exception, message: {}", ex);

		AbstractRequestLogEntity reqLog = this.getRequestLog(joinPoint.getArgs());

		if (reqLog == null) {
			_log.info("afterRequestThrowing: nothing to check, reqLog is null");

			return;
		}

		Class<?> clazz = ex.getClass();

		reqLog.setResponseTime(Instant.now());
		reqLog.setResponseData(ex.toString());
		reqLog.setHttpStatus(this.getHttpStatus(ex));
		reqLog.setDuration(CalendarUtil.getDurationInMillis(reqLog.getRequestTime(), reqLog.getResponseTime()));

		if (AbstractThrowableProblem.class.isAssignableFrom(clazz)) {
			AbstractThrowableProblem atp = (AbstractThrowableProblem) ex;

			reqLog.setErrorCode(
					Validator.isNotNull(atp.getStatus()) ? String.valueOf(atp.getStatus().getStatusCode()) : null);
			reqLog.setErrorDescription(atp.getTitle());

			_log.info("afterRequestThrowing is called with AbstractThrowableProblem, detail: {}", atp.getDetail());
			// reqLog.setErrorDetail(atp.getDetail());
		} else if (HttpResponseException.class.isAssignableFrom(clazz)) {
			HttpResponseException hrEx = (HttpResponseException) ex;

			reqLog.setErrorCode(hrEx.getErrorCode());
			reqLog.setErrorDescription(hrEx.getErrorMessage());
		}

		//save request log
		this.saveRequestLog(reqLog);
	}

	@Around("@annotation(outboundRequestLog)")
	public Object addOutboundRequestLog(final ProceedingJoinPoint joinPoint, OutboundRequestLog outboundRequestLog)
			throws Throwable {
		_log.debug("addOutboundRequestLog is called");

		Object[] args = joinPoint.getArgs();

		if (Validator.isNull(args) || args.length < 1) {
			_log.warn("addOutboundRequestLog: no parameter to process");

			return joinPoint.proceed();
		}

		Request req = null;

		for (Object ob : args) {
			Class<?> clazz = ob.getClass();

			if (Request.class.isAssignableFrom(clazz)) {
				req = (Request) ob;
			}
		}

		if (req != null) {
			// create uuid
			req.setClientMessageId(DiagnosticContextUtil.getOutboundClientMessageId());
			if (req.getApiGeeTransactionId() == null) {
				req.setApiGeeTransactionId(UUIDUtil.generateUUID(20));
			}
			// init inbound log
			OutboundReqLog reqLog = new OutboundReqLog();

			reqLog.setClientMessageId(req.getClientMessageId());
			reqLog.setTransactionId(req.getApiGeeTransactionId());
			reqLog.setSourceIp(HttpUtil.getHostAddress());
			reqLog.setServiceName(joinPoint.getSignature().getName());
			reqLog.setRequestData(this.gson.toJson(req));
			reqLog.setRequestTime(Instant.now());
			reqLog.setMethod(req.getMethod().name());
			reqLog.setRequestUri(req.getUrl());

			req.setRequestLog(reqLog);
		}

		return joinPoint.proceed();
	}

	private AbstractRequestLogEntity getRequestLog(Object[] args) {
		if (Validator.isNull(args)) {
			_log.warn("getInboundReqLog: nothing to check, args is null");

			return null;
		}

		for (Object ob : args) {
			if (ob == null || !Request.class.isAssignableFrom(ob.getClass())) {
				continue;
			}

			Request req = (Request) ob;

			if (req == null || !AbstractRequestLogEntity.class.isAssignableFrom(req.getRequestLog().getClass())) {
				continue;
			}

			return (AbstractRequestLogEntity) req.getRequestLog();
		}

		return null;
	}

	private void saveRequestLog(AbstractRequestLogEntity reqLog) {
		Class<?> clazz = reqLog.getClass();

		_log.info("saveRequestLog with data {}", this.gson.toJson(reqLog));

		if (InboundReqLog.class.isAssignableFrom(clazz)) {
			this.inboundReqLogRepository.save_((InboundReqLog) reqLog);
		} else if (OutboundReqLog.class.isAssignableFrom(clazz)) {
			this.outboundReqLogRepository.save_((OutboundReqLog) reqLog);
		}
	}

	private <T extends Exception> int getHttpStatus(T ex) {
		Class<?> clazz = ex.getClass();

		int status = 0;

		if (BadRequestAlertException.class.isAssignableFrom(clazz)
				|| MethodArgumentNotValidException.class.isAssignableFrom(clazz)) {
			status = Status.BAD_REQUEST.getStatusCode();
		} else if (NoPermissionException.class.isAssignableFrom(clazz)) {
			status = Status.FORBIDDEN.getStatusCode();
		} else if (UnauthorizedException.class.isAssignableFrom(clazz)) {
			status = Status.UNAUTHORIZED.getStatusCode();
		} else if (HttpResponseException.class.isAssignableFrom(clazz)) {
			HttpResponseException hrEx = (HttpResponseException) ex;

			status = hrEx.getHttpStatus();
		} else {
			status = Status.INTERNAL_SERVER_ERROR.getStatusCode();
		}

		return status;
	}
}
