/*
 * FulltextSearchRepositoryImpl.java
 *
 * Copyright (C) 2021 by Evotek. All right reserved.
 * This software is the confidential and proprietary information of Evotek
 */
package com.mb.laos.repository.impl;

import java.time.Instant;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.transaction.Transactional;

import org.hibernate.search.jpa.FullTextEntityManager;
import org.hibernate.search.jpa.Search;
import org.springframework.stereotype.Repository;

import com.mb.laos.repository.FulltextSearchRepository;

/**
 * 03/12/2021 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Repository
@Transactional
public class FulltextSearchRepositoryImpl implements FulltextSearchRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @SuppressWarnings("unchecked")
    @Override
    public void updateIndex(Class<?> clazz, Instant lastUpdateTime) {
        
        FullTextEntityManager fullTextEntityManager = Search.getFullTextEntityManager(this.entityManager);
        
        String className = clazz.getSimpleName();
        
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM ").append(className).append(" e ");
        
        sql.append(" WHERE 1 = 1 AND e.lastModifiedDate >= :lastUpdateTime ");
        
        Query query = entityManager.createQuery(sql.toString(), clazz);
        
        query.setParameter("lastUpdateTime", lastUpdateTime);
        
        query.getResultStream().forEach(fullTextEntityManager::index);
        
        fullTextEntityManager.flushToIndexes();
        fullTextEntityManager.clear();
    }
}
