package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AccountTypeCif {
    SAVING(1),
    NONSAVING(0);

    private int status;

    public static List<Integer> getValues() {
        return Stream.of(values()).map(e -> e.status).collect(Collectors.toList());
    }
}
