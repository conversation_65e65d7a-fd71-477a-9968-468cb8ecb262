<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
<!--    <changeSet author="DucNT" id="m-2024-10-21-01">-->
<!--        <sql>-->
<!--            <![CDATA[-->
<!--            INSERT INTO "GROUP_PRIVILEGE"-->
<!--            VALUES ('47', 'Nhóm quyền quản lý nhóm số', 'group-privilege.number-group', '47', '1', NULL, NULL,-->
<!--                    NULL, NULL);-->
<!--            INSERT INTO  "PRIVILEGE" VALUES ('252', 'NUMBER_GROUP_CREATE', '47', 'privilege.number-group-create', NULL, NULL, NULL, NULL);-->
<!--            INSERT INTO  "PRIVILEGE" VALUES ('253', 'NUMBER_GROUP_UPDATE', '47', 'privilege.number-group-update', NULL, NULL, NULL, NULL);-->
<!--            INSERT INTO  "PRIVILEGE" VALUES ('254', 'NUMBER_GROUP_DELETE', '47', 'privilege.number-group-delete', NULL, NULL, NULL, NULL);-->
<!--            INSERT INTO  "PRIVILEGE" VALUES ('255', 'NUMBER_GROUP_READ', '47', 'privilege.number-group-read', NULL, NULL, NULL, NULL);-->
<!--            INSERT INTO  "PRIVILEGE" VALUES ('256', 'NUMBER_GROUP_EXPORT', '47', 'privilege.number-group-export', NULL, NULL, NULL, NULL);-->
<!--            ]]>-->
<!--        </sql>-->
<!--    </changeSet>-->
</databaseChangeLog>
