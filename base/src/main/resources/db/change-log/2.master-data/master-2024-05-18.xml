<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="m-2024-05-18-01">
        <sql>
            INSERT INTO "GROUP_PRIVILEGE"
            VALUES ('36', 'Nhóm quyền yêu cầu hỗ trợ khách hàng', 'group-privilege.customer-support', '36', '1', NULL, NULL,
                    NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('210', 'REQUEST_CUSTOMER_SUPPORT_CREATE', '36', 'privilege.customer-support-create', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('211', 'REQUEST_CUSTOMER_SUPPORT_UPDATE', '36', 'privilege.customer-support-update', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('212', 'REQUEST_CUSTOMER_SUPPORT_READ', '36', 'privilege.customer-support-read', NULL, NULL, NULL, NULL);
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="m-2024-05-18-02">
        <sql>
            DELETE FROM "ROLE_PRIVILEGE" WHERE "PRIVILEGE_ID" = 210
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="m-2024-05-18-03">
        <sql>
            DELETE FROM "PRIVILEGE" WHERE "PRIVILEGE_ID" = 210
        </sql>
    </changeSet>
</databaseChangeLog>
