<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="m-2024-05-14-01">
        <sql>
            <![CDATA[
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'NUMBER_GROUP', '4', '4', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'NUMBER_GROUP', '5', '5', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'NUMBER_GROUP', '6', '6', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'NUMBER_GROUP', '8', '8', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'NUMBER_GROUP', '9', '9', '', 1);
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
