<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="tuyen.td" id="m-2023-11-15-01">
        <sql>
            INSERT INTO  "ROLE" VALUES ('99', 'ROLE_ADMIN', '999', '0', '0', 'superadmin', 'superadmin', TO_TIMESTAMP('2023-11-15 10:38:32.000000', 'SYYYY-MM-DD HH24:MI:SS:FF6'), TO_TIMESTAMP('2023-11-15 10:38:32.000000', 'SYYYY-MM-DD HH24:MI:SS:FF6'), 'Super Aministrator', '1');
            INSERT INTO  "USER_ROLE" VALUES ('1', '99');
        </sql>
    </changeSet>
    <changeSet author="tuyen.td" id="m-2023-11-15-02">
        <sql>
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '9');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '10');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '11');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '12');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '13');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '33');
            INSERT INTO  "ROLE_PRIVILEGE" VALUES ('99', '34');
        </sql>
    </changeSet>
</databaseChangeLog>
