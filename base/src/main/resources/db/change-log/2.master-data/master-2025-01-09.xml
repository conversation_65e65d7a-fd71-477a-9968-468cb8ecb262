<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="m-2025-01-09-01">
        <sql>
          <![CDATA[
            INSERT INTO "EX_CONTENT_TEMPLATE"("TEMPLATE_CODE", "CREATED_BY", "CREATED_DATE", "LAST_MODIFIED_BY",
                                              "LAST_MODIFIED_DATE", "DESCRIPTION", "NAME", "STATUS", "CONTENT", "TITLE")
            VALUES ('SMS_APPROVAL_UNCLOSED', NULL, NULL, NULL, NULL, 'Mẫu SMS gửi thông báo mở đóng thành công',
                    'Mở đóng thành công', '1', 'label.sms.approval-unclosed-content',
                    'label.sms.approval-unclosed-title');
            ]]>
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="m-2025-01-09-02">
        <sql>
          <![CDATA[
            INSERT INTO  "PRIVILEGE" VALUES ('264', 'CUSTOMER_UNCLOSED', '7', 'privilege.customer-unclosed', NULL, NULL, NULL, NULL);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="m-2025-01-09-03">
        <sql>
          <![CDATA[
            INSERT INTO  "PRIVILEGE" VALUES ('265', 'CUSTOMER_UNCLOSED_APPROVAL', '7', 'privilege.customer-unclosed-approval', NULL, NULL, NULL, NULL);
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>




