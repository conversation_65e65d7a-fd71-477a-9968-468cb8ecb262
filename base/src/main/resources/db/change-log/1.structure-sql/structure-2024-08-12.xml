<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="tuyen.td" id="s-2024-08-12-01">
        <sql>
            create table SERVICE_PACK
            (
                SERVICE_PACK_ID           NUMBER(19) NOT NULL
                    constraint "SERVICE_PACK_pk"
                    primary key,
                NAME  VARCHAR2(255),
                TYPE  VARCHAR2(255),
                CLIENT_ID  VARCHAR2(255) NOT NULL,
                STATUS  NUMBER(2) DEFAULT 1 NOT NULL,
                CREATED_DATE TIMESTAMP(6),
                CREATED_BY   VARCHAR2(75),
                LAST_MODIFIED_DATE TIMESTAMP(6),
                LAST_MODIFIED_BY VARCHAR2(75)
            )
        </sql>
    </changeSet>
    <changeSet author="tuyen.td" id="s-2024-08-12-02">
        <sql>
            CREATE SEQUENCE "SERVICE_PACK_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-03">
        <sql>
            INSERT INTO SERVICE_PACK(SERVICE_PACK_ID, NAME, TYPE, CLIENT_ID, STATUS) VALUES (SERVICE_PACK_SEQ.nextval, 'MBBANK', 'MBBANK', 'mbbank', 1);
            INSERT INTO SERVICE_PACK(SERVICE_PACK_ID, NAME, TYPE, CLIENT_ID, STATUS) VALUES (SERVICE_PACK_SEQ.nextval, 'UMONEY', 'UMONEY', 'umoney', 1);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-04">
        <sql>
            ALTER TABLE SERVICE_PACK ADD CARRIER_TYPE VARCHAR(75);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-05">
        <sql>
            ALTER TABLE CLIENT ADD TYPE VARCHAR(75);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-06">
        <sql>
            create table SERVICE_PACK_FUNCTION
            (
                SERVICE_PACK_FUNCTION_ID           NUMBER(19) NOT NULL
                    constraint "SERVICE_PACK_FUNCTION_pk"
                    primary key,
                URL  VARCHAR2(255),
                NAME  VARCHAR2(255),
                SERVICE_PACK_ID  NUMBER(19) NOT NULL,
                STATUS  NUMBER(2) DEFAULT 1 NOT NULL,
                CREATED_DATE TIMESTAMP(6),
                CREATED_BY   VARCHAR2(75),
                LAST_MODIFIED_DATE TIMESTAMP(6),
                LAST_MODIFIED_BY VARCHAR2(75)
            )
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-07">
        <sql>
            ALTER TABLE CLIENT ADD NAME VARCHAR(75);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-08">
        <sql>
            CREATE SEQUENCE "SERVICE_PACK_FUNCTION_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-09">
        <sql>
            UPDATE CLIENT SET TYPE = 'INTER' WHERE CLIENT_ID IN ('umoney', 'mbbank');
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-10">
        <sql>
            ALTER TABLE CLIENT ADD CREATED_DATE TIMESTAMP(6);
            ALTER TABLE CLIENT ADD CREATED_BY   VARCHAR2(75);
            ALTER TABLE CLIENT ADD LAST_MODIFIED_DATE TIMESTAMP(6);
            ALTER TABLE CLIENT ADD LAST_MODIFIED_BY VARCHAR2(75);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-11">
        <sql>
            ALTER TABLE SERVICE_PACK ADD CODE VARCHAR2(75);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-12">
        <sql>
            INSERT INTO SERVICE_PACK_FUNCTION (SERVICE_PACK_FUNCTION_ID, SERVICE_PACK_ID, URL, NAME, STATUS)
            SELECT SERVICE_PACK_FUNCTION_SEQ.nextval, SERVICE_PACK_ID, '/transfer-transaction/client/confirm-beneficiary', 'API truy vấn tài khoản thụ hưởng', 1
            From SERVICE_PACK
            WHERE TYPE = 'UMONEY';

            INSERT INTO SERVICE_PACK_FUNCTION (SERVICE_PACK_FUNCTION_ID, SERVICE_PACK_ID, URL, NAME, STATUS)
            SELECT SERVICE_PACK_FUNCTION_SEQ.nextval, SERVICE_PACK_ID, '/transfer-transaction/client/cashout', 'API chuyển khoản cashout Umoney', 1
            From SERVICE_PACK
            WHERE TYPE = 'UMONEY';

            INSERT INTO SERVICE_PACK_FUNCTION (SERVICE_PACK_FUNCTION_ID, SERVICE_PACK_ID, URL, NAME, STATUS)
            SELECT SERVICE_PACK_FUNCTION_SEQ.nextval, SERVICE_PACK_ID, '/customer/client/register', 'API active tài khoản Umoney', 1
            From SERVICE_PACK
            WHERE TYPE = 'UMONEY';

            INSERT INTO SERVICE_PACK_FUNCTION (SERVICE_PACK_FUNCTION_ID, SERVICE_PACK_ID, URL, NAME, STATUS)
            SELECT SERVICE_PACK_FUNCTION_SEQ.nextval, SERVICE_PACK_ID, '/ewallet/client/verify-account', 'API xác thực tài khoản', 1
            From SERVICE_PACK
            WHERE TYPE = 'MBBANK';

            INSERT INTO SERVICE_PACK_FUNCTION (SERVICE_PACK_FUNCTION_ID, SERVICE_PACK_ID, URL, NAME, STATUS)
            SELECT SERVICE_PACK_FUNCTION_SEQ.nextval, SERVICE_PACK_ID, '/ewallet/client/transfer-account', 'API cộng tiền vào tài khoản', 1
            From SERVICE_PACK
            WHERE TYPE = 'MBBANK';
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-13">
        <sql>
            UPDATE SERVICE_PACK SET CREATED_DATE = TO_TIMESTAMP('08/12/2024 15:20:05.0','MM/DD/YYYY HH24:MI:SS.FF3'),
                                    LAST_MODIFIED_DATE = TO_TIMESTAMP('08/12/2024 15:20:05.0','MM/DD/YYYY HH24:MI:SS.FF3'),
                                    LAST_MODIFIED_BY = 'superadmin',
                                    CREATED_BY = 'superadmin'
            WHERE CLIENT_ID IN ('umoney', 'mbbank');
        </sql>
    </changeSet>

    <changeSet author="tuyentd" id="s-2024-08-12-14">
        <sql>
            ALTER TABLE T_TRANSACTION_FEE_TYPE ADD TYPE NUMBER(2);
        </sql>
    </changeSet>

    <changeSet author="tuyentd" id="s-2024-08-12-15">
        <sql>
            UPDATE T_TRANSACTION_FEE_TYPE SET TYPE = 1;
        </sql>
    </changeSet>

    <changeSet author="tuyentd" id="s-2024-08-12-16">
        <sql>
            INSERT INTO  "T_TRANSACTION_FEE_TYPE" VALUES (TRANSACTION_FEE_TYPE_SEQ.nextval, 'Chuyển khoản', 'TRANSFER', 'Chuyển khoản', '1', NULL, NULL, NULL, NULL, 0);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-17">
        <sql>
            ALTER TABLE CLIENT MODIFY CLIENT_ID  VARCHAR2(100);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-18">
        <sql>
            ALTER TABLE CLIENT MODIFY NAME VARCHAR2(500);
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-08-12-19">
        <sql>
            ALTER TABLE SERVICE_PACK MODIFY NAME NVARCHAR2(500);
            ALTER TABLE CLIENT MODIFY NAME NVARCHAR2(500);
            ALTER TABLE SERVICE_PACK_FUNCTION MODIFY NAME NVARCHAR2(500);
        </sql>
    </changeSet>

</databaseChangeLog>
