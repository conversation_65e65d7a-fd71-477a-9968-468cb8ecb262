<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="tuyen.td" id="s-2023-07-07-01">
        <sql>
            ALTER TABLE "T_FEE" ADD MERCHANT_ID NUMBER(19) VISIBLE DEFAULT NULL;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2023-07-07-02">
        <sql>
            CREATE TABLE "SMS_LOG"
            (
            "SMS_LOG_ID" NUMBER(38, 0) NOT NULL,
            "FROM_USER"             VARCHAR2(255),
            "TO_USER"            VARCHAR2(255),
            "CONTENT"             VARCHAR2(255),
            "TYPE"         VARCHAR2(255),
            "STATUS"       NUMBER(1)     default 0 not null,
            "CREATED_BY"               VARCHAR2(75),
            "CREATED_DATE"             TIMESTAMP,
            "LAST_MODIFIED_BY"         VARCHAR2(75),
            "LAST_MODIFIED_DATE"       TIMESTAMP
            );
        </sql>
    </changeSet>

    <changeSet author="tuyentd" id="s-2023-07-07-03">
        <sql>
            CREATE SEQUENCE "SMS_LOG_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>
</databaseChangeLog>
