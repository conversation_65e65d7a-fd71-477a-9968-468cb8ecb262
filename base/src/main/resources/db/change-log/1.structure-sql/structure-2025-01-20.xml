<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2025-01-20-01">
        <sql>
            CREATE TABLE COUNTER_SAVING_ACCOUNT
            (
                ID                    NUMBER(38, 0) NOT NULL,
                SAVING_ACCOUNT_NUMBER VARCHAR2(255) NOT NULL,
                CUSTOMER_ID           NUMBER(19, 0),
                START_TIME            TIMESTAMP,
                SETTLEMENT_DUE_TIME   TIMESTAMP,
                INTEREST_RATE         NUMBER(19,1),
                SAVING_AMOUNT         NUMBER(19, 0),
                CATEGORY              VARCHAR2(50),
                STATUS                NUMBER(2,0),
                CREATED_BY            VARCHAR2(75),
                CREATED_DATE          TIMESTAMP,
                LAST_MODIFIED_BY      VARCHAR2(75),
                LAST_MODIFIED_DATE    TIMESTAMP,
                CONSTRAINT PK_COUNTER_SAVING_ACCOUNT PRIMARY KEY (ID)
            );
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2025-01-20-02">
        <sql>
            CREATE SEQUENCE "COUNTER_SAVING_ACCOUNT_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>
</databaseChangeLog>