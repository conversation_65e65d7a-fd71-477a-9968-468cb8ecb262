<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2024-11-29-01">
        <sql>
            DROP INDEX UC_CUSTOMER_PHONE_NUMBER_COL;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-11-29-02">
        <sql>
            <![CDATA[
            create unique index UC_CUSTOMER_PHONE_NUMBER_COL on CUSTOMER (CASE WHEN "REFERENCE_ID" IS NULL THEN CASE WHEN STATUS <> -1 THEN "REFERENCE_ID" END END,
                                                                          CASE WHEN "REFERENCE_ID" IS NULL
                                                                               THEN CASE WHEN STATUS NOT IN (-1, 2) AND "APPROVAL_TYPE" <> 1  THEN "PHONE_NUMBER" END END,
                                                                          CASE WHEN "REFERENCE_ID" IS NULL
                                                                               THEN CASE WHEN STATUS NOT IN (-1, 2) AND "APPROVAL_TYPE" <> 1 THEN "APPROVAL_STATUS" END END);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-11-29-03">
        <sql>
            DROP INDEX UC_CUSTOMER_USERNAME_COL;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-11-29-04">
        <sql>
            <![CDATA[
            create unique index UC_CUSTOMER_USERNAME_COL on CUSTOMER (CASE WHEN "REFERENCE_ID" IS NULL THEN "REFERENCE_ID" END,
                                                                      CASE WHEN "REFERENCE_ID" IS NULL THEN
                                                                           CASE
                                                                                WHEN STATUS <> -1 THEN
                                                                                    CASE
                                                                                        WHEN "USERNAME" IS NULL AND STATUS <> 2 THEN "PHONE_NUMBER"
                                                                                        WHEN "USERNAME" IS NOT NULL AND STATUS <> 2 THEN "USERNAME"
                                                                                    END
                                                                           END
                                                                      END,
                                                                      CASE WHEN "REFERENCE_ID" IS NULL THEN
                                                                          CASE
                                                                               WHEN STATUS NOT IN(-1,2) THEN "APPROVAL_STATUS"
                                                                          END
                                                                      END);
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
