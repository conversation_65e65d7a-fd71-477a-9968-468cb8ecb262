<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2024-11-18-01">
        <sql>
            ALTER TABLE T_MONEY_ACCOUNT ADD AVAILABLE_AMOUNT_TEMP NUMBER(19,2);
            UPDATE "T_MONEY_ACCOUNT" SET AVAILABLE_AMOUNT_TEMP = AVAILABLE_AMOUNT;
            ALTER TABLE T_MONEY_ACCOUNT DROP COLUMN AVAILABLE_AMOUNT;
            ALTER TABLE T_MONEY_ACCOUNT RENAME COLUMN AVAILABLE_AMOUNT_TEMP TO AVAILABLE_AMOUNT;
        </sql>
    </changeSet>
    <changeSet author="tuyen.td" id="s-2024-11-18-01">
        <sql>
            CREATE TABLE  "CURRENCY" (
             "CURRENCY_ID" NUMBER(19) VISIBLE NOT NULL ,
             "STATUS" NUMBER(1) VISIBLE DEFAULT 1 ,
             "NAME" NVARCHAR2(255) VISIBLE ,
             "CODE" NVARCHAR2(75) VISIBLE ,
             "CREATED_BY" VARCHAR2(75 BYTE) VISIBLE DEFAULT NULL,
             "CREATED_DATE" TIMESTAMP(6) VISIBLE DEFAULT NULL,
             "LAST_MODIFIED_BY" VARCHAR2(75 BYTE) VISIBLE DEFAULT NULL,
             "LAST_MODIFIED_DATE" TIMESTAMP(6) VISIBLE DEFAULT NULL
            );
        </sql>
    </changeSet>

    <changeSet author="DucNT" id="s-2024-11-18-02">
        <sql>
            CREATE SEQUENCE "CURRENCY_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-11-18-03">
        <sql>
            INSERT INTO CURRENCY(CURRENCY_ID, NAME, CODE, STATUS) VALUES (CURRENCY_SEQ.nextval, 'LAK', 'LAK', 1);
            INSERT INTO CURRENCY(CURRENCY_ID, NAME, CODE, STATUS) VALUES (CURRENCY_SEQ.nextval, 'USD', 'USD', 1);
            INSERT INTO CURRENCY(CURRENCY_ID, NAME, CODE, STATUS) VALUES (CURRENCY_SEQ.nextval, 'THB', 'THB', 1);
            UPDATE CURRENCY SET CREATED_DATE = TO_TIMESTAMP('11/18/2024 13:20:05.0','MM/DD/YYYY HH24:MI:SS.FF3'),
                                    LAST_MODIFIED_DATE = TO_TIMESTAMP('11/18/2024 13:20:05.0','MM/DD/YYYY HH24:MI:SS.FF3'),
                                    LAST_MODIFIED_BY = 'superadmin',
                                    CREATED_BY = 'superadmin'
            WHERE CODE IN ('LAK', 'USD', 'THB');
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-11-18-04">
        <sql>
            ALTER TABLE CURRENCY ADD VALUE VARCHAR(10) VISIBLE;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-11-18-05">
        <sql>
            UPDATE CURRENCY SET VALUE = '418' WHERE CODE = 'LAK';
            UPDATE CURRENCY SET VALUE = '419' WHERE CODE = 'USD';
            UPDATE CURRENCY SET VALUE = '420' WHERE CODE = 'THB';
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2024-11-18-06">
        <sql>
            ALTER TABLE MERCHANT ADD CURRENCY VARCHAR(75) DEFAULT 'LAK';
        </sql>
    </changeSet>
</databaseChangeLog>
