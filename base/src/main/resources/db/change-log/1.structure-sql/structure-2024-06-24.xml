<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="DucNT" id="s-2024-06-24-01">
        <sql>
            CREATE TABLE T_RECEIVE_TRANSFER_MONEY
            (
                "RECEIVE_TRANSFER_MONEY_ID"  NUMBER(19) NOT NULL
                    constraint "T_RECEIVE_TRANSFER_MONEY_pk"
                    primary key,
                "TRANSFER_TYPE"              VARCHAR2(50),
                "CUSTOMER_ID"                NUMBER(19) NOT NULL,
                "CUSTOMER_ACCOUNT_NUMBER"    VARCHAR2(255) NOT NULL,
                "BENEFICIARY_ACCOUNT_NUMBER" VARCHAR2(255),
                "TARGET"                     VARCHAR2(100) NOT NULL,
                "TRANSACTION_ID"             VARCHAR2(50) NOT NULL,
                "MESSAGE"                    NVARCHAR2(1000),
                "TRANSACTION_AMOUNT"         NUMBER(19) NOT NULL,
                "TRANSACTION_CURRENCY"       VARCHAR2(20) NOT NULL,
                "BRANCH_CODE"                VARCHAR2(50),
                "TRANSACTION_FINISH_TIME"    TIMESTAMP(6),
                "TRANSACTION_FEE"            NUMBER(19,5),
                "STATUS"                     NUMBER(2) DEFAULT 1 NOT NULL,
                "CREATED_BY"                 VARCHAR2(75),
                "CREATED_DATE"               TIMESTAMP(6),
                "LAST_MODIFIED_BY"           VARCHAR2(75),
                "LAST_MODIFIED_DATE"         TIMESTAMP(6),
                "TRANSACTION_TYPE"           VARCHAR2(75),
                "BANK_CODE"                  VARCHAR2(50),
                "QR_CODE_VALUE"              VARCHAR2(255),
                "BENEFICIARY_CUSTOMER_NAME"  VARCHAR2(255),
                "MERCHANT_ID"                NUMBER(19)
            );
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-06-24-02">
        <sql>
            CREATE SEQUENCE "T_RECEIVE_TRANSFER_MONEY_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-06-24-03">
        <sql>
           ALTER TABLE "T_RECEIVE_TRANSFER_MONEY" DROP COLUMN TARGET;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-06-24-04">
        <sql>
            ALTER TABLE "T_RECEIVE_TRANSFER_MONEY" MODIFY "CUSTOMER_ID" NUMBER(19) NULL;
        </sql>
    </changeSet>
</databaseChangeLog>
