<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="DucNT" id="s-2023-08-02-01">
        <sql>
            CREATE TABLE T_TRANSACTION_LIMIT
            (
                TRANSACTION_LIMIT_ID         NUMBER(19,0) NOT NULL,
                SERVICE_NAME                 VARCHAR2(75) NOT NULL,
                MAX_TRANSFER_AMOUNT_OF_DAY   NUMBER(19,0) NOT NULL,
                MAX_TRANSFER_AMOUNT_OF_MONTH NUMBER(19,0) NOT NULL,
                MAX_TRANSFER_AMOUNT_OF_YEAR  NUMBER(19,0) NOT NULL,
                START_DATE                   TIMESTAMP NOT NULL,
                END_DATE                     TIMESTAMP NOT NULL,
                REASON                       VARCHAR2(255),
                STATUS                       NUMBER(2,0) DEFAULT 0,
                SECTOR_ID                    NUMBER(10,0) NOT NULL,
                CREATED_BY                   VARCHAR2(75),
                CREATED_DATE                 TIMESTAMP,
                LAST_MODIFIED_BY             VARCHAR2(75),
                LAST_MODIFIED_DATE           TIMESTAMP,
                CONSTRAINT PK_T_TRANSACTION_LIMIT PRIMARY KEY (TRANSACTION_LIMIT_ID)
            );
            COMMENT
            ON COLUMN T_TRANSACTION_LIMIT.STATUS IS 'Trạng thái, 0/1/-1, Chờ phê duyệt/Đã phê duyệt/Từ chối';
        </sql>
    </changeSet>

    <changeSet author="DucNT" id="s-2023-08-02-02">
        <sql>
            CREATE SEQUENCE "T_TRANSACTION_LIMIT_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

    <changeSet author="DucNT" id="s-2023-08-02-03">
        <sql>
            CREATE TABLE T_TRANSACTION_LIMIT_DETAIL
            (
                TRANSACTION_LIMIT_DETAIL_ID NUMBER(19, 0) NOT NULL,
                TRANSACTION_LIMIT_ID        NUMBER(19, 0) NOT NULL,
                MAX_TRANSFER_AMOUNT         NUMBER(19,0) NOT NULL,
                TRANSFER_TYPE               VARCHAR2(255) NOT NULL,
                STATUS                      NUMBER(2,0) DEFAULT 1,
                CREATED_BY                  VARCHAR2(75),
                CREATED_DATE                TIMESTAMP,
                LAST_MODIFIED_BY            VARCHAR2(75),
                LAST_MODIFIED_DATE          TIMESTAMP,
                CONSTRAINT PK_T_TRANSACTION_LIMIT_DETAIL PRIMARY KEY (TRANSACTION_LIMIT_DETAIL_ID)
            );
            COMMENT
            ON COLUMN T_TRANSACTION_LIMIT_DETAIL.STATUS IS 'Trạng thái, 0/1/-1, Không hoạt động/Hoạt động/Xoá';
        </sql>
    </changeSet>

    <changeSet author="DucNT" id="s-2023-08-02-04">
        <sql>
            CREATE SEQUENCE "T_TRANSACTION_LIMIT_DETAIL_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2023-08-02-05">
        <sql>
           ALTER TABLE "T_TRANSACTION_LIMIT" MODIFY MAX_TRANSFER_AMOUNT_OF_MONTH NUMBER(19,0) NULL;
           ALTER TABLE "T_TRANSACTION_LIMIT" MODIFY MAX_TRANSFER_AMOUNT_OF_YEAR NUMBER(19,0) NULL;
        </sql>
    </changeSet>
</databaseChangeLog>
