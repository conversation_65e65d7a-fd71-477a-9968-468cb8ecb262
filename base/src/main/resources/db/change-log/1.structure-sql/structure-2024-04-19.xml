<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2024-04-19-01">
        <sql>
            ALTER TABLE "CUSTOMER" ADD LAST_TRANSACTION TIMESTAMP;
            COMMENT ON COLUMN  "CUSTOMER"."LAST_TRANSACTION" IS 'Thời gian giao dịch cuối';
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-04-19-02">
        <sql>
            ALTER TABLE "CUSTOMER" ADD LAST_CLOSE_ACCOUNT_NOTIFICATION TIMESTAMP;
            COMMENT ON COLUMN  "CUSTOMER"."LAST_CLOSE_ACCOUNT_NOTIFICATION" IS 'Thời gian gửi thông báo đóng cuối';
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-04-19-03">
        <sql>
            UPDATE CUSTOMER  c SET c.LAST_TRANSACTION  = (SELECT MAX(t.LAST_MODIFIED_DATE) FROM T_TRANSACTION t WHERE t.CUSTOMER_ID = c.CUSTOMER_ID)
            WHERE EXISTS (SELECT 1 FROM T_TRANSACTION t2 WHERE t2.CUSTOMER_ID = c.CUSTOMER_ID)
        </sql>
    </changeSet>
</databaseChangeLog>
