<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="tuyen.td" id="s-2025-06-12-01">
        <sql>
            CREATE TABLE "T_NOTIFICATION_HISTORY"
            (
                "NOTIFICATION_HISTORY_ID"      NUMBER(19) VISIBLE NOT NULL,
                "TRANSFER_TRANSACTION_ID"    NUMBER(19) VISIBLE,
                "TRANSFER_TYPE"              VARCHAR2(50 BYTE) VISIBLE,
                "CUSTOMER_ID"                NUMBER(19) VISIBLE NOT NULL,
                "CUSTOMER_ACCOUNT_NUMBER"    VARCHAR2(255 BYTE) VISIBLE,
                "TARGET"                     VARCHAR2(255),
                "TRANSACTION_ID"             VARCHAR2(50 BYTE) VISIBLE NOT NULL,
                "PAY_DATE"                   TIMESTAMP(6) VISIBLE,
                "MESSAGE"                    NVARCHAR2(1000) VISIBLE,
                "TRANSACTION_STATUS"         VARCHAR2(50 BYTE) VISIBLE NOT NULL,
                "TRANSACTION_FEE"            NUMBER(19) VISIBLE,
                "TRANSACTION_START_TIME"     TIMESTAMP(6) VISIBLE,
                "TRANSACTION_AMOUNT"         NUMBER(19,2) VISIBLE,
                "TRANSACTION_CURRENCY"       VARCHAR2(20 BYTE) VISIBLE,
                "PHONE_NUMBER"               VARCHAR2(255 BYTE) VISIBLE NOT NULL,
                "BRANCH_CODE"                VARCHAR2(50 BYTE) VISIBLE NOT NULL,
                "TRANSACTION_FINISH_TIME"    TIMESTAMP(6) VISIBLE,
                "DESCRIPTION"                NVARCHAR2(255) VISIBLE,
                "STATUS"                     NUMBER(2) VISIBLE DEFAULT 0 NOT NULL,
                "CREATED_BY"                 VARCHAR2(75 BYTE) VISIBLE,
                "CREATED_DATE"               TIMESTAMP(6) VISIBLE,
                "LAST_MODIFIED_BY"           VARCHAR2(75 BYTE) VISIBLE,
                "LAST_MODIFIED_DATE"         TIMESTAMP(6) VISIBLE,
                "TRANSACTION_TYPE"           VARCHAR2(75 BYTE) VISIBLE,
                "BANK_CODE"                  VARCHAR2(50 BYTE) VISIBLE,
                "TRANSACTION_CODE"           VARCHAR2(50 BYTE) VISIBLE,
                "BENEFICIARY_CUSTOMER_NAME"  NVARCHAR2(255) VISIBLE,
                "OTP_TYPE"                   VARCHAR2(255),
                "TOTAL_AMOUNT"               NUMBER(19,2),
                "CONFIGURATION_FEE_TYPE"     VARCHAR(50),
                "DISCOUNT"                   NUMBER(19) VISIBLE,
                "QR_CODE_VALUE"              VARCHAR2(255),
                "BENEFICIARY_ACCOUNT_NUMBER" VARCHAR(255),
                "EXTRA_TRANSACTION_CODE"     VARCHAR2(50 CHAR),
                "TYPE"                       VARCHAR2(50 BYTE) VISIBLE,
                "BILLING_TYPE"               VARCHAR2(50),
                "CLIENT_MESSAGE_ID"          VARCHAR2(255),
                "ACTUAL_TRANSACTION_AMOUNT"  NUMBER(19,2) VISIBLE,
                SAVING_ACCOUNT_TYPE VARCHAR2(50)
            );
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2025-02-06-02">
        <sql>
            CREATE SEQUENCE  "NOTIFICATION_HISTORY_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2025-02-06-03">
        <sql>
            ALTER TABLE "T_NOTIFICATION_HISTORY" ADD "NOTIFICATION_TYPE" VARCHAR2(50);
            COMMENT ON COLUMN  "T_NOTIFICATION_HISTORY"."NOTIFICATION_TYPE" IS 'Loại Thông báo, NOTIFICATION_LIMIT: Thông báo hạn mức, PREMIUM_ACCOUNT_NUMBER_REVERT: thông báo lỗi thu hồi TKSĐ';
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2025-02-06-04">
        <sql>
            ALTER TABLE "T_NOTIFICATION_HISTORY" ADD "REFERRAL_CODE" VARCHAR2(100);
            COMMENT ON COLUMN  "T_NOTIFICATION_HISTORY"."REFERRAL_CODE" IS 'Ma gioi thieu';
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2025-02-06-05">
        <sql>
            ALTER TABLE "T_NOTIFICATION_HISTORY" ADD "CIF" VARCHAR2(20);
            COMMENT ON COLUMN  "T_NOTIFICATION_HISTORY"."CIF" IS 'Ma khach hang CIF';
        </sql>
    </changeSet>

</databaseChangeLog>
