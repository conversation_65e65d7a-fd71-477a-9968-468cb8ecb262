package com.mb.laos.controller;

import com.mb.laos.controller.request.NotificationBalanceRequest;
import com.mb.laos.controller.request.NotificationRequest;
import com.mb.laos.controller.request.NotificationSearchRequest;
import com.mb.laos.model.dto.NotiTransactionDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.NotificationQualityDTO;
import com.mb.laos.service.NotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
public class NotificationController {

    private final NotificationService notificationService;

    @PostMapping("/me/notification")
    public ResponseEntity<Page<NotificationDTO>> getNotificationAdvertisement(
            @RequestBody @Valid NotificationSearchRequest notificationSearchRequest) {
        return ResponseEntity.ok().body(notificationService.getNotification(notificationSearchRequest));
    }

    @PostMapping("/me/notification-balance")
    public ResponseEntity<Page<NotificationDTO>> getNotificationBalance(
            @RequestBody @Valid NotificationBalanceRequest request, Pageable pageable) {
        return ResponseEntity.ok().body(notificationService.getNotificationBalance(request, pageable));
    }

    @PostMapping("/me/mark-read")
    public ResponseEntity<Void> markRead(@RequestBody NotificationRequest request) {
        this.notificationService.markRead(request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/me/mark-read-all")
    public ResponseEntity<Boolean> markReadAll() {
        this.notificationService.markReadAll();
        return ResponseEntity.ok(Boolean.TRUE);
    }

    @PostMapping("/me/count-unread")
    public ResponseEntity<NotificationQualityDTO> countNotificationUnread() {
        return ResponseEntity.ok().body(notificationService.countNotificationUnread());
    }

    /**
     * API dùng để test bắn notification bằng websocket
     */
    @PostMapping("/send-notification")
    public void sendNotification(@RequestBody NotificationDTO notificationDTO) {
        notificationService.sendNotification(notificationDTO);
    }

    @PostMapping("/detail")
    public ResponseEntity<NotificationDTO> getDetail(@RequestBody NotificationRequest request) {
        return ResponseEntity.ok(notificationService.getDetail(request));
    }

    @GetMapping(value = "/public/icon/{fileEntryId}/{normalizeName}/{classPk}", produces = {MediaType.IMAGE_JPEG_VALUE, MediaType.IMAGE_PNG_VALUE, MediaType.IMAGE_GIF_VALUE, "image/heic", "image/bmp"})
    @ResponseBody
    public byte[] getIconBinary(@PathVariable("fileEntryId") String fileEntryId, @PathVariable("normalizeName") String normalizeName, @PathVariable("classPk") String classPk) {
        return this.notificationService.getIconByte(classPk, fileEntryId, normalizeName);
    }

    @PostMapping("/detail-noti-transaction")
    public ResponseEntity<NotiTransactionDTO> getNotiTransaction(@RequestBody NotificationRequest request) {
        return ResponseEntity.ok(notificationService.getNotiTransaction(request));
    }
}
