package com.mb.laos.repository;

import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.model.Notification;
import com.mb.laos.model.dto.NotificationDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationCacheRepository extends JpaRepository<Notification, Long> {

    @CachePut(cacheNames = BusinessCacheConstants.Notification.NOTIFICATION_TRANSFER, key = "#key", unless = "#result == null")
    default NotificationDTO put(String key, NotificationDTO value) {
        return value;
    }

    @CacheEvict(cacheNames = BusinessCacheConstants.Notification.NOTIFICATION_TRANSFER, key = "#key")
    default String evict(String key) {
        return null;
    }
}
