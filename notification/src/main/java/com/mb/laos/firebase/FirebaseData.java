package com.mb.laos.firebase;

import com.mb.laos.model.dto.DocumentDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FirebaseData {
    private String topic;
    private String token;
    private String title;
    private String body;
    private String imageUrl;
    private DocumentDTO icon;
    private Map<String, String> data;
}
