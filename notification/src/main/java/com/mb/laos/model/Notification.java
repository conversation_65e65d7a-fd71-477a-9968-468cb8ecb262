package com.mb.laos.model;

import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.TargetType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "NOTIFICATION")
@EqualsAndHashCode(callSuper = false)
public class Notification extends AbstractAuditingEntity {

    private static final long serialVersionUID = 67775815282920687L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NOTIFICATION_SEQ")
    @SequenceGenerator(sequenceName = "NOTIFICATION_SEQ", name = "NOTIFICATION_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "NOTIFICATION_ID", length = 19)
    private long notificationId;

    @Column(name = "CLASS_PK")
    private long classPk;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "CONTENT")
    private String content;

    @Column(name = "NOTIFICATION_STATUS")
    @Enumerated(EnumType.STRING)
    private NotificationStatus notificationStatus;

    @Column(name = "PUBLISH_TIME")
    private LocalDateTime publishTime;

    @Column(name = "NOTIFICATION_TYPE")
    @Enumerated(EnumType.STRING)
    private NotificationType notificationType;

    @Column(name = "TARGET_TYPE")
    @Enumerated(EnumType.STRING)
    private TargetType targetType;

    @Column(name = "TOPIC_NAME")
    private String topicName;

    @Column(name = "IS_READ")
    private boolean isRead;

    @Column(name = "END_USER_TYPE")
    @Enumerated(EnumType.STRING)
    private EndUserType endUserType;

    @Column(name = "EVENT_ID")
    private long eventId;
}
