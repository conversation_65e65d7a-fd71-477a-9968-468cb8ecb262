/**
 * 
 */
package com.mb.laos.repository.extend;

import java.util.List;
import org.springframework.data.domain.Pageable;
import com.mb.laos.model.FileEntry;
import com.mb.laos.model.search.FileEntrySearch;

/**
 * <AUTHOR>
 *
 */
public interface FileEntryRepositoryExtend {

	/**
	 * @param searchParam
	 * @param pageable
	 * @return
	 */
	List<FileEntry> search(FileEntrySearch params, Pageable pageable);
	
	/**
	 * 
	 * @param searchParam
	 * @return
	 */
	Long count (FileEntrySearch params);
}
