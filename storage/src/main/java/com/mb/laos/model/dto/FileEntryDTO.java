/**
 * 
 */
package com.mb.laos.model.dto;

import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
public class FileEntryDTO implements Serializable {
	private static final long serialVersionUID = 2105312206084619913L;

	private Long entryId;

	private Long classPk;
	
	@ApiModelProperty(hidden = true)
	private String className;
	
	@ApiModelProperty(hidden = true)
	private String resourceType;

	private String hashKey;

	private String description;
}
