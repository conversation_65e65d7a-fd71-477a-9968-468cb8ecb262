package com.mb.laos.dto.jackson;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;
import java.io.IOException;

import org.junit.jupiter.api.Test;

import com.mb.laos.dto.MixInForIgnoreType;
import com.mb.laos.model.dto.PrivilegeDTO;
import com.mb.laos.model.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 05/06/2021 - LinhLH: Create new
 *
 * <AUTHOR>
 */
public class JacksonSerializationIgnoreUnitTest {
    // tests - single entity to json

    // ignore

    @Test
    public final void givenOnlyNonDefaultValuesAreSerializedAndDtoHasOnlyDefaultValues_whenSerializing_thenCorrect()
            throws JsonParseException, IOException {
        final ObjectMapper mapper = new ObjectMapper();
        final String dtoAsString = mapper.writeValueAsString(new UserDTO());

        assertThat(dtoAsString, not(containsString("id")));
        
        System.out.println(dtoAsString);
    }

    @Test
    public final void givenOnlyNonDefaultValuesAreSerializedAndDtoHasNonDefaultValue_whenSerializing_thenCorrect()
            throws JsonParseException, IOException {
        final ObjectMapper mapper = new ObjectMapper();
        
        final UserDTO userDTO = new UserDTO();
        
        userDTO.setStatus(1);

        final String dtoAsString = mapper.writeValueAsString(userDTO);

        assertThat(dtoAsString, containsString("status"));
        
        System.out.println(dtoAsString);
    }

    @Test
    public final void givenFieldIsIgnoredByName_whenDtoIsSerialized_thenCorrect()
            throws JsonParseException, IOException {
        final ObjectMapper mapper = new ObjectMapper();
        
        final UserDTO userDTO = new UserDTO();
        
        userDTO.setUserId(1L);

        final String dtoAsString = mapper.writeValueAsString(userDTO);

        assertThat(dtoAsString, containsString("userId"));
        assertThat(dtoAsString, not(containsString("status")));
        
        System.out.println(dtoAsString);
    }

    @Test
    public final void givenFieldIsIgnoredDirectly_whenDtoIsSerialized_thenCorrect()
            throws JsonParseException, IOException {
        final ObjectMapper mapper = new ObjectMapper();
        
        final UserDTO userDTO = new UserDTO();

        final String dtoAsString = mapper.writeValueAsString(userDTO);

        assertThat(dtoAsString, not(containsString("id")));
        assertThat(dtoAsString, not(containsString("status")));
        
        System.out.println(dtoAsString);
    }

    // @Ignore("Jackson 2.7.1-1 seems to have changed the API for this case")
    @Test
    public final void givenFieldTypeIsIgnored_whenDtoIsSerialized_thenCorrect() throws JsonParseException, IOException {
        final ObjectMapper mapper = new ObjectMapper();
        
        mapper.addMixIn(String[].class, MixInForIgnoreType.class);
        
        final UserDTO userDTO = new UserDTO();
        
        userDTO.setStatus(1);

        final String dtoAsString = mapper.writeValueAsString(userDTO);

        assertThat(dtoAsString, not(containsString("id")));
        assertThat(dtoAsString, containsString("status"));
        assertThat(dtoAsString, not(containsString("lastModifiedDate")));
        
        System.out.println(dtoAsString);
    }

    @Test
    public final void givenNullsIgnoredOnClass_whenWritingObjectWithNullField_thenIgnored()
            throws JsonProcessingException {
        final ObjectMapper mapper = new ObjectMapper();
        
        final UserDTO userDTO = new UserDTO();

        final String dtoAsString = mapper.writeValueAsString(userDTO);

        assertThat(dtoAsString, not(containsString("id")));
        assertThat(dtoAsString, not(containsString("status")));
        assertThat(dtoAsString, not(containsString("lastModifiedDate")));
        
        System.out.println(dtoAsString);
    }

    @Test
    public final void givenNullsIgnoredGlobally_whenWritingObjectWithNullField_thenIgnored()
            throws JsonProcessingException {
        final ObjectMapper mapper = new ObjectMapper();
        
        mapper.setSerializationInclusion(Include.NON_NULL);
        
        final PrivilegeDTO privilegeDTO = new PrivilegeDTO();

        privilegeDTO.setName("UPDATE_ROLE");
        
        final String dtoAsString = mapper.writeValueAsString(privilegeDTO);

        assertThat(dtoAsString, not(containsString("id")));
        assertThat(dtoAsString, containsString("name"));
        assertThat(dtoAsString, not(containsString("description")));
        
        System.out.println(dtoAsString);
    }
}
