package com.mb.laos.model;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Instant;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase.Replace;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.test.annotation.Rollback;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.model.dto.DepartmentDTO;
import com.mb.laos.service.DepartmentService;

@ExtendWith(MockitoExtension.class)
@DataJpaTest
@AutoConfigureTestDatabase(replace = Replace.NONE)
@Rollback(false)
public class DepartmentTest {
    
    @Mock
    private DepartmentService departmentService;
   
    @Test
    public void createSuccessTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentName("Phong HR");
        dto.setDescription("Phong HR");
        dto.setCreatedBy("superadmin");
        dto.setCreatedDate(Instant.now());
        dto.setLastModifiedDate(Instant.now());
        dto.setDepartmentCode("HR");
        dto.setShortName("HR");
        dto.setCreatedByFullname("Hoang Le Minh");
        dto.setLastModifiedByFullname("Hoang Le Minh");
        dto.setStatus(1);

        Mockito.when(this.departmentService.create(dto)).thenReturn(dto);

        assertEquals(dto, this.departmentService.create(dto));
    }
    
    @Test
    public void createFailedTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentName("Phong HR");
        dto.setDescription("Phong HR");
        dto.setCreatedBy("superadmin");
        dto.setCreatedDate(Instant.now());
        dto.setLastModifiedDate(Instant.now());
        dto.setDepartmentCode("HR");
        dto.setShortName("HR");
        dto.setCreatedByFullname("Hoang Le Minh");
        dto.setLastModifiedByFullname("Hoang Le Minh");
        dto.setStatus(1);

        Mockito.when(this.departmentService.create(dto)).thenThrow(BadRequestAlertException.class);

        assertThatThrownBy(() -> departmentService.create(dto))
            .isInstanceOf(BadRequestAlertException.class);
    }

    @Test
    public void getDetailSuccessTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentId(1L);

        Mockito.when(this.departmentService.findById(dto.getDepartmentId())).thenReturn(dto);

        assertNotNull(this.departmentService.findById(dto.getDepartmentId()));
    }
    
    @Test
    public void getDetailFailTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentId(1L);

        Mockito.when(this.departmentService.findById(dto.getDepartmentId())).thenThrow(BadRequestAlertException.class);

        assertThatThrownBy(() -> departmentService.findById(dto.getDepartmentId()))
            .isInstanceOf(BadRequestAlertException.class);
    }
    
    @Test
    public void updateSuccessTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentId(1L);
        dto.setDepartmentName("Phong HR");
        dto.setDescription("Phong HR");
        dto.setCreatedBy("superadmin");
        dto.setCreatedDate(Instant.now());
        dto.setLastModifiedDate(Instant.now());
        dto.setDepartmentCode("HR");
        dto.setShortName("HR");
        dto.setCreatedByFullname("Hoang Le Minh");
        dto.setLastModifiedByFullname("Hoang Le Minh");
        dto.setStatus(1);
        
        Mockito.when(this.departmentService.update(dto)).thenReturn(dto);

        assertEquals(dto, this.departmentService.update(dto));
    }
    
    @Test
    public void updateFailTest() {
        DepartmentDTO dto = new DepartmentDTO();

        dto.setDepartmentId(1L);
        dto.setDepartmentName("Phong HR");
        dto.setDescription("Phong HR");
        dto.setCreatedBy("superadmin");
        dto.setCreatedDate(Instant.now());
        dto.setLastModifiedDate(Instant.now());
        dto.setDepartmentCode("HR");
        dto.setShortName("HR");
        dto.setCreatedByFullname("Hoang Le Minh");
        dto.setLastModifiedByFullname("Hoang Le Minh");
        dto.setStatus(1);

        Mockito.when(this.departmentService.update(dto)).thenThrow(BadRequestAlertException.class);

        assertThatThrownBy(() -> departmentService.update(dto))
            .isInstanceOf(BadRequestAlertException.class);
    }
}
