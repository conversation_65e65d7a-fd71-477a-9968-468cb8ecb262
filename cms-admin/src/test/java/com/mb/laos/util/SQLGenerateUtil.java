/*
 * SQLGenerateUtil.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved.
 * This software is the confidential and proprietary information of Evotek
 */
package com.mb.laos.util;

/**
 * 20/09/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
public class SQLGenerateUtil {
    /**
     * @param args
     * @throws Exception 
     */
    public static void main(String[] args) throws Exception {
        generateUser(2101, 2);
        //generateUserRole(2101, 2);
    }
    
    public static void generateUser(int start, int total) {
        for (int i = start; i< total + start; i++) {
            StringBuilder sb = new StringBuilder();
            
            sb.append("INSERT INTO \"USER_\"(\"USER_ID\", \"CREATED_BY\", \"CREATED_DATE\", \"LAST_MODIFIED_BY\", \"LAST_MODIFIED_DATE\", \"DATE_OF_BIRTH\", \"DESCRIPTION\", \"EMAIL\", \"FULLNAME\", \"PASSWORD\", \"PHONE_NUMBER\", \"ROLE_UPDATABLE\", \"STATUS\", \"USERNAME\", \"GENDER\", \"DEVICE_TOKEN\", \"PW_EXPIRED_TIME\", \"IS_ACTIVE\", \"POSITION_ID\", \"DEPARTMENT_ID\") VALUES ('");
            sb.append(i);
            sb.append("', NULL, TO_TIMESTAMP('2021-09-10 17:31:47.000000', 'SYYYY-MM-DD HH24:MI:SS:FF6'), '");
            sb.append("admin");
            sb.append(i);
            sb.append("', TO_TIMESTAMP('2022-09-19 08:18:34.545000', 'SYYYY-MM-DD HH24:MI:SS:FF6'), NULL, NULL, '");
            sb.append("admin");
            sb.append(i);
            sb.append("@gmail.com', '");
            sb.append("Admin ");
            sb.append(i);
            sb.append("', '");
            sb.append("$2a$11$Otax3i7GaLH4UP/EUsajP.3uAU7Ld6/KwWeJ1DXa0fGDE.DJkkYpm");
            sb.append("', NULL, '0', '1', '");
            sb.append("admin");
            sb.append(i);
            sb.append("', '1', NULL, NULL, '0', NULL, NULL);");
            
            System.out.println(sb.toString());
        }
    }
    
    public static void generateUserRole(int start, int total) {
        for (int i = start; i< total + start; i++) {
            StringBuilder sb = new StringBuilder();
            
            sb.append("INSERT INTO \"USER_ROLE\"(\"USER_ID\", \"ROLE_ID\") VALUES ('");
            sb.append(i);
            sb.append("', '1');");
            
            System.out.println(sb.toString());
        }
    }
}
