package com.mb.laos.model.search;

import java.time.LocalDate;

import com.mb.laos.enums.LapnetAsyncType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class LapnetTransactionReportSearch extends Parameter {

	private static final long serialVersionUID = -5174423685195348836L;
	
	private LocalDate fromDate;
	
	private LocalDate toDate;

	private LocalDate asyncDate;

	private LapnetAsyncType type;
}
