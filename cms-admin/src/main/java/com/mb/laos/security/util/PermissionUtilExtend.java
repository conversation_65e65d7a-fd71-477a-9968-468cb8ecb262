/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.mb.laos.security.util;

import java.util.Collection;
import com.mb.laos.model.Role;

/**
 *
 * <AUTHOR>
 */
public class PermissionUtilExtend extends PermissionUtil{
	public static boolean isAdministrator(Collection<String> roles) {
		return roles.contains(SecurityConstants.SystemRole.SUPER_ADMIN);
	}

	public static boolean isAdministrator(Role role) {
		return SecurityConstants.SystemRole.SUPER_ADMIN.equals(role.getName());
	}
}
