/**
 * 
 */
package com.mb.laos.repository.extend;

import com.mb.laos.cache.util.CmsCacheConstants;
import com.mb.laos.model.Privilege;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface PrivilegeRepositoryExtend {
	
	@Cacheable(cacheNames = CmsCacheConstants.Privilege.FIND_BY_ROLEID, key = "#roleId", unless = "#result == null")
	List<Privilege> findByRoleId(long roleId);
}
