package com.mb.laos.repository;

import com.mb.laos.cache.util.OtpCacheConstants;
import com.mb.laos.model.PremiumAccNumberCacheDTO;
import com.mb.laos.model.SmsBalance;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PremiumAccNumberCacheCmsRepository extends JpaRepository<SmsBalance, Long> {

    @CachePut(cacheNames = OtpCacheConstants.Others.OTP_PREMIUM_ACC_NUMBER, key = "#key", unless = "#result == null")
    default PremiumAccNumberCacheDTO put(String key, PremiumAccNumberCacheDTO value) {
        return value;
    }

    @Cacheable(cacheNames = OtpCacheConstants.Others.OTP_PREMIUM_ACC_NUMBER, key = "#key", unless = "#result == null")
    default PremiumAccNumberCacheDTO get(String key) {
        return null;
    }

    @CacheEvict(cacheNames = OtpCacheConstants.Others.OTP_PREMIUM_ACC_NUMBER, key = "#key")
    default String evict(String key) {
        return key;
    }
}
