package com.mb.laos.repository.impl;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.mb.laos.model.Referral;
import com.mb.laos.model.User;
import org.apache.commons.collections4.ListUtils;
import org.apache.lucene.search.Sort;
import org.hibernate.search.jpa.FullTextEntityManager;
import org.hibernate.search.jpa.FullTextQuery;
import org.hibernate.search.jpa.Search;
import org.hibernate.search.query.dsl.BooleanJunction;
import org.hibernate.search.query.dsl.QueryBuilder;
import org.hibernate.search.query.dsl.sort.SortFieldContext;
import org.springframework.data.domain.Pageable;
import com.mb.laos.model.LapnetTransactionReport;
import com.mb.laos.model.ResultSet;
import com.mb.laos.model.search.LapnetTransactionReportSearch;
import com.mb.laos.repository.extend.LapnetTransactionReportRepositoryExtend;
import com.mb.laos.util.Constants;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LapnetTransactionReportRepositoryImpl
        implements LapnetTransactionReportRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @SuppressWarnings("unchecked")
    @Override
    public ResultSet<LapnetTransactionReport> searchByKeyword(LapnetTransactionReportSearch params,
                                                              Pageable pageable) {
        FullTextEntityManager fullTextEntityManager =
                Search.getFullTextEntityManager(this.entityManager);

        QueryBuilder queryBuilder = fullTextEntityManager.getSearchFactory().buildQueryBuilder()
                .forEntity(LapnetTransactionReport.class)
                .overridesForField(LapnetTransactionReport.FieldName.CURRENCY, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.FROM_ACCOUNT, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.FROM_USER, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.FROM_MEMBER, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.TRANSACTION_ID, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.PURPOSE, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.REFERENCE_NUMBER, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.TO_ACCOUNT, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.TO_MEMBER, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.TO_TYPE, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.AMOUNT, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.FEE, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .overridesForField(LapnetTransactionReport.FieldName.TYPE, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                .get();

        String keyword = params.getKeyword();

        BooleanJunction<?> mustJunc = queryBuilder.bool();

        if (Validator.isNotNull(params.getFromDate()) && Validator.isNotNull(params.getToDate())) {
            mustJunc = mustJunc.must(queryBuilder.range().onField(LapnetTransactionReport.FieldName.TIME)
                    .from(LocalDateUtil.getStartOfDay(params.getFromDate()))
                    .to(LocalDateUtil.getEndOfDay(params.getToDate())).createQuery());
        }

        if (Validator.isNotNull(params.getType())) {
            mustJunc = mustJunc.must(
                    queryBuilder.keyword().onField(LapnetTransactionReport.FieldName.TYPE).matching(params.getType())
                            .createQuery());
        }

        if (Validator.isNotNull(keyword)) {
            BooleanJunction<?> shouldJunc = queryBuilder.bool();

            shouldJunc = shouldJunc
                    .should(queryBuilder.keyword()
                            .onFields(LapnetTransactionReport.FieldName.CURRENCY,
                                    LapnetTransactionReport.FieldName.FROM_ACCOUNT,
                                    LapnetTransactionReport.FieldName.FROM_MEMBER,
                                    LapnetTransactionReport.FieldName.TRANSACTION_ID,
                                    LapnetTransactionReport.FieldName.PURPOSE,
                                    LapnetTransactionReport.FieldName.REFERENCE_NUMBER,
                                    LapnetTransactionReport.FieldName.TO_ACCOUNT,
                                    LapnetTransactionReport.FieldName.TO_MEMBER,
                                    LapnetTransactionReport.FieldName.FROM_USER,
                                    LapnetTransactionReport.FieldName.TO_TYPE,
                                    LapnetTransactionReport.FieldName.FEE,
                                    LapnetTransactionReport.FieldName.AMOUNT
                            )
                            .matching(keyword.toLowerCase())
                            .createQuery())
                    .should(queryBuilder.keyword()
                            .wildcard()
                            .onFields(LapnetTransactionReport.FieldName.FROM_ACCOUNT,
                                    LapnetTransactionReport.FieldName.TO_ACCOUNT)
                            .matching(keyword.toLowerCase()).createQuery());

            mustJunc = mustJunc.must(shouldJunc.createQuery());
        }

        org.apache.lucene.search.Query query = mustJunc.createQuery();

        FullTextQuery jpaQuery =
                fullTextEntityManager.createFullTextQuery(query, LapnetTransactionReport.class);

        Sort sort = null;

        if (Validator.isNotNull(params.getOrderByColumn())
                && Validator.isNotNull(params.getOrderByType())) {
            SortFieldContext st = queryBuilder.sort()
                    .byField(params.getOrderByColumn());

            sort = params.getOrderByType().equalsIgnoreCase(QueryUtil.DESC) ? st.desc().createSort() : st.asc().createSort();

        } else {
            sort = queryBuilder.sort()
                    .byField(LapnetTransactionReport.FieldName.TIME)
                    .desc()
                    .createSort();
        }

        jpaQuery.setSort(sort);

        int count = jpaQuery.getResultSize();

        if (pageable != null) {
            jpaQuery.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            jpaQuery.setMaxResults(pageable.getPageSize());
        }

        return new ResultSet<>(jpaQuery.getResultList(), count);
    }

    @Override
    public List<LapnetTransactionReport> findAllByTransactionIdIn(List<Long> lapnetIds) {
        String sql = "SELECT e FROM LapnetTransactionReport e where e.transactionId in :transactionIds";

        List<LapnetTransactionReport> result = new ArrayList<>();
        //tach ra vi oracle chi cho phep find in toi da 1000 phan tu
        List<List<Long>> lapnetIdSubList = ListUtils.partition(lapnetIds, 900);
        for (List<Long> ids : lapnetIdSubList) {
            Query query = entityManager.createQuery(sql, LapnetTransactionReport.class);
            Map<String, Object> values = new HashMap<>();
            values.put("transactionIds", ids);
            values.forEach(query::setParameter);
            result.addAll(query.getResultList());
        }
        return result;
    }
}
