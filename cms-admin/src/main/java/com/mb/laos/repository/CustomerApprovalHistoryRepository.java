package com.mb.laos.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.mb.laos.model.CustomerApprovalHistory;

@Repository
public interface CustomerApprovalHistoryRepository extends JpaRepository<CustomerApprovalHistory, Long> {

    /**
     * findAllByClassPk
     *
     * @param classPk Long
     * @return List<CustomerApprovalHistory>
     */
    List<CustomerApprovalHistory> findAllByClassPk(Long classPk);
}
