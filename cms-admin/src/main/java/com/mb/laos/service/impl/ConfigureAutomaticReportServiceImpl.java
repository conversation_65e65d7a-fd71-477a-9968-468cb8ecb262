package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.component.EntrustedHistoryJob;
import com.mb.laos.component.LapnetAutomaticReportJob;
import com.mb.laos.component.UmoneyAutomaticReportJob;
import com.mb.laos.component.UmoneyOnBehalfAutomaticReportJob;
import com.mb.laos.configuration.ValidationProperties;
import com.mb.laos.enums.ConfigureAutomaticReportType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.ConfigureAutomaticReport;
import com.mb.laos.model.EmailRecipients;
import com.mb.laos.model.dto.ConfigureAutomaticReportDTO;
import com.mb.laos.model.dto.EmailRecipientsDTO;
import com.mb.laos.repository.ConfigureAutomaticReportRepository;
import com.mb.laos.repository.EmailRecipientsReportRepository;
import com.mb.laos.service.ConfigureAutomaticReportService;
import com.mb.laos.service.mapper.ConfigureAutomaticReportMapper;
import com.mb.laos.service.mapper.EmailRecipientsMapper;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
@RequiredArgsConstructor
@Log4j
public class ConfigureAutomaticReportServiceImpl implements ConfigureAutomaticReportService {

    private final ConfigureAutomaticReportRepository configureAutomaticReportRepository;

    private final ConfigureAutomaticReportMapper configureAutomaticReportMapper;

    private final EmailRecipientsReportRepository emailRecipientsReportRepository;

    private final EmailRecipientsMapper emailRecipientsMapper;

    private final UmoneyAutomaticReportJob umoneyAutomaticReportJob;
    private final UmoneyOnBehalfAutomaticReportJob umoneyOnBehalfAutomaticReportJob;

    private final LapnetAutomaticReportJob lapnetAutomaticReportJob;

    private final EntrustedHistoryJob entrustedHistoryJob;

    private final ValidationProperties validation;

    @Override
    @Transactional(readOnly = true)
    public ConfigureAutomaticReportDTO getInfo(ConfigureAutomaticReportType type) {

        if (Validator.isNull(type)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        ConfigureAutomaticReportDTO configureAutomaticReportDTO = this.configureAutomaticReportMapper.toDto(this.configureAutomaticReportRepository.findByType(type));

        if (Validator.isNull(configureAutomaticReportDTO)) {
            return null;
        }

        if (Validator.isNotNull(configureAutomaticReportDTO.getTransactionStatus())) {
            List<String> transactionStatuses = Arrays.stream(configureAutomaticReportDTO.getTransactionStatus().split(StringPool.COMMA)).collect(Collectors.toList());
            configureAutomaticReportDTO.setTransactionStatuses(transactionStatuses);
        }

        List<EmailRecipients> emailRecipients = this.emailRecipientsReportRepository.findAllByConfigureAutomaticReportId(configureAutomaticReportDTO.getConfigureAutomaticReportId());

        configureAutomaticReportDTO.setEmailRecipients(emailRecipients);

        return configureAutomaticReportDTO;
    }

    @Override
    public ConfigureAutomaticReportDTO create(ConfigureAutomaticReportDTO dto) {

        ConfigureAutomaticReport configureAutomaticReportExisted = this.configureAutomaticReportRepository
                .findByType(dto.getType());

        if (Validator.isNotNull(configureAutomaticReportExisted)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        this.validateRequestConfigureAutomaticReport(dto);

        if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.UMONEY)
                || Validator.equals(dto.getType(), ConfigureAutomaticReportType.ENTRUSTED_HISTORY)) {
            dto.setTransactionStatus(String.join(StringPool.COMMA, dto.getTransactionStatuses()));
        }

        ConfigureAutomaticReport configureAutomaticReport = this.configureAutomaticReportRepository
                .save(this.configureAutomaticReportMapper.toEntity(dto));

        List<EmailRecipientsDTO> emailRecipients = new ArrayList<>();

        dto.getEmails().forEach(item -> {
            EmailRecipientsDTO emailRecipientsDTO = EmailRecipientsDTO.builder()
                    .email(item)
                    .configureAutomaticReportId(configureAutomaticReport.getConfigureAutomaticReportId())
                    .build();
            emailRecipients.add(emailRecipientsDTO);
        });

        this.emailRecipientsReportRepository.saveAll(this.emailRecipientsMapper.toEntity(emailRecipients));

        try {
            if (Validator.equals(dto.getStatus(), EntityStatus.ACTIVE.getStatus())) {
                switch (dto.getType()) {
                    case UMONEY:
                        this.umoneyAutomaticReportJob.createUmoneyReportJob(dto);
                        break;
                    case LAPNET:
                        this.lapnetAutomaticReportJob.createLapnetReportJob(dto);
                        break;
                    case UMONEY_ON_BEHALF:
                        this.umoneyOnBehalfAutomaticReportJob.createUmoneyOnBehalfReportJob(dto);
                        break;
                    case ENTRUSTED_HISTORY:
                        this.entrustedHistoryJob.createEntrustedReportJob(dto);
                        break;
                }
            }
        } catch (Exception e) {
            _log.error("Create job Automatic Report error", e);
        }

        return dto;
    }

    @Override
    public ConfigureAutomaticReportDTO update(ConfigureAutomaticReportDTO dto) {
        if (Validator.isNull(dto.getConfigureAutomaticReportId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        this.validateRequestConfigureAutomaticReport(dto);

        ConfigureAutomaticReportDTO configureAutomaticReport = this.configureAutomaticReportMapper
                .toDto(this.configureAutomaticReportRepository
                        .findById(dto.getConfigureAutomaticReportId())
                        .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101183)));

        if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.UMONEY)
                || Validator.equals(dto.getType(), ConfigureAutomaticReportType.ENTRUSTED_HISTORY)) {
            configureAutomaticReport.setTransactionStatus(String.join(StringPool.COMMA, dto.getTransactionStatuses()));
        }

        configureAutomaticReport.setContent(dto.getContent());
        configureAutomaticReport.setTitle(dto.getTitle());
        configureAutomaticReport.setSendTime(dto.getSendTime());

        if (Validator.isNotNull(dto.getEmails())) {
            List<EmailRecipients> emailRecipients = this.emailRecipientsReportRepository
                    .findAllByConfigureAutomaticReportId(dto.getConfigureAutomaticReportId());

            this.emailRecipientsReportRepository.deleteAll(emailRecipients);

            List<EmailRecipientsDTO> emailRecipientsDTOS = new ArrayList<>();

            dto.getEmails().forEach(item -> {
                EmailRecipientsDTO emailRecipientsDTO = EmailRecipientsDTO.builder()
                        .email(item)
                        .configureAutomaticReportId(configureAutomaticReport.getConfigureAutomaticReportId())
                        .build();
                emailRecipientsDTOS.add(emailRecipientsDTO);
            });

            this.emailRecipientsReportRepository.saveAll(this.emailRecipientsMapper.toEntity(emailRecipientsDTOS));
        }

        try {
            if (Validator.equals(configureAutomaticReport.getStatus(), EntityStatus.ACTIVE.getStatus())) {
                if (Validator.equals(dto.getStatus(), EntityStatus.INACTIVE.getStatus())) {
                    this.deleteJob(dto);
                } else {
                    this.updateJob(dto);
                }
            } else {
                if (Validator.equals(dto.getStatus(), EntityStatus.ACTIVE.getStatus())) {
                    this.createJob(dto);
                }
            }
        } catch (Exception e) {
            _log.error("update job Umoney error", e);
        }

        configureAutomaticReport.setStatus(dto.getStatus());
        this.configureAutomaticReportRepository.save(this.configureAutomaticReportMapper.toEntity(configureAutomaticReport));
        return configureAutomaticReport;
    }

    private void updateJob(ConfigureAutomaticReportDTO dto) throws SchedulerException {
        switch (dto.getType()) {
            case UMONEY:
                this.umoneyAutomaticReportJob.updateUmoneyReportJob(dto);
                break;
            case LAPNET:
                this.lapnetAutomaticReportJob.updateLapnetReportJob(dto);
                break;
            case UMONEY_ON_BEHALF:
                this.umoneyOnBehalfAutomaticReportJob.updateUmoneyOnBehalfReportJob(dto);
                break;
            case ENTRUSTED_HISTORY:
                this.entrustedHistoryJob.updateEntrustedReportJob(dto);
                break;

            default:
        }
    }

    private void deleteJob(ConfigureAutomaticReportDTO dto) throws SchedulerException {
        switch (dto.getType()) {
            case UMONEY:
                this.umoneyAutomaticReportJob.deleteUmoneyReportJob();
                break;
            case LAPNET:
                this.lapnetAutomaticReportJob.deleteLapnetReportJob();
                break;
            case UMONEY_ON_BEHALF:
                this.umoneyOnBehalfAutomaticReportJob.deleteUmoneyOnBehalfReportJob();
                break;
            case ENTRUSTED_HISTORY:
                this.entrustedHistoryJob.deleteEntrustedReportJob();
                break;

            default:
        }
    }

    private void createJob(ConfigureAutomaticReportDTO dto) throws SchedulerException {
        switch (dto.getType()) {
            case UMONEY:
                this.umoneyAutomaticReportJob.createUmoneyReportJob(dto);
                break;
            case LAPNET:
                this.lapnetAutomaticReportJob.createLapnetReportJob(dto);
                break;
            case UMONEY_ON_BEHALF:
                this.umoneyOnBehalfAutomaticReportJob.createUmoneyOnBehalfReportJob(dto);
                break;
            case ENTRUSTED_HISTORY:
                this.entrustedHistoryJob.createEntrustedReportJob(dto);
                break;

            default:
        }
    }

    private void validateRequestConfigureAutomaticReport(ConfigureAutomaticReportDTO request) {
        if (Validator.isNull(request.getContent())
                || Validator.isNull(request.getTitle())
                || Validator.isNull(request.getSendTime())
                || Validator.isNull(request.getType())
                || Validator.isNull(request.getEmails())) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        boolean emailValidation = request.getEmails().stream().anyMatch(item -> !item.matches(ValidationConstraint.PATTERN.EMAIL_REGEX_V2));
        if (emailValidation) {
            throw new BadRequestAlertException(ErrorCode.MSG1084);
        }
    }
}
