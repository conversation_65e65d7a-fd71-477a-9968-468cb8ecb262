package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.cms.request.PremiumAccNumberCreateRequest;
import com.mb.laos.cms.request.PremiumAccNumberStructureUpdateRequest;
import com.mb.laos.cms.request.StructureCodeRequest;
import com.mb.laos.cms.response.ImportFailedResponse;
import com.mb.laos.cms.response.ImportPremiumAccountNumberStructureResponse;
import com.mb.laos.configuration.TemplateProperties;
import com.mb.laos.enums.CommonCategory;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.exception.StorageException;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.PremiumAccountNumberStructure;
import com.mb.laos.model.dto.PremiumAccountNumberStructureDTO;
import com.mb.laos.model.dto.StructureLengthDTO;
import com.mb.laos.model.search.PremiumAccountNumberStructureSearch;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.PremiumAccountNumberStructureRepository;
import com.mb.laos.request.IdRequest;
import com.mb.laos.service.PremiumAccountNumberStructureService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.PremiumAccountNumberStructureMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.ExcelUtil;
import com.mb.laos.util.FileUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.PremiumAccountNumberStructureImportTemplate;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mb.laos.util.PremiumAccountNumberStructureImportTemplate.FORMAT_TXT;

@Service
@Slf4j
@RequiredArgsConstructor
public class PremiumAccountNumberStructureServiceImpl implements PremiumAccountNumberStructureService {
    private final StorageService storageService;
    private final TemplateProperties templateProperties;
    private final PremiumAccountNumberStructureRepository premiumAccountNumberStructureRepository;
    private final PremiumAccountNumberStructureMapper premiumAccountNumberStructureMapper;
    private final CommonRepository commonRepository;
//    private final NumberGroupRepository numberGroupRepository;

    @Override
    public Page<PremiumAccountNumberStructureDTO> search(PremiumAccountNumberStructureSearch search) {
        long totalElement = this.premiumAccountNumberStructureRepository.count(search);
        if (totalElement == 0L) {
            return Page.empty();
        }

        List<PremiumAccountNumberStructureDTO> premiumAccountNumberStructureDTOS = this.premiumAccountNumberStructureMapper
                .toDto(this.premiumAccountNumberStructureRepository.search(search, PageRequest.of(search.getPageIndex(), search.getPageSize())));

        return new PageImpl<>(premiumAccountNumberStructureDTOS, PageRequest.of(search.getPageIndex(), search.getPageSize()), totalElement);
    }

    @Override
    public PremiumAccountNumberStructureDTO detail(IdRequest idRequest) {
        PremiumAccountNumberStructure premiumAccountNumberStructure = this.premiumAccountNumberStructureRepository
                .findByPremiumAccountNumberStructureIdAndStatusNot(idRequest.getId(), EntityStatus.DELETED.getStatus());

        if (Validator.isNull(premiumAccountNumberStructure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101128);
        }

        return this.premiumAccountNumberStructureMapper.toDto(premiumAccountNumberStructure);
    }

    @Override
    public void delete(IdRequest idRequest) {
        PremiumAccountNumberStructure premiumAccountNumberStructure = this.premiumAccountNumberStructureRepository
                .findByPremiumAccountNumberStructureIdAndStatusNot(idRequest.getId(), EntityStatus.DELETED.getStatus());

        if (Validator.isNull(premiumAccountNumberStructure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101128);
        }
        premiumAccountNumberStructure.setStatus(EntityStatus.DELETED.getStatus());
        this.premiumAccountNumberStructureRepository.save(premiumAccountNumberStructure);
        this.evictCache();
    }

    @Override
    public void lock(IdRequest idRequest) {
        PremiumAccountNumberStructure premiumAccountNumberStructure = this.premiumAccountNumberStructureRepository
                .findByPremiumAccountNumberStructureIdAndStatusNot(idRequest.getId(), EntityStatus.DELETED.getStatus());

        if (Validator.isNull(premiumAccountNumberStructure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101128);
        }

        premiumAccountNumberStructure.setStatus(EntityStatus.INACTIVE.getStatus());
        this.premiumAccountNumberStructureRepository.save(premiumAccountNumberStructure);
        this.evictCache();
    }

    @Override
    public void unlock(IdRequest idRequest) {
        PremiumAccountNumberStructure premiumAccountNumberStructure = this.premiumAccountNumberStructureRepository
                .findByPremiumAccountNumberStructureIdAndStatusNot(idRequest.getId(), EntityStatus.DELETED.getStatus());

        if (Validator.isNull(premiumAccountNumberStructure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101128);
        }

        premiumAccountNumberStructure.setStatus(EntityStatus.ACTIVE.getStatus());
        this.premiumAccountNumberStructureRepository.save(premiumAccountNumberStructure);
        this.evictCache();
    }

    @Override
    public PremiumAccountNumberStructureDTO update(PremiumAccNumberStructureUpdateRequest request) {
        PremiumAccountNumberStructure premiumAccountNumberStructure = this.premiumAccountNumberStructureRepository
                .findByPremiumAccountNumberStructureIdAndStatusNot(request.getPremiumAccountNumberStructureId(), EntityStatus.DELETED.getStatus());

//        Optional<NumberGroup> numberGroup = this.numberGroupRepository.findByNumberGroupIdAndStatus(request.getNumberGroupId(), EntityStatus.ACTIVE.getStatus());
//
//        if (!numberGroup.isPresent()) {
//            throw new BadRequestAlertException(ErrorCode.MSG101129);
//        }
//
//        if (request.getPrice() < numberGroup.get().getMinPrice() || request.getPrice() > numberGroup.get().getMaxPrice()) {
//            throw new BadRequestAlertException(ErrorCode.MSG101195);
//        }

        if (Validator.isNull(premiumAccountNumberStructure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101138);
        }

        if (!Validator.equals(request.getPattern(), premiumAccountNumberStructure.getPattern())) {
            if (premiumAccountNumberStructureRepository.existsByPatternAndStatusNot(request.getPattern(), EntityStatus.DELETED.getStatus())) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_DATA_EXISTED,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_PATTERN)}),
                        PremiumAccountNumberStructure.class.getSimpleName(), LabelKey.ERROR_DATA_EXISTED);
            }
        }

//        premiumAccountNumberStructure.setNumberGroupId(request.getNumberGroupId());
        premiumAccountNumberStructure.setName(Validator.isNotNull(request.getName()) ? request.getName() : premiumAccountNumberStructure.getName());
        premiumAccountNumberStructure.setPrice(Validator.isNotNull(request.getPrice()) ? request.getPrice() : premiumAccountNumberStructure.getPrice());
        premiumAccountNumberStructure.setDiscount(Double.valueOf(request.getDiscount()));
        premiumAccountNumberStructure.setTotalPrice(this.getTotalPrice(request.getPrice(), Double.parseDouble(request.getDiscount())));
        premiumAccountNumberStructure.setPattern(request.getPattern());

        this.evictCache();
        return this.premiumAccountNumberStructureMapper.toDto(this.premiumAccountNumberStructureRepository.save(premiumAccountNumberStructure));
    }

    @Override
    public List<PremiumAccountNumberStructureDTO> structureCodes(StructureCodeRequest request) {
        List<PremiumAccountNumberStructureDTO> list = new ArrayList<>();
        List<PremiumAccountNumberStructure> premiumAccountNumberStructures;

        if (Validator.isNotNull(request.getLengths())) {
            premiumAccountNumberStructures = this.premiumAccountNumberStructureRepository
                    .findAllByLengthInAndStatusNot(request.getLengths(), EntityStatus.DELETED.getStatus());
        } else {
            premiumAccountNumberStructures =
                    this.premiumAccountNumberStructureRepository.findAllByStatusNot(EntityStatus.DELETED.getStatus());
        }

        if (Validator.isNotNull(premiumAccountNumberStructures)) {
            premiumAccountNumberStructures.forEach(premiumAccountNumberStructure -> list.add(
                    new PremiumAccountNumberStructureDTO(premiumAccountNumberStructure.getLength(), premiumAccountNumberStructure.getNumberStructure())));
        }

        return list;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        String resultFileName = String.format(Labels.getLabels(LabelKey.LABEL_TEMPLATE_LUCKY_NUMBER_ACCOUNT_STRUCTURE_FILE_NAME),
                DateUtil.formatStringLongTimestamp(new Date()));

        resultFileName = StringUtil.replace(StringUtil.trimAll(resultFileName), StringPool.SPACE, StringPool.DASH);

//        List<String> numberGroups = this.numberGroupRepository.findAllByStatus(EntityStatus.ACTIVE.getStatus())
//                .stream().map(NumberGroup::getName).collect(Collectors.toList());

        List<String> commons = this.commonRepository.findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus())
                .stream().map(Common::getCode).collect(Collectors.toList());

        // format tail file excel .xlsx
        response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
        // set fileName
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + resultFileName);

        try (InputStream inputStream = this.storageService.getExcelTemplateFromResource(this.templateProperties.getPremiumAccountNumberStructure().getTemplateFile());
             ByteArrayOutputStream baos = new ByteArrayOutputStream();
             OutputStream os = response.getOutputStream()) {

            // Bước 1: Xử lý template bằng Jxls và lưu vào ByteArrayOutputStream
            Context context = new Context();
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.LENGTH, Labels.getLabels(LabelKey.LABEL_LENGTH));
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_STRUCTURE, Labels.getLabels(LabelKey.LABEL_NUMBER_STRUCTURE));
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NAME_STRUCTURE, Labels.getLabels(LabelKey.LABEL_STRUCTURE_NAME));
//            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_GROUP, Labels.getLabels(LabelKey.LABEL_NUMBER_GROUP));
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.PRICE,
                    Labels.getLabels(LabelKey.LABEL_PRICE) + StringPool.SPACE + StringPool.OPEN_PARENTHESIS +
                            CurrencyType.LAK.name() + StringPool.CLOSE_PARENTHESIS);
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.DISCOUNT, Labels.getLabels(LabelKey.LABEL_DISCOUNT));
            context.putVar(PremiumAccountNumberStructureImportTemplate.Header.PATTERN, Labels.getLabels(LabelKey.LABEL_PATTERN));
            context.putVar("localDateNow", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
            context.putVar("title", Labels.getLabels(LabelKey.LABEL_PREMIUM_NUMBER_STRUCTURE_TITLE));
            context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
            context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));

            JxlsHelper.getInstance().processTemplate(inputStream, baos, context);

            try (Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(baos.toByteArray()))) {
                Sheet sheet = workbook.getSheetAt(0);

                DataValidationHelper validationHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);

                String[] lengthArray = commons.toArray(new String[0]);

                CellRangeAddressList lengthAddressList = new CellRangeAddressList(10, 500, 1, 1);
                DataValidationConstraint lengthConstraint = validationHelper.createExplicitListConstraint(lengthArray);
                DataValidation lengthDataValidation = validationHelper.createValidation(lengthConstraint, lengthAddressList);

                lengthDataValidation.setSuppressDropDownArrow(true);
                lengthDataValidation.setShowErrorBox(true);
                lengthDataValidation.setEmptyCellAllowed(false);
                sheet.addValidationData(lengthDataValidation);

//                String[] numberGroupArray = numberGroups.toArray(new String[0]);
//
//                CellRangeAddressList addressList = new CellRangeAddressList(10, 500, 4, 4);
//                DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(numberGroupArray);
//                DataValidation dataValidation = validationHelper.createValidation(constraint, addressList);
//
//                dataValidation.setSuppressDropDownArrow(true);
//                dataValidation.setShowErrorBox(true);
//                dataValidation.setEmptyCellAllowed(false);
//                sheet.addValidationData(dataValidation);

                workbook.write(os);
            }
            response.flushBuffer();
        } catch (IOException e) {
            _log.error("Error occurred when when download template excel", e);
        }
    }

    @Override
    public void importStructures(HttpServletResponse response, MultipartFile file) {
        try {
            boolean isValidExcel = ExcelUtil.isSameTemplate(file, this.storageService.getExcelTemplateFromResource(this.templateProperties.getPremiumAccountNumberStructure().getTemplateFile()));
            if (!isValidExcel) {
                throw new BadRequestAlertException(ErrorCode.MSG100182);
            }

            ImportPremiumAccountNumberStructureResponse premiumAccNumberStructureResponse = this.readExcelFile(file);

            if (Validator.isNotNull(premiumAccNumberStructureResponse.getPremiumAccountNumberStructureDTOS())
                    && premiumAccNumberStructureResponse.isSuccess()) {

                premiumAccNumberStructureResponse.getPremiumAccountNumberStructureDTOS().forEach(item -> {
                    item.setStatus(EntityStatus.ACTIVE.getStatus());
                    item.setTotalPrice(this.getTotalPrice(item.getPrice(), item.getDiscount()));
                });
                List<PremiumAccountNumberStructure> premiumAccountNumberStructures = premiumAccountNumberStructureMapper
                        .toEntity(premiumAccNumberStructureResponse.getPremiumAccountNumberStructureDTOS());
                this.premiumAccountNumberStructureRepository.saveAll(premiumAccountNumberStructures);

                this.evictCache();
            }

            if (!premiumAccNumberStructureResponse.isSuccess()) {
                // read failed
                // Extract failed error cause
                StringBuilder content = new StringBuilder();
                content.append(String.format(FORMAT_TXT, Labels.getLabels(LabelKey.LABEL_SERIAL), Labels.getLabels(LabelKey.LABEL_FIELD), Labels.getLabels(LabelKey.LABEL_ERROR_DETAIL)));
                content.append(StringPool.NEW_LINE);

                premiumAccNumberStructureResponse.getImportFailedResponses().forEach(failed -> {
                    AtomicBoolean isFirstError = new AtomicBoolean(true);
                    failed.getErrorMessage().forEach((key, value) -> value.forEach(error -> {
                        if (isFirstError.get()) {
                            content.append(String.format(FORMAT_TXT, failed.getSerial(), key, error));
                            isFirstError.set(false);
                        } else {
                            content.append(String.format(FORMAT_TXT, StringPool.BLANK, key, error));
                        }
                    }));
                    content.append(StringPool.NEW_LINE);
                });
                throw new BadRequestAlertException(ErrorCode.MSG101058, content.toString());
            }
        } catch (IOException | StorageException e) {
            _log.error("Error occurred when import structure ", e);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<StructureLengthDTO> numberGroups() {
        List<StructureLengthDTO> numberGroups = new ArrayList<>();
        List<Common> commons = this.commonRepository.findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(commons)) {
            commons.sort(Comparator.comparing(common -> Integer.parseInt(common.getValue())));
            commons.forEach(item -> numberGroups.add(new StructureLengthDTO(Integer.valueOf(item.getValue()), Labels.getLabels(LabelKey.LABEL_NUMBER_GROUP_VALUE, new Object[]{item.getValue()}))));
        }

        return numberGroups;
    }

    private ImportPremiumAccountNumberStructureResponse readExcelFile(MultipartFile file) throws IOException {
        // 1. Validate excel File
        if (Validator.isNull(file)) {
            throw new BadRequestAlertException(ErrorCode.MSG100182);
        }

        if (!Arrays.asList(FileUtil.XLS, FileUtil.XLSX).contains(FileUtil.getFileExtension(FileUtil.sanitizeFilename(file.getOriginalFilename())))) {
            throw new BadRequestAlertException(ErrorCode.MSG101053);
        }

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        // 3. Get data from rows
        List<String> headers = PremiumAccountNumberStructureImportTemplate.headers;
        List<PremiumAccountNumberStructureDTO> premiumAccNumberStructureSuccessList = new ArrayList<>();
        List<ImportFailedResponse> importFailedResponseList = new ArrayList<>();

        Map<String, Set<String>> checkDuplicate = new HashMap<>();
        checkDuplicate.put(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_STRUCTURE, new HashSet<>());
        checkDuplicate.put(PremiumAccountNumberStructureImportTemplate.Header.PATTERN, new HashSet<>());
        checkDuplicate.put(PremiumAccountNumberStructureImportTemplate.Header.SERIAL, new HashSet<>());

        List<Common> commons = this.commonRepository.findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus());

        int rowIndex = 0;

        for (Row row : sheet) {
            PremiumAccountNumberStructureDTO premiumAccountNumberStructureDTO = new PremiumAccountNumberStructureDTO();
            ImportFailedResponse importFailedResponse = new ImportFailedResponse();
            Map<String, List<String>> errorMessage = new HashMap<>();

            if (rowIndex == PremiumAccountNumberStructureImportTemplate.ROW_NUM_START) {
                // Validate template
                if (row.getLastCellNum() != PremiumAccountNumberStructureImportTemplate.MAX_COL_SIZE) {
                    throw new BadRequestAlertException(ErrorCode.MSG100182);
                }

                for (Cell cell : row) {
                    String value = ExcelUtil.readCellContent(cell);

                    if (!value.contains(headers.get(cell.getColumnIndex()))) {
                        throw new BadRequestAlertException(ErrorCode.MSG100182);
                    }
                }
            } else if (rowIndex > PremiumAccountNumberStructureImportTemplate.ROW_NUM_START) {
                // Read data, if no data found -> throw exception
                if (ExcelUtil.isEmptyAllCell(row)) {
                    continue;
                }

                this.readDataFromRow(row, premiumAccountNumberStructureDTO, importFailedResponse, errorMessage, checkDuplicate, commons);

                if (importFailedResponse.getErrorMessage().size() > 0) {
                    importFailedResponseList.add(importFailedResponse);
                }
                if ((importFailedResponseList.size() == 0)) {
                    premiumAccNumberStructureSuccessList.add(premiumAccountNumberStructureDTO);
                }
            }
            rowIndex++;
        }
        ImportPremiumAccountNumberStructureResponse importPremiumAccountNumberStructureResponse = new ImportPremiumAccountNumberStructureResponse();
        importPremiumAccountNumberStructureResponse.setSuccess(Validator.isNull(importFailedResponseList));

        importPremiumAccountNumberStructureResponse.setPremiumAccountNumberStructureDTOS(premiumAccNumberStructureSuccessList);
        if (!importPremiumAccountNumberStructureResponse.isSuccess()) {
            importPremiumAccountNumberStructureResponse.setImportFailedResponses(importFailedResponseList);
        }
        return importPremiumAccountNumberStructureResponse;
    }

    private void readDataFromRow(Row row, PremiumAccountNumberStructureDTO dto, ImportFailedResponse importFailedResponse, Map<String, List<String>> errorMessage, Map<String, Set<String>> checkDuplicate, List<Common> commons) {
        List<String> headers = PremiumAccountNumberStructureImportTemplate.headers;
//        List<NumberGroup> numberGroups = this.numberGroupRepository.findAllByStatus(EntityStatus.ACTIVE.getStatus());

        for (Cell cell : row) {
            if (cell.getColumnIndex() >= PremiumAccountNumberStructureImportTemplate.MAX_COL_SIZE) {
                break;
            }
            String value = StringUtil.removeDecimalPart(ExcelUtil.readCellContent(cell));

            String header = headers.get(cell.getColumnIndex());
            List<String> errorDetail = new ArrayList<>();
            switch (cell.getColumnIndex()) {
                case 0:
                    try {
                        if (Validator.isNull(value)) {
                            importFailedResponse.setSerial(value + "(row " + (cell.getRowIndex() + 1) + ")");
                        } else {
                            importFailedResponse.setSerial(value);
                            // check serial already exist in File
                            if (!checkDuplicate.get(PremiumAccountNumberStructureImportTemplate.Header.SERIAL).add(value)) {
                                errorDetail.add(Labels.getLabels(LabelKey.ERROR_DATA_IS_DUPLICATE, new Object[]{header}));
                                importFailedResponse.setSerial(value + "(row " + (cell.getRowIndex() + 1) + ")");
                            } else {
                                checkDuplicate.get(PremiumAccountNumberStructureImportTemplate.Header.SERIAL).add(value);
                                int serial = cell.getRowIndex() + 1;

                                if (serial > ValidationConstraint.LENGTH.SERIAL_MAX_LENGTH) {
                                    errorDetail.add(Labels.getLabels(LabelKey.ERROR_EXCEED_MAX_LENGTH, new Object[]{header, String.valueOf(ValidationConstraint.LENGTH.SERIAL_MAX_LENGTH).length()}));
                                }
                            }
                        }
                    } catch (NumberFormatException ex) {
                        errorDetail.add(Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT, new Object[]{header}));
                    } finally {
                        if (errorDetail.size() != 0) {
                            errorMessage.put(header, errorDetail);
                        }
                    }
                    break;

                // col A - Độ dài
                case 1:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
                    } else {
                        String length = value;
                        Optional<Common> common = commons.stream().filter(item -> Validator.equals(item.getCode(), length)).findFirst();
                        if (!common.isPresent()) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_GROUP_IS_INVALID));
                        }
                    }
                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }
                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setLength(Integer.parseInt(value));
                    }
                    break;
                // col B - Cấu trúc số
                case 2:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header}));
                    } else {
                        if (this.premiumAccountNumberStructureRepository.existsByNumberStructureAndStatusNot(value.toLowerCase(), EntityStatus.DELETED.getStatus())) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_DATA_EXISTED, new Object[]{header}));
                        }

                        if (!checkDuplicate.get(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_STRUCTURE).add(value)) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_DATA_IS_DUPLICATE, new Object[]{header}));
                        }

                        if (value.length() > ValidationConstraint.LENGTH.LUCKY_ACCOUNT_NUMBER.NUMBER_STRUCTURE) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_STRUCTURE_IS_MAX_LENGTH));
                        }
                    }
                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }
                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setNumberStructure(value);
                    }
                    break;
                // col C - Tên cấu trúc số
                case 3:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
                    }

                    if (Validator.isNotNull(value)) {
                        if (value.length() > ValidationConstraint.LENGTH.NUMBER_STRUCTURE_NAME_MAX_LENGTH) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_EXCEED_MAX_LENGTH, new Object[]{header, String.valueOf(ValidationConstraint.LENGTH.NUMBER_STRUCTURE_NAME_MAX_LENGTH)}));
                        }

                        if (!value.matches(ValidationConstraint.PATTERN.NO_SPECIAL_CHARACTERS)) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_STRUCTURE_NAME_IS_INVALID));
                        }
                    }

                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }
                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setName(value);
                    }
                    break;
                // col D - Nhóm số
//                case 4:
//                    if (Validator.isNull(value)) {
//                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
//                    } else {
//                        if (Validator.isNull(numberGroups)) {
//                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_GROUP_IS_INVALID));
//                        } else {
//                            String name = value;
//                            NumberGroup numberGroup = numberGroups.stream().filter(item -> Validator.equals(item.getName(), name)).findFirst().orElse(null);
//
//                            if (Validator.isNull(numberGroup)) {
//                                errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_GROUP_IS_INVALID));
//                            }
//
//                            if (errorMessage.size() == 0 && errorDetail.size() == 0) {
//                                dto.setNumberGroupId(numberGroup.getNumberGroupId());
//                            }
//                        }
//                    }
//
//                    if (errorDetail.size() != 0) {
//                        errorMessage.put(header, errorDetail);
//                    }
//                    break;
                // col E - Giá
                case 4:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
                    } else {
                        if (!value.matches(ValidationConstraint.PATTERN.LONG_REGEX)) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_INVALID_DATA, new Object[]{header.toLowerCase()}));
                        }
//                        else {
//                            long price = (long) Double.parseDouble(value);
//
//                            if (Validator.isNotNull(dto.getNumberGroupId())) {
//                                NumberGroup numberGroup = numberGroups.stream().filter(item -> dto.getNumberGroupId().equals(item.getNumberGroupId())).findFirst().orElse(null);
//
//                                if (Validator.isNull(numberGroup)) {
//                                    errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_GROUP_IS_INVALID));
//                                } else {
//                                    if (price < numberGroup.getMinPrice() || price > numberGroup.getMaxPrice()) {
//                                        errorDetail.add(Labels.getLabels(LabelKey.ERROR_NUMBER_GROUP_NOT_MATCH));
//                                    }
//                                }
//                            }
//                        }
                    }

                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }

                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setPrice(Validator.isNotNull(value) ? (long) Double.parseDouble(value) : 0L);
                    }

                    break;
                // col F - Chiết khấu
                case 5:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
                    } else {
                        if (!value.contains(StringPool.PERIOD) && value.startsWith(StringPool.NUMBER_0) && !Validator.equals(value, StringPool.NUMBER_0)) {
                            value = value.replaceFirst(StringPool.NUMBER_0, StringPool.BLANK);
                        }

                        if (!value.matches(ValidationConstraint.PATTERN.DISCOUNT_REGEX) || Double.parseDouble(value) > Integer.parseInt(StringPool.MAX_PERCENT)) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_INVALID_DATA, new Object[]{header.toLowerCase()}));
                        }
                    }
                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }
                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setDiscount(Double.valueOf(Validator.isNotNull(value) ? value : String.valueOf(0)));
                    }
                    break;
                // col H - Pattern
                case 6:
                    if (Validator.isNull(value)) {
                        errorDetail.add(Labels.getLabels(LabelKey.LABEL_ERROR_DATA_IS_REQUIRED, new Object[]{header.toLowerCase()}));
                    } else {
                        if (!checkDuplicate.get(PremiumAccountNumberStructureImportTemplate.Header.PATTERN).add(value)) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_DATA_IS_DUPLICATE, new Object[]{header.toLowerCase()}));
                        }

                        if (Validator.isNotNull(dto.getLength()) &&
                                this.premiumAccountNumberStructureRepository.existsByPatternAndLengthAndStatusNot(value, dto.getLength(), EntityStatus.DELETED.getStatus())) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_DATA_EXISTED, new Object[]{header.toLowerCase()}));
                        }
                        if (value.length() > ValidationConstraint.LENGTH.LUCKY_ACCOUNT_NUMBER.PATTERN_MAX_LENGTH) {
                            errorDetail.add(Labels.getLabels(LabelKey.ERROR_EXCEED_MAX_LENGTH, new Object[]{header, String.valueOf(ValidationConstraint.LENGTH.LUCKY_ACCOUNT_NUMBER.PATTERN_MAX_LENGTH)}));
                        }
                    }

                    if (errorDetail.size() != 0) {
                        errorMessage.put(header, errorDetail);
                    }
                    if (errorMessage.size() == 0 && errorDetail.size() == 0) {
                        dto.setPattern(value);
                    }
                    break;
            }
            importFailedResponse.setErrorMessage(errorMessage);
        }
    }

    @Override
    public PremiumAccountNumberStructureDTO create(PremiumAccNumberCreateRequest request) {
        PremiumAccountNumberStructure structure = this.premiumAccountNumberStructureRepository
                .findByNumberStructureAndStatusNot(request.getNumberStructure(), EntityStatus.DELETED.getStatus());

        if (this.premiumAccountNumberStructureRepository.existsByPatternAndStatusNot(request.getPattern(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_EXISTED,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PATTERN)}),
                    PremiumAccountNumberStructure.class.getSimpleName(), LabelKey.ERROR_DATA_EXISTED);
        }

//        Optional<NumberGroup> numberGroup = this.numberGroupRepository.findByNumberGroupIdAndStatus(request.getNumberGroupId(), EntityStatus.ACTIVE.getStatus());
//
//        if (!numberGroup.isPresent()) {
//            throw new BadRequestAlertException(ErrorCode.MSG101129);
//        }
//
//        if (request.getPrice() < numberGroup.get().getMinPrice() || request.getPrice() > numberGroup.get().getMaxPrice()) {
//            throw new BadRequestAlertException(ErrorCode.MSG101195);
//        }

        if (Validator.isNotNull(structure)) {
            throw new BadRequestAlertException(ErrorCode.MSG101194);
        }

        List<Common> commons = this.commonRepository.findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus());
        List<String> lengthStructure = commons.stream().map(Common::getCode).collect(Collectors.toList());
        boolean isNumberGroup = lengthStructure.contains(String.valueOf(request.getLength()));

        if (!isNumberGroup) {
            throw new BadRequestAlertException(ErrorCode.MSG101129);
        }

        PremiumAccountNumberStructureDTO dto = new PremiumAccountNumberStructureDTO();
        dto.setNumberStructure(request.getNumberStructure());
        dto.setName(request.getName());
        dto.setDiscount(Double.valueOf(request.getDiscount()));
        dto.setPattern(request.getPattern());
//        dto.setNumberGroupId(request.getNumberGroupId());
        dto.setPrice(request.getPrice());
        dto.setLength(request.getLength());
        dto.setTotalPrice(this.getTotalPrice(request.getPrice(), Double.parseDouble(request.getDiscount())));
        dto.setStatus(EntityStatus.ACTIVE.getStatus());

        PremiumAccountNumberStructure premiumAccNumberStructure = this.premiumAccountNumberStructureRepository
                .save(this.premiumAccountNumberStructureMapper.toEntity(dto));

        this.evictCache();
        return this.premiumAccountNumberStructureMapper.toDto(premiumAccNumberStructure);
    }

    private Long getTotalPrice(Long price, double discount) {
        if (Validator.isNotNull(price)) {
            return Math.round(price - (price * discount) / 100);
        }
        return 0L;
    }

    @Override
    public void export(HttpServletResponse response, PremiumAccountNumberStructureSearch request) {
        try {
            request.setHasPageable(false);
//            List<NumberGroup> numberGroups = this.numberGroupRepository.findAllByStatusNot(EntityStatus.DELETED.getStatus());
            List<PremiumAccountNumberStructureDTO> structures = this.premiumAccountNumberStructureMapper
                    .toDto(this.premiumAccountNumberStructureRepository.search(request, null));
            String sourceZone = Labels.getTimeZoneFromRequest();

            structures.forEach(structure -> {
                if (Validator.isNotNull(structure.getLastModifiedDate())) {
                    String lastModifiedDateStr = InstantUtil.formatStringLongDate(structure.getLastModifiedDate(), ZoneId.of(sourceZone));
                    structure.setLastModifiedDateStr(lastModifiedDateStr);
                }
                if (Validator.isNotNull(structure.getCreatedDate())) {
                    String createdDateStr = InstantUtil.formatStringLongDate(structure.getCreatedDate(), ZoneId.of(sourceZone));
                    structure.setCreatedDateStr(createdDateStr);
                }

                if (Validator.isNotNull(structure.getPrice())) {
                    structure.setPriceStr(StringUtil.formatMoney(structure.getPrice()));
                }

//                if (Validator.isNotNull(numberGroups) && Validator.isNotNull(structure.getNumberGroupId())) {
//                    NumberGroup numberGroup = numberGroups.stream().filter(item -> structure.getNumberGroupId().equals(item.getNumberGroupId())).findFirst().orElse(null);
//                    if (Validator.isNotNull(numberGroup)) {
//                        structure.setNumberGroup(numberGroup.getName());
//                    }
//                }

            });

            String resultFileName = String.format(Labels.getLabels(LabelKey.LABEL_TEMPLATE_LUCKY_NUMBER_ACCOUNT_STRUCTURE_FILE_NAME),
                    DateUtil.formatStringLongTimestamp(new Date()));
            resultFileName = StringUtil.replace(StringUtil.trimAll(resultFileName), StringPool.SPACE, StringPool.DASH);

            try (InputStream inputStream = this.storageService.getExcelTemplateFromResource(this.templateProperties.getLuckyNumberAccountStructure().getTemplateFile())) {

                try (OutputStream os = response.getOutputStream()) {
                    Context context = new Context();
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.LENGTH, Labels.getLabels(LabelKey.LABEL_LENGTH));
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_STRUCTURE, Labels.getLabels(LabelKey.LABEL_NUMBER_STRUCTURE));
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NAME_STRUCTURE, Labels.getLabels(LabelKey.LABEL_STRUCTURE_NAME));
//                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.NUMBER_GROUP, Labels.getLabels(LabelKey.LABEL_NUMBER_GROUP));
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.PRICE, Labels.getLabels(LabelKey.LABEL_ORIGINAL_PRICE));
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.DISCOUNT, Labels.getLabels(LabelKey.LABEL_DISCOUNT));
                    context.putVar(PremiumAccountNumberStructureImportTemplate.Header.PATTERN, Labels.getLabels(LabelKey.LABEL_PATTERN));
                    context.putVar("localDateNow", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
                    context.putVar("title", Labels.getLabels(LabelKey.LABEL_PREMIUM_NUMBER_STRUCTURE_TITLE));
                    context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
                    context.putVar("lastModifiedBy", Labels.getLabels(LabelKey.LABEL_LAST_MODIFIED_BY));
                    context.putVar("lastModifiedDate", Labels.getLabels(LabelKey.LABEL_LAST_MODIFIED_DATE));
                    context.putVar("createdBy", Labels.getLabels(LabelKey.LABEL_CREATE_BY));
                    context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));
                    context.putVar("createdDate", Labels.getLabels(LabelKey.LABEL_VERSION_CREATED_DATE));

                    context.putVar("lists", structures);

                    // format tail file excel .xlsx
                    response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
                    // set fileName
                    response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + resultFileName);
                    JxlsHelper.getInstance().processTemplate(inputStream, os, context);
                    response.flushBuffer();
                }
            } catch (IOException e) {
                _log.error("Error occurred when export when export excel", e);
            }

        } catch (Exception e) {
            _log.error("Export excel error", e);
        }
    }

    public void evictCache() {
        List<Common> commons = this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(commons)) {
            commons.forEach(item -> this.premiumAccountNumberStructureRepository
                    .evictFindAllByLengthAndStatus(Integer.parseInt(item.getValue())));
        }
    }
}
