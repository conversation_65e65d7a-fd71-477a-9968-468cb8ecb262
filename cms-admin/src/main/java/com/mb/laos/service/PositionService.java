package com.mb.laos.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.mb.laos.model.dto.PositionDTO;
import com.mb.laos.model.search.PositionSearch;

public interface PositionService {

    /**
     * searchByKeyword
     *
     * @param params   PositionSearch
     * @return Page<PositionDTO>
     */
    Page<PositionDTO> searchByKeyword(PositionSearch params);

    /**
     * findAllPosition
     *
     * @return List<PositionDTO>
     */
    List<PositionDTO> findAllPosition();

    /**
     * Get information position
     *
     * @param positionId PositionDTO
     */
    PositionDTO getPositionInfo(Long positionId);

    /**
     * create Position
     *
     * @param positionDTO PositionDTO
     * @return PositionDTO
     */
    PositionDTO create(PositionDTO positionDTO);

    /**
     * update Position
     *
     * @param positionDTO PositionDTO
     * @return PositionDTO
     */
    PositionDTO update(PositionDTO positionDTO);

    /**
     * lock -> update status to INACTIVE (1)
     *
     * @param positionId Long
     */
    void lock(Long positionId);

    /**
     * unlock -> update status to ACTIVE (1)
     *
     * @param positionId Long
     */
    void unlock(Long positionId);

    /**
     * delete -> update status to DELETE (-1)
     *
     * @param positionId Long
     */
    void delete(Long positionId);
}
