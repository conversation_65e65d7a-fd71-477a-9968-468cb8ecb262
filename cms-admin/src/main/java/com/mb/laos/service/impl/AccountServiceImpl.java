package com.mb.laos.service.impl;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.util.HttpUtil;
import com.mb.laos.cms.request.LoginRequest;
import com.mb.laos.cms.response.TokenResponse;
import com.mb.laos.configuration.ClientProperties;
import com.mb.laos.configuration.ValidationProperties;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.Gender;
import com.mb.laos.enums.OtpType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Department;
import com.mb.laos.model.Position;
import com.mb.laos.model.Role;
import com.mb.laos.model.User;
import com.mb.laos.model.UserLogin;
import com.mb.laos.model.UserRole;
import com.mb.laos.model.dto.AccountDTO;
import com.mb.laos.model.dto.UserDTO;
import com.mb.laos.repository.DepartmentRepository;
import com.mb.laos.repository.OtpRepository;
import com.mb.laos.repository.PositionRepository;
import com.mb.laos.repository.RoleRepository;
import com.mb.laos.repository.UserRepository;
import com.mb.laos.repository.UserRoleRepository;
import com.mb.laos.security.RsaProvider;
import com.mb.laos.security.UserPrincipal;
import com.mb.laos.security.jwt.JWTAccessToken;
import com.mb.laos.security.jwt.JWTToken;
import com.mb.laos.security.jwt.JWTTokenProvider;
import com.mb.laos.security.request.OtpVerifyRequest;
import com.mb.laos.security.request.PasswordRequest;
import com.mb.laos.security.response.OtpVerifyResponse;
import com.mb.laos.security.response.PasswordVerifyResponse;
import com.mb.laos.security.util.CmsSecurityUtils;
import com.mb.laos.security.util.SecurityConstants;
import com.mb.laos.service.AccountService;
import com.mb.laos.service.KaptchaService;
import com.mb.laos.service.LoginAttemptService;
import com.mb.laos.service.OtpService;
import com.mb.laos.service.PasswordVerifyService;
import com.mb.laos.service.UserLoginService;
import com.mb.laos.service.mapper.AccountMapper;
import com.mb.laos.service.mapper.UserMapper;
import com.mb.laos.util.GetterUtil;
import com.mb.laos.util.ReflectionUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class AccountServiceImpl implements AccountService {

    private final UserRepository userRepository;

    private final UserRoleRepository userRoleRepository;

    private final PasswordEncoder passwordEncoder;

    private final UserMapper userMapper;

    private final AccountMapper accountMapper;

    private final OtpRepository otpRepository;

    private final RoleRepository roleRepository;

    private final PasswordVerifyService passwordVerifyService;

    private final ValidationProperties validationProperties;

    private final AuthenticationManagerBuilder authenticationManagerBuilder;

    private final JWTTokenProvider<UserPrincipal> jwtTokenProvider;

    private final RsaProvider rsaProvider;

    private final LoginAttemptService loginAttemptService;

    private final OtpService otpService;

    private final UserLoginService userLoginService;

    private final KaptchaService kaptchaService;

    private final ClientProperties clientProperties;

    private final DepartmentRepository departmentRepository;

    private final PositionRepository positionRepository;

    @Override
    public ResponseEntity<TokenResponse> authorize(HttpServletRequest request, HttpServletResponse response,
                                                   LoginRequest loginRequest) {
        String username = GetterUtil.getString(loginRequest.getUsername());
        String password = GetterUtil.getString(loginRequest.getPassword());
        String deviceToken = GetterUtil.getString(loginRequest.getDeviceToken());

        String ip = HttpUtil.getClientIP(request);

        UserLogin loginLog = UserLogin.builder().username(username).ip(ip).loginTime(Instant.now()).build();

        try {
            User user = this.findByUsername(username);

            if (Validator.isNull(user) || !Validator.equals(EntityStatus.ACTIVE.getStatus(), user.getStatus())) {
                throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD),
                        User.class.getSimpleName(), LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD);
            }

            password = this.rsaProvider.decrypt(password);

            UsernamePasswordAuthenticationToken authenticationToken =
                    new UsernamePasswordAuthenticationToken(username, password);

            Authentication authentication =
                    this.authenticationManagerBuilder.getObject().authenticate(authenticationToken);

            SecurityContextHolder.getContext().setAuthentication(authentication);

            this.updateUserDeviceToken(user, deviceToken);

			boolean rememberMe = Validator.isNotNull(loginRequest.getRememberMe()) && loginRequest.getRememberMe();

            JWTAccessToken accessToken = this.jwtTokenProvider.createAccessToken(username, rememberMe);

            JWTToken refreshToken = this.jwtTokenProvider.createRefreshToken(username);

            HttpHeaders httpHeaders = new HttpHeaders();

            TokenResponse tokenResponse = TokenResponse.builder().type(SecurityConstants.Header.BEARER_START.trim())
                    .token(accessToken.getCsrfToken().getToken()).duration(accessToken.getCsrfToken().getDuration())
                    .changePwRequired(Validator.isNotNull(user.getPwExpiredTime())
                            && user.getPwExpiredTime().isBefore(LocalDateTime.now()))
                    .build();

            // save login success
            loginLog.setSuccess(true);
            loginLog.setDescription(Labels.getLabels(LabelKey.MESSAGE_LOGIN_SUCCESSFUL));

            // clear login failed attempt
            this.loginAttemptService.loginSucceeded(ip);

            HttpCookie accessCookie = this.jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.ACCESS_TOKEN,
                    accessToken.getAccessToken().getToken(), accessToken.getAccessToken().getDuration());

            HttpCookie refreshCookie = this.jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.REFRESH_TOKEN,
                    refreshToken.getToken(), refreshToken.getDuration());

            HttpCookie rememberMeCookie = this.jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.REMEMBER_ME,
                    String.valueOf(rememberMe), accessToken.getAccessToken().getDuration());

            httpHeaders.add(HttpHeaders.SET_COOKIE, accessCookie.toString());
            httpHeaders.add(HttpHeaders.SET_COOKIE, refreshCookie.toString());
            httpHeaders.add(HttpHeaders.SET_COOKIE, rememberMeCookie.toString());

            return new ResponseEntity<>(tokenResponse, httpHeaders, HttpStatus.OK);
        } catch (Exception e) {
            _log.error("Admin login failure with user name {}, exception: {}", username, e);

            // save login failure
            loginLog.setSuccess(false);
            loginLog.setDescription(Labels.getLabels(LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD));

            this.loginAttemptService.loginFailed(ip);

            Map<String, Object> params =
                    this.loginAttemptService.isRequiredCaptcha(ip) ? this.kaptchaService.generateRequired()
                            : new HashMap<>();

            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD, params);
        } finally {
            this.userLoginService.save(loginLog);
        }
    }

    @Override
    public void changePassword(PasswordRequest request) {
        User user = this.getUserLogin();

        String currentPassword = request.getCurrentPassword();
        String newPassword = request.getPassword();
        String confirmPassword = request.getConfirmPassword();

        if (Validator.isNull(currentPassword)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_CURRENT_PASSWORD_IS_MISSING),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_CURRENT_PASSWORD_IS_MISSING);
        }

        if (Validator.isNull(newPassword)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_NEW_PASSWORD_IS_MISSING),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_NEW_PASSWORD_IS_MISSING);
        }

        if (Validator.isNull(confirmPassword)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CONFIRM_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // decode password
        try {
            currentPassword = this.rsaProvider.decrypt(currentPassword);
            newPassword = this.rsaProvider.decrypt(newPassword);
            confirmPassword = this.rsaProvider.decrypt(confirmPassword);

        } catch (Exception e) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        if (!this.passwordEncoder.matches(currentPassword, user.getPassword())) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_CURRENT_PASSWORD_IS_INCORRECT),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_CURRENT_PASSWORD_IS_INCORRECT);
        }

        if (!Validator.equals(newPassword, confirmPassword)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_CONFIRM_PASSWORD_IS_NOT_MATCH),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_CONFIRM_PASSWORD_IS_NOT_MATCH);
        }

        // độ dài mật khẩu
        if (newPassword.length() < this.validationProperties.getPasswordMinLength()
                || newPassword.length() > this.validationProperties.getPasswordMaxLength()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_INVALID_LENGTH,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD),
                                    this.validationProperties.getPasswordMinLength(),
                                    this.validationProperties.getPasswordMaxLength()}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_INVALID_LENGTH);
        }

        user.setPassword(this.passwordEncoder.encode(newPassword));

        LocalDateTime expiredTime = LocalDateTime.now().plusDays(this.clientProperties.getPwDuration());

        user.setPwExpiredTime(expiredTime);

        this.userRepository.save(user);
    }

    @Override
    public void changePhoneNumber(OtpVerifyRequest request, HttpServletResponse response) {
        User user = this.getUserLogin();

        String phoneNumber = request.getPhoneNumber();

        if (Validator.isNull(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // validate otp
		this.otpService.validateOtp(phoneNumber, request.getTransactionId(), OtpType.CHANGE_PHONE_NUMBER,
				request.getOtp());

        // check sđt đã dùng hay chưa

        if (this.userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        // check trùng username
        if (this.userRepository.existsByUsername(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        User oldUser = ReflectionUtil.clone(user);

        user.setPhoneNumber(phoneNumber);
        // save
        this.userRepository.saveChangePhoneNumber(oldUser, user);

        this.invalidateToken(response);
    }

    @Override
    public AccountDTO getAccount() {
        User user = this.getUserLogin();

        AccountDTO dto = this.accountMapper.toDto(user);

        dto.setPrivileges(CmsSecurityUtils.getPrivileges());

        dto.setMaxRoleLevel(this.getMaxUserRoleLevel(user));

        dto.setChangePwRequired(Validator.isNotNull(user.getPwExpiredTime())
                && user.getPwExpiredTime().isBefore(LocalDateTime.now()));

        return dto;
    }

    @Override
    public UserDTO getAccountInfo() {
        User user = this.getUserLogin();

        UserDTO userDTO = this.userMapper.toInfoDto(user, this.userRepository);

		Optional<Department> department = this.departmentRepository
				.findByDepartmentIdAndStatusNot(user.getDepartmentId(), EntityStatus.DELETED.getStatus());

        if (department.isPresent()) {
            userDTO.setDepartmentName(department.get().getDepartmentName());
        }

        Position position = this.positionRepository.findByPositionIdAndStatusNot(user.getPositionId(),
                EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(position)) {
            userDTO.setPositionName(position.getPositionName());
        }

        return userDTO;
    }

    @Override
    public ResponseEntity<PasswordVerifyResponse> getPwVerifyStatus() {
        User user = this.getUserLogin();

        PasswordVerifyResponse response = this.passwordVerifyService.getVerifyStatus(user.getUsername());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @Override
    public void invalidateToken(HttpServletResponse response) {
        String username = CmsSecurityUtils.getCurrentUserLogin().orElse(null);

        if (Validator.isNotNull(username)) {
            jwtTokenProvider.invalidateToken(username);

            response.addCookie(jwtTokenProvider.clearCookie(SecurityConstants.Cookie.ACCESS_TOKEN));
            response.addCookie(jwtTokenProvider.clearCookie(SecurityConstants.Cookie.REFRESH_TOKEN));
            response.addCookie(jwtTokenProvider.clearCookie(SecurityConstants.Cookie.REMEMBER_ME));
        }
    }

    @Override
    public OtpVerifyResponse otpVerify(OtpVerifyRequest request, OtpType otpType) {
        String phoneNumber = request.getPhoneNumber();

        // validate otp
        this.otpService.validateOtp(phoneNumber, request.getTransactionId(), otpType, request.getOtp());

        // save otp verify transaction
        String transactionId = this.otpRepository.putTransaction(phoneNumber);

        _log.info("Create OTP verify transaction id = {}", transactionId);
        // create response
        OtpVerifyResponse response = OtpVerifyResponse.builder().transactionId(transactionId).build();

        // invalidate otp
        this.otpService.invalidateOtp(phoneNumber, otpType);

        return response;
    }

    @Override
    public ResponseEntity<TokenResponse> refreshToken(HttpServletRequest request, HttpServletResponse response) {
        String ip = HttpUtil.getClientIP(request);

        Cookie[] cookies = request.getCookies();

        if (Validator.isNull(cookies)) {
            _log.error("refreshToken error: cookie request is null, ip = {}", ip);

            throw new UnauthorizedException(Labels.getLabels(LabelKey.ERROR_INVALID_REFRESH_TOKEN),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_REFRESH_TOKEN);
        }

        Cookie refreshCookie = Arrays.stream(cookies)
                .filter(cookie -> cookie.getName().equals(SecurityConstants.Cookie.REFRESH_TOKEN)).findFirst()
                .orElse(null);

        if (refreshCookie == null || Validator.isNull(refreshCookie.getValue())) {
            _log.error("refreshToken error: refresh cookie is null, ip = {}", ip);

            throw new UnauthorizedException(Labels.getLabels(LabelKey.ERROR_INVALID_REFRESH_TOKEN),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_REFRESH_TOKEN);
        }

        // parse token
        String username = this.jwtTokenProvider.validateRefreshToken(refreshCookie.getValue());

        // get user
        User user = this.findByUsername(username);

        if (Validator.isNull(user) || !Validator.equals(EntityStatus.ACTIVE.getStatus(), user.getStatus())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_USER_DOES_NOT_EXIST_OR_HAS_BEEN_DEACTIVATED),
                    User.class.getSimpleName(), LabelKey.ERROR_USER_DOES_NOT_EXIST_OR_HAS_BEEN_DEACTIVATED);
        }

        Cookie rememberMeCookie =
                Arrays.stream(cookies).filter(cookie -> cookie.getName().equals(SecurityConstants.Cookie.REMEMBER_ME))
                        .findFirst().orElse(null);

        boolean rememberMe = Objects.nonNull(rememberMeCookie) && Validator.isNotNull(rememberMeCookie.getValue())
                && GetterUtil.getBooleanValue(rememberMeCookie.getValue(), false);

        // create cookie
        JWTAccessToken accessToken = jwtTokenProvider.createAccessToken(username, rememberMe);

        JWTToken refreshToken = jwtTokenProvider.createRefreshToken(username);

        HttpHeaders httpHeaders = new HttpHeaders();

        TokenResponse tokenResponse = TokenResponse.builder().type(SecurityConstants.Header.BEARER_START.trim())
                .token(accessToken.getCsrfToken().getToken()).duration(accessToken.getCsrfToken().getDuration())
                .build();

        HttpCookie accessCookie = jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.ACCESS_TOKEN,
                accessToken.getAccessToken().getToken(), accessToken.getAccessToken().getDuration());

        HttpCookie newRefreshCookie = jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.REFRESH_TOKEN,
                refreshToken.getToken(), refreshToken.getDuration());

        HttpCookie newRememberMeCookie = jwtTokenProvider.createHttpCookie(SecurityConstants.Cookie.REMEMBER_ME,
                String.valueOf(rememberMe), accessToken.getAccessToken().getDuration());

        httpHeaders.add(HttpHeaders.SET_COOKIE, accessCookie.toString());
        httpHeaders.add(HttpHeaders.SET_COOKIE, newRefreshCookie.toString());
        httpHeaders.add(HttpHeaders.SET_COOKIE, newRememberMeCookie.toString());

        return new ResponseEntity<>(tokenResponse, httpHeaders, HttpStatus.OK);
    }

    @Override
    public void register(UserDTO dto) {
        if (Validator.isNotNull(dto.getUserId())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_CANNOT_CREATE_DATA_WITH_EXISTED_ID,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_USER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_CANNOT_CREATE_DATA_WITH_EXISTED_ID);
        }

//        String transactionId = dto.getTransactionId();
        String phoneNumber = dto.getPhoneNumber();

//        if (Validator.isNull(transactionId)) {
//            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER),
//                    User.class.getSimpleName(), LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER);
//        }
//
//        if (Validator.isNull(phoneNumber)) {
//            throw new BadRequestAlertException(
//                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
//                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
//                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
//        }
//
//        // get transactionId đã verify
//        String verifiedTransactionId = this.otpRepository.getTransactionIfPresent(phoneNumber);
//
//        if (Validator.isNull(verifiedTransactionId) || !Validator.equals(transactionId, verifiedTransactionId)) {
//            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER),
//                    User.class.getSimpleName(), LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER);
//        }

        // phoneNumber = StringUtil.replace(phoneNumber, PhoneNumber.VN_COUNTRY_CODE, PhoneNumber.PREFIX);

        // check trùng username
        if (this.userRepository.existsByUsername(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        if (Validator.isNull(dto.getFullname())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_FULLNAME)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        if (dto.getFullname().length() < this.validationProperties.getFullnameMinLength()
                || dto.getFullname().length() > this.validationProperties.getFullnameMaxLength()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_INVALID_LENGTH,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_FULLNAME),
                                    this.validationProperties.getFullnameMinLength(),
                                    this.validationProperties.getFullnameMaxLength()}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_INVALID_LENGTH);
        }

        if (Validator.isNotNull(dto.getDateOfBirth())) {
            LocalDate now = LocalDate.now();
            // Ngày sinh ko đc lớn hơn hiện tại
            if (dto.getDateOfBirth().isAfter(now)) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_DATE_CANNOT_BE_IN_THE_FUTURE,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_DATE_OF_BIRTH)}),
                        User.class.getSimpleName(), LabelKey.ERROR_DATE_CANNOT_BE_IN_THE_FUTURE);
            }
        }

        if (Validator.isNotNull(dto.getGender())) {
            Gender gender = Gender.valueOf(dto.getGender());

            if (Validator.isNull(gender)) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_INVALID_DATA,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_GENDER)}),
                        User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA);
            }
        }

        if (!Validator.isVNPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        if (this.userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        if (Validator.isNull(dto.getEmail())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        if (!Validator.isEmailAddress(dto.getEmail())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        if (this.userRepository.existsByEmail(dto.getEmail())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        if (Validator.isNull(dto.getPassword())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        if (Validator.isNull(dto.getConfirmPassword())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CONFIRM_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // decode password
        String password = dto.getPassword();
        String confirmPassword = dto.getConfirmPassword();

        try {
            password = this.rsaProvider.decrypt(password);
            confirmPassword = this.rsaProvider.decrypt(confirmPassword);
        } catch (Exception e) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.LABEL_PASSWORD);
        }

        if (!Validator.equals(password, confirmPassword)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_THE_PASSWORD_CONFIRMATION_DOES_NOT_MATCH),
                    User.class.getSimpleName(), LabelKey.ERROR_THE_PASSWORD_CONFIRMATION_DOES_NOT_MATCH);
        }

        // độ dài mật khẩu
        if (password.length() < this.validationProperties.getPasswordMinLength()
                || password.length() > this.validationProperties.getPasswordMaxLength()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_INVALID_LENGTH,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD),
                                    this.validationProperties.getPasswordMinLength(),
                                    this.validationProperties.getPasswordMaxLength()}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_INVALID_LENGTH);
        }

        // mạnh yếu
        // if (!this.validationProperties.isPasswordValid(password)) {
        // throw new BadRequestAlertException(
        // Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
        // new Object[] {Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
        // User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        // }

        User user = this.userMapper.toEntity(dto);

        user.setUsername(phoneNumber);
        user.setPassword(this.passwordEncoder.encode(password));
        user.setStatus(EntityStatus.ACTIVE.getStatus());
        user.setRoleUpdatable(false); // user cannot assign others role

        user = this.userRepository.save(user);

        // lưu role
        Role role = this.roleRepository.findFirstByNameAndStatusNot( SecurityConstants.SystemRole.ROLE_USER_POTENTIAL,
                EntityStatus.DELETED.getStatus());

        if (Validator.isNull(role) || !Validator.equals(role.getStatus(), EntityStatus.ACTIVE.getStatus())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_ROLE)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }

        // save user role
        UserRole userRole = new UserRole(user.getUserId(), role.getRoleId());

        this.userRoleRepository.save(userRole);
    }

    @Override
    public void resetPw(PasswordRequest request) {
        String transactionId = request.getTransactionId();
        String phoneNumber = request.getPhoneNumber();

        if (Validator.isNull(transactionId)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER),
                    User.class.getSimpleName(), LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER);
        }

        if (Validator.isNull(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // get transactionId đã verify
        String verifiedTransactionId = this.otpRepository.getTransactionIfPresent(phoneNumber);

        if (Validator.isNull(verifiedTransactionId) || !Validator.equals(transactionId, verifiedTransactionId)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER),
                    User.class.getSimpleName(), LabelKey.ERROR_YOU_MUST_VERIFY_YOUR_PHONE_NUMBER);
        }

        // get user by phonenumber
        User user = this.userRepository.findByPhoneNumber(phoneNumber);

        if (Validator.isNull(user) || !Validator.equals(user.getStatus(), EntityStatus.ACTIVE.getStatus())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_USER_WITH_PHONE_NUMBER_CANNOT_BE_FOUND, new Object[]{phoneNumber}),
                    User.class.getSimpleName(), LabelKey.ERROR_USER_WITH_PHONE_NUMBER_CANNOT_BE_FOUND);
        }

        String newPassword = request.getPassword();
        String confirmPassword = request.getConfirmPassword();

        if (Validator.isNull(newPassword)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_NEW_PASSWORD_IS_MISSING),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_NEW_PASSWORD_IS_MISSING);
        }

        if (Validator.isNull(confirmPassword)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CONFIRM_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // decode password
        try {
            newPassword = this.rsaProvider.decrypt(newPassword);
            confirmPassword = this.rsaProvider.decrypt(confirmPassword);

        } catch (Exception e) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.LABEL_PASSWORD);
        }

        if (!Validator.equals(newPassword, confirmPassword)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_YOUR_CONFIRM_PASSWORD_IS_NOT_MATCH),
                    User.class.getSimpleName(), LabelKey.ERROR_YOUR_CONFIRM_PASSWORD_IS_NOT_MATCH);
        }

        // độ dài mật khẩu
        if (newPassword.length() < this.validationProperties.getPasswordMinLength()
                || newPassword.length() > this.validationProperties.getPasswordMaxLength()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_INVALID_LENGTH,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD),
                                    this.validationProperties.getPasswordMinLength(),
                                    this.validationProperties.getPasswordMaxLength()}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_INVALID_LENGTH);
        }

        // mạnh yếu
        // if (!this.validationProperties.isPasswordValid(newPassword)) {
        // throw new BadRequestAlertException(
        // Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
        // new Object[] {Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
        // User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        // }

        user.setPassword(this.passwordEncoder.encode(newPassword));

        this.userRepository.save(user);
    }

    @Override
    public void sendOtpToChangePhoneNumber(String phoneNumber) {
        User user = this.getUserLogin();

        // check sđt đã dùng hay chưa
        if (Validator.isNull(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        if (!Validator.isVNPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        if (this.userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        // check trùng username
        if (this.userRepository.existsByUsername(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        this.passwordVerifyService.validate(user.getUsername());

        this.otpService.sendOtpViaSms(phoneNumber, OtpType.CHANGE_PHONE_NUMBER, false);
    }

    @Override
    public void sendOtpToRegister(String phoneNumber) {
        if (Validator.isNull(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // check số điện thoại có đúng định dạng ko
        if (!Validator.isVNPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        // check sđt có tồn tại ko, nếu không mới gửi otp
        if (this.userRepository.existsByPhoneNumber(phoneNumber) || this.userRepository.existsByUsername(phoneNumber)) {
            _log.error("The phone number {}  has already been taken", phoneNumber);

            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
        }

        this.otpService.sendOtpViaSms(phoneNumber, OtpType.REGISTER, false);
    }

    @Override
    public void sendOtpToResetPw(String phoneNumber) {
        if (Validator.isNull(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // check số điện thoại có đúng định dạng ko
        if (!Validator.isVNPhoneNumber(phoneNumber)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
        }

        // check sđt có tồn tại ko, nếu có mới gửi otp
        if (this.userRepository.existsByPhoneNumberAndStatusIs(phoneNumber, EntityStatus.ACTIVE.getStatus())) {
            _log.error("The phone number {} has been found", phoneNumber);

            this.otpService.sendOtpViaSms(phoneNumber, OtpType.RESET_PASSWORD, false);
        } else {
            _log.error("The phone number {} could not be found or has been deactived", phoneNumber);
        }
    }

    @Override
    public void updateUserInfo(UserDTO dto) {
        User user = this.getUserLogin();

        if (Validator.isNull(dto.getFullname())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_FULLNAME)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        // họ và tên
        if (!Validator.equals(dto.getFullname(), user.getFullname())) {
            if (dto.getFullname().length() < this.validationProperties.getFullnameMinLength()
                    || dto.getFullname().length() > this.validationProperties.getFullnameMaxLength()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_INPUT_INVALID_LENGTH,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_FULLNAME),
                                        this.validationProperties.getFullnameMinLength(),
                                        this.validationProperties.getFullnameMaxLength()}),
                        User.class.getSimpleName(), LabelKey.ERROR_INPUT_INVALID_LENGTH);
            }

            user.setFullname(dto.getFullname());
        }

        // ngày sinh
        if (Validator.isNull(dto.getDateOfBirth())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_DATE_OF_BIRTH)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        LocalDate now = LocalDate.now();
        // Ngày sinh ko đc lớn hơn hiện tại
        if (dto.getDateOfBirth().isAfter(now)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATE_CANNOT_BE_IN_THE_FUTURE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_DATE_OF_BIRTH)}),
                    User.class.getSimpleName(), LabelKey.ERROR_DATE_CANNOT_BE_IN_THE_FUTURE);
        }

        // Giới tính
        if (Validator.isNull(dto.getGender())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_GENDER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        Gender gender = Gender.valueOf(dto.getGender());

        if (Validator.isNull(gender)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INVALID_DATA,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_GENDER)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA);
        }

        if (Validator.isNull(dto.getEmail())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        if (!Validator.equals(dto.getEmail(), user.getEmail())) {
            if (!Validator.isEmailAddress(dto.getEmail())) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_INVALID_DATA_FORMAT,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                        User.class.getSimpleName(), LabelKey.ERROR_INVALID_DATA_FORMAT);
            }

            if (this.userRepository.existsByEmail(dto.getEmail())) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_DUPLICATE_DATA,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_EMAIL)}),
                        User.class.getSimpleName(), LabelKey.ERROR_DUPLICATE_DATA);
            }

            user.setEmail(dto.getEmail());
        }

        user.setDateOfBirth(dto.getDateOfBirth());
        user.setGender(dto.getGender());

        this.userRepository.save(user);
    }

    @Override
    public ResponseEntity<PasswordVerifyResponse> verifyPassword(String password) {
        User user = this.getUserLogin();

        if (Validator.isNull(password)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_PASSWORD)}),
                    User.class.getSimpleName(), LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }

        try {
            password = this.rsaProvider.decrypt(password);
        } catch (Exception e) {
            _log.error("Password decrypt failured: {}", password);

            return new ResponseEntity<>(this.passwordVerifyService.verify(user.getUsername(), false), HttpStatus.OK);
        }

        PasswordVerifyResponse response = this.passwordVerifyService.verify(user.getUsername(),
                this.passwordEncoder.matches(password, user.getPassword()));

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private User findByUsername(String username) {
        User user = this.userRepository.findByUsername(username);

        if (user == null) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD),
                    User.class.getSimpleName(), LabelKey.ERROR_INVALID_USERNAME_OR_PASSWORD);
        }

        return user;
    }

    private User getUserLogin() {
        return CmsSecurityUtils.getUserLogin()
                .orElseThrow(() -> new UnauthorizedException(Labels.getLabels(LabelKey.ERROR_USER_COULD_NOT_BE_FOUND),
                        User.class.getSimpleName(), LabelKey.ERROR_USER_COULD_NOT_BE_FOUND));
    }

    private int getMaxUserRoleLevel(User user) {
        List<Integer> roleLevels = new ArrayList<>();

        List<Role> roles = user.getRoles();

        if (Validator.isNull(roles)) {
            roles = this.roleRepository.findByUserId(user.getUserId());
        }

        for (Role role : roles) {
            if (role.getStatus() == EntityStatus.ACTIVE.getStatus()) {
                roleLevels.add(role.getLevel());
            }
        }

        return Validator.isNotNull(roleLevels) ? Collections.max(roleLevels) : 0;
    }

    /**
     * update device token of user if login success
     *
     * @param user
     * @param deviceToken
     */
    private void updateUserDeviceToken(User user, String deviceToken) {
        if (!Validator.equals(user.getDeviceToken(), deviceToken)) {
            user.setDeviceToken(deviceToken);

            this.userRepository.saveAll(Collections.singleton(user));
        }
    }
}
