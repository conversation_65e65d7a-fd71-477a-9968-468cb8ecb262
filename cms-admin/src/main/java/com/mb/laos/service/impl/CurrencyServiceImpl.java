package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.Currency;
import com.mb.laos.model.dto.CurrencyDTO;
import com.mb.laos.model.search.CurrencySearch;
import com.mb.laos.repository.CurrencyRepository;
import com.mb.laos.service.CurrencyService;
import com.mb.laos.service.mapper.CurrencyMapper;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CurrencyServiceImpl implements CurrencyService {

    private final CurrencyRepository currencyRepository;

    private final CurrencyMapper currencyMapper;

    @Override
    public Page<CurrencyDTO> search(CurrencySearch search) {
        Pageable pageable = PageRequest.of(search.getPageIndex(), search.getPageSize());

        List<Currency> currencies = currencyRepository.search(search, pageable);

        List<CurrencyDTO> currencyDTOS = currencyMapper.toDto(currencies);

        return new PageImpl<>(currencyDTOS, pageable, currencyRepository.count(search));
    }

    @Override
    public CurrencyDTO create(CurrencyDTO request) {

        if (this.currencyRepository.existsByCodeAndStatusNot(request.getCode().toUpperCase(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG101216);
        }
        if (this.currencyRepository.existsByValueAndStatusNot(request.getValue(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG101283);
        }

        CurrencyDTO currencyDTO = new CurrencyDTO();
        currencyDTO.setCode(request.getCode().toUpperCase());
        currencyDTO.setName(request.getName());
        currencyDTO.setValue(request.getValue());
        currencyDTO.setStatus(EntityStatus.ACTIVE.getStatus());
        Currency currency = this.currencyRepository.save(this.currencyMapper.toEntity(currencyDTO));

        return this.currencyMapper.toDto(currency);
    }

    @Override
    public CurrencyDTO update(CurrencyDTO request) {
        Currency currency = ensureExisted(request.getCurrencyId());

        if (!Validator.equals(request.getCode(), currency.getCode()) && this.currencyRepository.existsByCodeAndStatusNot(request.getCode().toUpperCase(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG101216);
        }
        if (!Validator.equals(request.getValue(), currency.getValue()) && this.currencyRepository.existsByValueAndStatusNot(request.getValue(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG101283);
        }

        currency.setCode(request.getCode().toUpperCase());
        currency.setName(request.getName());
        currency.setValue(request.getValue());

        return this.currencyMapper.toDto(this.currencyRepository.save(currency));
    }

    @Override
    @Transactional
    public void active(Long id) {
        Currency currency = ensureExisted(id);

        currency.setStatus(EntityStatus.ACTIVE.getStatus());

        this.currencyRepository.save(currency);
    }

    @Override
    @Transactional
    public void inactive(Long id) {

        Currency currency = ensureExisted(id);

        currency.setStatus(EntityStatus.INACTIVE.getStatus());

        this.currencyRepository.save(currency);
    }

    @Override
    public CurrencyDTO detail(Long id) {
        Currency currency = ensureExisted(id);

        return this.currencyMapper.toDto(currency);
    }

    @Override
    public void delete(Long id) {

        Currency currency = ensureExisted(id);

        currency.setStatus(EntityStatus.DELETED.getStatus());

        this.currencyRepository.save(currency);
    }

    private Currency ensureExisted(Long id) {
        if (Validator.isNull(id)) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        }

        return currencyRepository.findById(id).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101183));
    }
}
