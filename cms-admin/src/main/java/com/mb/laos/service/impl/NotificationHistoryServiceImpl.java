package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.component.PremiumAccNumberChargeFailJob;
import com.mb.laos.component.SmsBalanceChargeFailJob;
import com.mb.laos.configuration.TemplateProperties;
import com.mb.laos.configuration.TransactionProperties;
import com.mb.laos.configuration.ValidationProperties;
import com.mb.laos.enums.ConfigureAutomaticReportType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.NotificationHistoryEnum;
import com.mb.laos.enums.NotificationLimitStatus;
import com.mb.laos.enums.NotificationLimitType;
import com.mb.laos.enums.TransactionMBType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.ConfigureAutomaticReport;
import com.mb.laos.model.Customer;
import com.mb.laos.model.EmailRecipients;
import com.mb.laos.model.NotificationHistory;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.Transaction_;
import com.mb.laos.model.dto.ConfigureAutomaticReportDTO;
import com.mb.laos.model.dto.EmailRecipientsDTO;
import com.mb.laos.model.dto.NotificationHistoryDTO;
import com.mb.laos.model.search.NotificationHistoryRequest;
import com.mb.laos.repository.ConfigureAutomaticReportRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.EmailRecipientsReportRepository;
import com.mb.laos.repository.NotificationHistoryRepository;
import com.mb.laos.repository.PremiumAccNumberRepository;
import com.mb.laos.service.NotificationHistoryService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.ConfigureAutomaticReportMapper;
import com.mb.laos.service.mapper.EmailRecipientsMapper;
import com.mb.laos.service.mapper.NotificationHistoryMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.FileUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.enums.TransactionStatusConstant;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.jasypt.encryption.pbe.PBEStringEncryptor;
import org.jetbrains.annotations.NotNull;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
@RequiredArgsConstructor
@Log4j
public class NotificationHistoryServiceImpl implements NotificationHistoryService {
    private final NotificationHistoryRepository notificationHistoryRepository;
    private final NotificationHistoryMapper notificationHistoryMapper;
    private final TransactionProperties transactionProperties;
    private final StorageService storageService;
    private final CustomerRepository customerRepository;
    private final TemplateProperties templateProperties;
    private final PBEStringEncryptor encryptor;
    private final ConfigureAutomaticReportRepository configureAutomaticReportRepository;
    private final ConfigureAutomaticReportMapper configureAutomaticReportMapper;
    private final EmailRecipientsReportRepository emailRecipientsReportRepository;
    private final EmailRecipientsMapper emailRecipientsMapper;
    private final ValidationProperties validation;
    private final PremiumAccNumberRepository premiumAccNumberRepository;
    private final SmsBalanceChargeFailJob smsBalanceChargeFailJob;
    private final PremiumAccNumberChargeFailJob premiumAccNumberChargeFailJob;

    @Override
    public Page<NotificationHistoryDTO> searchByKeyword(NotificationHistoryRequest params, Pageable pageable) {
        params.setEncryptedKeyword(encryptor.encrypt(params.getKeyword()));
        List<NotificationHistoryDTO> notificationHistories = this.notificationHistoryMapper.toDto(this.notificationHistoryRepository
                .search(params, PageRequest.of(params.getPageIndex(), params.getPageSize())));

        this.enrichTransactions(notificationHistories, params);

        this.enrichTransferTypeTransactions(notificationHistories, params);

        this.enrichReferralCode(notificationHistories);

        return new PageImpl<>(notificationHistories, PageRequest.of(params.getPageIndex(),
                params.getPageSize()), this.notificationHistoryRepository.count(params));
    }

    @Override
    public Page<NotificationHistoryDTO> countNotification(NotificationHistoryRequest params, Pageable pageable) {
        List<NotificationHistoryDTO> notificationHistories = this.notificationHistoryRepository
                .countNotification(params, PageRequest.of(params.getPageIndex(), params.getPageSize()));

        long totalElement = notificationHistories.size();

        if (totalElement == 0L) {
            return Page.empty();
        }

        notificationHistories = this.getPaginatedTransactions(params, notificationHistories);
        this.enrichTransferType(notificationHistories);

        return new PageImpl<>(notificationHistories, PageRequest.of(params.getPageIndex(), params.getPageSize()), totalElement);
    }

    private List<NotificationHistoryDTO> enrichTransferType(List<NotificationHistoryDTO> list) {
        list.forEach(item -> {
            if (Validator.equals(item.getNotificationType(), NotificationHistoryEnum.NOTIFICATION_LIMIT)) {
                if (Validator.isNotNull(item.getBankCode())
                        && Validator.isNotNull(item.getTransferType())
                        && item.getTransferType().equals(TransferType.TRANSFER_MONEY)) {
                    if (item.getBankCode().equals(TransactionMBType.MB.toString())) {
                        item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTERNAL_BANK));
                    } else {
                        item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTER_BANK));
                    }
                }
                if (item.getTransferType().equals(TransferType.TOPUP)) {
                    item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_TOP_UP));
                } else if (item.getTransferType().equals(TransferType.BILLING)) {
                    item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_BILLING));
                } else if (item.getTransferType().equals(TransferType.CASH_IN)) {
                    item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_CASH_IN));
                } else if (item.getTransferType().equals(TransferType.CASH_OUT)) {
                    item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_CASH_OUT));
                } else if (item.getTransferType().equals(TransferType.INSURANCE)) {
                    item.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INSURANCE));
                }

                if (Validator.equals(item.getTypeLimit(), NotificationLimitType.LIMIT_PER_DAY)) {
                    item.setTypeLimitStr(Labels.getLabels(LabelKey.LABEL_EXCEEDED_DAILY_LIMIT));
                } else if (Validator.equals(item.getTypeLimit(), NotificationLimitType.LIMIT_PER_MONTH)) {
                    item.setTypeLimitStr(Labels.getLabels(LabelKey.LABEL_EXCEEDED_MONTHLY_LIMIT));
                }
            }
        });

        return list;
    }

    @NotNull
    private List<NotificationHistoryDTO> getPaginatedTransactions(NotificationHistoryRequest request, List<NotificationHistoryDTO> list) {

        list.sort(Comparator.comparing(NotificationHistoryDTO::getLastModifiedDate).reversed());

        if (request.isHasPageable()) {
            long totalElement = list.size();

            int fromIndex = request.getPageSize() * request.getPageIndex();
            int toIndex = fromIndex + request.getPageSize();

            if (toIndex > totalElement) {
                toIndex = (int) totalElement;
            }

            if (Validator.equals(fromIndex, toIndex)) {
                NotificationHistoryDTO transaction = list.get(fromIndex);

                list.clear();
                list.add(transaction);
            }

            if (fromIndex > totalElement) {
                list.clear();
            } else {
                list = list.subList(fromIndex, toIndex);
            }
        }
        return list;
    }

    @Override
    public void notificationHistoryExport(HttpServletResponse response, NotificationHistoryRequest request) {
        this.validateTime(request);
        request.setEncryptedKeyword(this.encryptor.encrypt(request.getKeyword()));

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());
        List<NotificationHistoryDTO> notificationHistories = this.notificationHistoryMapper.toDto(
                this.notificationHistoryRepository.search(request, pageable));

        this.enrichTransactions(notificationHistories, request);
        this.enrichTransferTypeTransactions(notificationHistories, request);
        this.enrichReferralCode(notificationHistories);

        NotificationHistoryEnum notificationType = request.getNotificationTypes().stream().findFirst().orElse(null);
        if (notificationType == null) {
            return;
        }

        String templateFileName;
        String resultFileName;

        switch (notificationType) {
            case PREMIUM_ACCOUNT_NUMBER_REVERT:
                resultFileName = getFormattedFileName(LabelKey.LABEL_TRANSACTION_HISTORY_FILE_NAME);
                templateFileName = this.templateProperties.getNotificationLimit().getTemplateFileName();
                break;
            case SMS_BALANCE_CHARGE_FAIL:
                resultFileName = getFormattedFileName(LabelKey.LABEL_TEMPLATE_NOTIFICATION_SMS_BALANCE_FILE_NAME);
                templateFileName = this.templateProperties.getNotificationSmsBalanceFeeFailed().getTemplateFileName();
                break;
            default:
                return;
        }

        exportToExcel(response, notificationHistories, request, resultFileName, templateFileName);
    }

    private String getFormattedFileName(String labelKey) {
        String fileName = String.format(Labels.getLabels(labelKey),
                DateUtil.formatStringLongTimestamp(new Date()));
        return StringUtil.replace(StringUtil.trimAll(fileName), StringPool.SPACE, StringPool.DASH);
    }

    /**
     * Xuất danh sách NotificationHistory ra file Excel
     */
    private void exportToExcel(HttpServletResponse response, List<NotificationHistoryDTO> data,
                               NotificationHistoryRequest request, String fileName, String templateFileName) {
        try (InputStream inputStream = this.storageService.getExcelTemplateFromResource(templateFileName);
             OutputStream os = response.getOutputStream()) {

            Context context = createExportContext(data, request);

            response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + fileName);

            JxlsHelper.getInstance().processTemplate(inputStream, os, context);
            response.flushBuffer();
        } catch (Exception e) {
            _log.error("Export excel error", e);
        }
    }

    /**
     * Tạo context chứa dữ liệu cần export
     */
    private Context createExportContext(List<NotificationHistoryDTO> data, NotificationHistoryRequest request) {
        Context context = new Context();

        String sourceZone = Labels.getTimeZoneFromRequest();
        String createdDateStartAt = Validator.isNotNull(request.getFromDate()) ?
                LocalDateUtil.getLocalDate(request.getFromDate(), DateUtil.SHORT_DATE_PATTERN_DASH) : StringPool.THREE_PERIOD;
        String createdDateEndAt = Validator.isNotNull(request.getToDate()) ?
                LocalDateUtil.getLocalDate(request.getToDate(), DateUtil.SHORT_DATE_PATTERN_DASH) : StringPool.THREE_PERIOD;

        if (Validator.isNotNull(request.getNotificationTypes()) &&
                Validator.equals(request.getNotificationTypes().stream().findFirst().get(), NotificationHistoryEnum.SMS_BALANCE_CHARGE_FAIL)) {
            context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_REGISTER_SMS_BALANCE_FAIL_TITLE));
        } else {
            context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_PREMIUM_ACCOUNT_REVERT_TITLE));
        }

        data.forEach(item -> {
            if (Validator.isNotNull(item.getTransactionFinishTime())) {
                String transactionTimeStr = InstantUtil.formatStringLongDate(item.getTransactionFinishTime(), ZoneId.of(sourceZone));
                item.setTransactionFinishTimeStr(transactionTimeStr);
            }

            if (Validator.equals(item.getStatus(), EntityStatus.INACTIVE.getStatus())) {
                item.setStatusStr(Labels.getLabels(LabelKey.LABEL_NOT_CONTACT_YET));
            } else {
                item.setStatusStr(Labels.getLabels(LabelKey.LABEL_CONTACTED));
            }
        });

        context.putVar("createdDateStartAt", createdDateStartAt);
        context.putVar("createdDateEndAt", createdDateEndAt);
        context.putVar("localDateNow", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
        context.putVar("fromDate", Labels.getLabels(LabelKey.LABEL_FROM_DATE));
        context.putVar("toDate", Labels.getLabels(LabelKey.LABEL_TO_DATE));
        context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
        context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));
        context.putVar("customerCif", Labels.getLabels(LabelKey.LABEL_CIF_NUMBER));
        context.putVar("transactionStatus", Labels.getLabels(LabelKey.LABEL_CONTACT_STATUS));
        context.putVar("type", Labels.getLabels(LabelKey.LABEL_TRANSACTION_TRANSFER_TYPE));
        context.putVar("phoneNumber", Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER));
        context.putVar("fullName", Labels.getLabels(LabelKey.LABEL_FULLNAME));
        context.putVar("amount", Labels.getLabels(LabelKey.LABEL_TRANSACTION_AMOUNT));
        context.putVar("accountNumber", Labels.getLabels(LabelKey.LABEL_ACCOUNT_NUMBER));
        context.putVar("referralCode", Labels.getLabels(LabelKey.LABEL_REFERRAL_CODE_CUSTOMER));
        context.putVar("transactionDate", Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        context.putVar("status", Labels.getLabels(LabelKey.LABEL_STATUS));
        context.putVar("lists", data);

        return context;
    }

    @Override
    public void notContacted(Long id) {
        NotificationHistory notificationHistory = this.ensureExistedNewsAndStatusNot(id, NotificationLimitStatus.CONTACTED.getStatus());

        notificationHistory.setStatus(NotificationLimitStatus.NO_CONTACT_YET.getStatus());

        this.notificationHistoryRepository.save(notificationHistory);
    }

    @Override
    public void contacted(Long id) {
        NotificationHistory notificationHistory = this.ensureExistedNewsAndStatusNot(id, NotificationLimitStatus.NO_CONTACT_YET.getStatus());

        notificationHistory.setStatus(NotificationLimitStatus.CONTACTED.getStatus());

        this.notificationHistoryRepository.save(notificationHistory);
    }

    @Override
    public ConfigureAutomaticReportDTO create(ConfigureAutomaticReportDTO dto) {

        ConfigureAutomaticReport configureAutomaticReportExisted = this.configureAutomaticReportRepository.
                findByType(dto.getType());

        if (Validator.isNotNull(configureAutomaticReportExisted)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        this.validateRequestConfigureAutomaticReport(dto);
        dto.setSendTime(LocalTime.now());
        dto.setIntervalMinute(Validator.isNotNull(dto.getIntervalMinute()) ? dto.getIntervalMinute() : null);

        ConfigureAutomaticReport configureAutomaticReport = this.configureAutomaticReportRepository
                .save(this.configureAutomaticReportMapper.toEntity(dto));

        List<EmailRecipientsDTO> emailRecipients = new ArrayList<>();

        dto.getEmails().forEach(item -> {
            EmailRecipientsDTO emailRecipientsDTO = EmailRecipientsDTO.builder()
                    .email(item)
                    .configureAutomaticReportId(configureAutomaticReport.getConfigureAutomaticReportId())
                    .build();
            emailRecipients.add(emailRecipientsDTO);
        });

        this.emailRecipientsReportRepository.saveAll(this.emailRecipientsMapper.toEntity(emailRecipients));

        try {
            if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.SMS_BALANCE_CHARGE_FEE)) {
                this.smsBalanceChargeFailJob.deleteSmsBalanceChargeFail();
                this.smsBalanceChargeFailJob.createSmsBalanceChargeFail(dto);
            }
            if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.PREMIUM_ACC_NUMBER_CHARGE_FEE)) {
                this.premiumAccNumberChargeFailJob.deletePremiumAccNumberChargeFail();
                this.premiumAccNumberChargeFailJob.createPremiumAccNumberChargeFail(dto);
            }
        } catch (Exception e) {
            _log.error("Create job charge fee error", e);
        }
        return dto;
    }

    private boolean checkDuplicateEmail(List<String> emails) {
        Set<String> uniqueEmails = new HashSet<>();
        for (String mail : emails) {
            if (!uniqueEmails.add(mail)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ConfigureAutomaticReportDTO update(ConfigureAutomaticReportDTO dto) {
        if (Validator.isNull(dto.getConfigureAutomaticReportId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        this.validateRequestConfigureAutomaticReport(dto);

        ConfigureAutomaticReportDTO configAutomaticReport = this.configureAutomaticReportMapper
                .toDto(this.configureAutomaticReportRepository
                        .findById(dto.getConfigureAutomaticReportId())
                        .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101183)));

        configAutomaticReport.setContent(dto.getContent());
        configAutomaticReport.setTitle(dto.getTitle());
        configAutomaticReport.setStatus(dto.getStatus());
        configAutomaticReport.setSendTime(LocalTime.now());
        configAutomaticReport.setIntervalMinute(Validator.isNotNull(dto.getIntervalMinute()) ? dto.getIntervalMinute() : null);

        this.configureAutomaticReportRepository.save(this.configureAutomaticReportMapper.toEntity(configAutomaticReport));

        if (Validator.isNotNull(dto.getEmails())) {
            List<EmailRecipients> emailRecipients = this.emailRecipientsReportRepository
                    .findAllByConfigureAutomaticReportId(dto.getConfigureAutomaticReportId());

            this.emailRecipientsReportRepository.deleteAll(emailRecipients);

            List<EmailRecipientsDTO> emailRecipientsDTOS = new ArrayList<>();

            dto.getEmails().forEach(item -> {
                EmailRecipientsDTO emailRecipientsDTO = EmailRecipientsDTO.builder()
                        .email(item)
                        .configureAutomaticReportId(configAutomaticReport.getConfigureAutomaticReportId())
                        .build();
                emailRecipientsDTOS.add(emailRecipientsDTO);
            });
            this.emailRecipientsReportRepository.saveAll(this.emailRecipientsMapper.toEntity(emailRecipientsDTOS));
        }

        try {
            if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.SMS_BALANCE_CHARGE_FEE)) {
                this.smsBalanceChargeFailJob.updateSmsBalanceChargeFail(dto);
            }

            if (Validator.equals(dto.getType(), ConfigureAutomaticReportType.PREMIUM_ACC_NUMBER_CHARGE_FEE)) {
                this.premiumAccNumberChargeFailJob.updatePremiumAccNumberChargeFail(dto);
            }
        } catch (Exception e) {
            _log.error("update job charge fee error", e);
        }

        return configAutomaticReport;
    }

    @Override
    @Transactional(readOnly = true)
    public ConfigureAutomaticReportDTO getInfo(ConfigureAutomaticReportType type) {

        if (Validator.isNull(type)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        ConfigureAutomaticReportDTO configureAutomaticReportDTO = this.configureAutomaticReportMapper
                .toDto(this.configureAutomaticReportRepository.findByType(type));

        if (Validator.isNull(configureAutomaticReportDTO)) {
            return null;
        }

        if (Validator.isNotNull(configureAutomaticReportDTO.getTransactionStatus())) {
            List<String> transactionStatuses = Arrays.stream(configureAutomaticReportDTO.getTransactionStatus().split(StringPool.COMMA))
                    .collect(Collectors.toList());
            configureAutomaticReportDTO.setTransactionStatuses(transactionStatuses);
        }

        List<EmailRecipients> emailRecipients = this.emailRecipientsReportRepository
                .findAllByConfigureAutomaticReportId(configureAutomaticReportDTO.getConfigureAutomaticReportId());

        configureAutomaticReportDTO.setEmailRecipients(emailRecipients);

        return configureAutomaticReportDTO;
    }

    private NotificationHistory ensureExistedNewsAndStatusNot(Long id, int status) {
        if (Validator.isNull(id)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        NotificationHistory notificationHistory = this.notificationHistoryRepository.findById(id).orElse(null);

        if (Validator.isNull(notificationHistory) || !Validator.equals(notificationHistory.getStatus(), status)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        return notificationHistory;
    }

    private void validateTime(NotificationHistoryRequest request) {
        if (request.getToDate().isBefore(request.getFromDate())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CUSTOMER)}),
                    Transaction_.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);

        } else if (Period.between(request.getToDate()
                .minus(this.transactionProperties.getMaxPeriodExportData().minusDays(1)), request.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE),
                    Transaction_.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE);
        }
    }

    private void enrichTransactions(List<NotificationHistoryDTO> notificationHistories, NotificationHistoryRequest request) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

        List<Long> customerIds = notificationHistories.stream()
                .map(NotificationHistoryDTO::getCustomerId)
                .distinct()
                .collect(Collectors.toList());

        List<Customer> customers = this.customerRepository.findAllByCustomerIdIn(customerIds);


        notificationHistories.forEach(notificationHistoryDTO -> {
            // 1: enrich thong tin khach hang
            if (!CollectionUtils.isEmpty(customers)) {
                Optional<Customer> optionalCustomer = customers.stream()
                        .filter(customer -> Validator.equals(customer.getCustomerId(), notificationHistoryDTO.getCustomerId()))
                        .findFirst();

                optionalCustomer.ifPresent(customer -> {
                    notificationHistoryDTO.setCustomerAccountName(customer.getFullname());
                    notificationHistoryDTO.setCustomerCif(customer.getCif());
                });
            }

            // 2: enrich so tien giao dich
            long transactionAmount = Validator.isNotNull(notificationHistoryDTO.getTransactionAmount())
                    ? notificationHistoryDTO.getTransactionAmount()
                    : 0L;

            double totalAmountTransaction = 0;

            totalAmountTransaction = Math.round(transactionAmount - transactionAmount * notificationHistoryDTO.getDiscount() / 100) + Math.round(notificationHistoryDTO.getTransactionFee());

            notificationHistoryDTO.setTotalAmountTransaction(totalAmountTransaction);

            // 3: format ngay giao dich
            notificationHistoryDTO.setTransactionDate(notificationHistoryDTO.formatTransactionStartTime());

            if (Validator.isNotNull(notificationHistoryDTO.getTransactionStartTime()) && Validator.isNotNull(request.getTimeZoneStr())) {
                ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(notificationHistoryDTO.getTransactionStartTime(),
                        ZoneId.of(request.getTimeZoneStr()));
                notificationHistoryDTO.setTransactionStartTimeStr(zonedDateTime.format(formatter));
            }

            // 4: trang thai giao dich
            notificationHistoryDTO.setTransactionStatusStr(Labels.getLabels(
                    TransactionStatusConstant.valueOf(notificationHistoryDTO.getTransactionStatus().name()).getLabel()));

            //5 format currency fee
            notificationHistoryDTO.setTransactionAmountStr(StringUtil.formatMoney(notificationHistoryDTO.getTransactionAmount()));
            notificationHistoryDTO.setTransactionFeeStr(StringUtil.formatMoney(Double.valueOf(notificationHistoryDTO.getTransactionFee()).longValue()));
            notificationHistoryDTO.setTotalAmountTransactionStr(StringUtil.formatMoney(Double.valueOf(notificationHistoryDTO.getTotalAmountTransaction()).longValue()));
            if (Validator.isNotNull(notificationHistoryDTO.getTotalAmount())) {
                notificationHistoryDTO.setTotalAmountStr(StringUtil.formatMoney(Double.valueOf(notificationHistoryDTO.getTotalAmount()).longValue()));
            }
            notificationHistoryDTO.setDiscountFixedStr(StringUtil.formatMoney(Double.valueOf(notificationHistoryDTO.getDiscountFixed()).longValue()));
            notificationHistoryDTO.setActualTransactionAmountStr(StringUtil.formatMoney(Double.valueOf(Validator.isNotNull(notificationHistoryDTO.getActualTransactionAmount()) ? notificationHistoryDTO.getActualTransactionAmount() : 0L).longValue()));

            if (Validator.equals(notificationHistoryDTO.getStatus(), NotificationLimitStatus.CONTACTED.getStatus())) {
                notificationHistoryDTO.setStatusStr(Labels.getLabels(LabelKey.LABEL_CONTACTED));
            } else {
                notificationHistoryDTO.setStatusStr(Labels.getLabels(LabelKey.LABEL_NOT_CONTACT_YET));
            }

            if (Validator.equals(notificationHistoryDTO.getTypeLimit(), NotificationLimitType.LIMIT_PER_DAY)) {
                notificationHistoryDTO.setTypeLimitStr(Labels.getLabels(LabelKey.LABEL_EXCEEDED_DAILY_LIMIT));
            } else if (Validator.equals(notificationHistoryDTO.getTypeLimit(), NotificationLimitType.LIMIT_PER_MONTH)) {
                notificationHistoryDTO.setTypeLimitStr(Labels.getLabels(LabelKey.LABEL_EXCEEDED_MONTHLY_LIMIT));
            }

            if (Validator.isNotNull(notificationHistoryDTO.getCreatedDate()) && Validator.isNotNull(request.getTimeZoneStr())) {
                ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(notificationHistoryDTO.getCreatedDate(),
                        ZoneId.of(request.getTimeZoneStr()));
                notificationHistoryDTO.setCreatedDateStr(zonedDateTime.format(formatter));
            }
        });
    }

    private void enrichTransferTypeTransactions(List<NotificationHistoryDTO> notificationHistories, NotificationHistoryRequest request) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

        notificationHistories.forEach(transactionDTO -> {
            if (Validator.isNotNull(transactionDTO.getTransactionStartTime()) && Validator.isNotNull(request.getTimeZoneStr())) {
                ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(transactionDTO.getTransactionStartTime(),
                        ZoneId.of(request.getTimeZoneStr()));
                transactionDTO.setTransactionStartTimeStr(zonedDateTime.format(formatter));
            }
        });
    }

    private void validateRequestConfigureAutomaticReport(ConfigureAutomaticReportDTO request) {
        if (Validator.isNull(request.getContent())
                || Validator.isNull(request.getTitle())
                || Validator.isNull(request.getEmails())) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        boolean emailValidation = request.getEmails().stream()
                .anyMatch(item -> !item.matches(ValidationConstraint.PATTERN.EMAIL_REGEX_V2));
        if (emailValidation) {
            throw new BadRequestAlertException(ErrorCode.MSG1084);
        }

        if (this.checkDuplicateEmail(request.getEmails())) {
            throw new BadRequestAlertException(ErrorCode.MSG101272);
        }

    }

    private void enrichReferralCode(List<NotificationHistoryDTO> notificationHistories) {
        // Lấy danh sách số tài khoản số đẹp trong noti
        List<String> accountNumbers = notificationHistories.stream()
                .map(NotificationHistoryDTO::getPhoneNumber)
                .distinct()
                .collect(Collectors.toList());

        // Tạo Map từ số tài khoản -> referralCode để tra cứu nhanh
        Map<String, String> referralCodeMap = this.premiumAccNumberRepository
                .findAllByPremiumAccNumberIn(accountNumbers)
                .stream()
                .filter(p -> p.getReferralCode() != null && !p.getReferralCode().isEmpty()) // Lọc những giá trị hợp lệ
                .collect(Collectors.toMap(PremiumAccNumber::getPremiumAccNumber, PremiumAccNumber::getReferralCode));

        // Cập nhật referralCode cho từng NotificationLimitDTO nếu có trong Map
        notificationHistories.forEach(dto -> {
            String referralCode = referralCodeMap.get(dto.getPhoneNumber());
            if (referralCode != null) {
                dto.setReferralCode(referralCode);
            }
        });
    }
}
