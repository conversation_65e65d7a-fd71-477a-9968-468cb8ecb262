package com.mb.laos.service;

import com.mb.laos.enums.ConfigureAutomaticReportType;
import com.mb.laos.model.dto.ConfigureAutomaticReportDTO;

public interface ConfigureAutomaticReportService {
    /**
     * Get information
     *
     * @param type
     */
    ConfigureAutomaticReportDTO getInfo(ConfigureAutomaticReportType type);

    /**
     * create
     *
     * @param configureAutomaticReportDTO
     * @return ConfigureAutomaticReportDTO
     */
    ConfigureAutomaticReportDTO create(ConfigureAutomaticReportDTO configureAutomaticReportDTO);

    /**
     * update
     *
     * @param configureAutomaticReportDTO
     * @return ConfigureAutomaticReportDTO
     */
    ConfigureAutomaticReportDTO update(ConfigureAutomaticReportDTO configureAutomaticReportDTO);
}
