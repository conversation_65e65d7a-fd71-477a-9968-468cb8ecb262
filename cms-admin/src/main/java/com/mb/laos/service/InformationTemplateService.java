package com.mb.laos.service;

import org.springframework.data.domain.Page;
import com.mb.laos.model.dto.InformationTemplateDTO;
import com.mb.laos.model.search.InformationTemplateSearch;

public interface InformationTemplateService {
    public Boolean lock(Long informationTemplateId);
    
    public Boolean unlock(Long informationTemplateId);
    
    public InformationTemplateDTO create(InformationTemplateDTO dto);
    
    public InformationTemplateDTO update(InformationTemplateDTO dto);
    
    public Boolean delete(Long informationTemplateId);
    
    public Page<InformationTemplateDTO> searchByKeyword(InformationTemplateSearch search);
    
    public Page<InformationTemplateDTO> search(InformationTemplateSearch search);
    
    public InformationTemplateDTO findById(Long informationTemplateId);
}
