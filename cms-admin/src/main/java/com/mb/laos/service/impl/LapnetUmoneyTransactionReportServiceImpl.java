package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.LapnetReportRequest;
import com.mb.laos.api.response.LapnetSettlementReportResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.cms.response.LapnetTransactionReportResponse;
import com.mb.laos.configuration.CmsProperties;
import com.mb.laos.configuration.LapnetProperties;
import com.mb.laos.configuration.TemplateProperties;
import com.mb.laos.enums.LapnetAsyncType;
import com.mb.laos.enums.UmoneyErrorCode;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.LapnetAsyncReportResults;
import com.mb.laos.model.LapnetTransactionReport;
import com.mb.laos.model.ResultSet;
import com.mb.laos.model.dto.LapnetAsyncReportResultsDTO;
import com.mb.laos.model.dto.LapnetReportDTO;
import com.mb.laos.model.dto.LapnetTransactionReportDTO;
import com.mb.laos.model.search.LapnetTransactionReportSearch;
import com.mb.laos.repository.LapnetAsyncReportResultsRepository;
import com.mb.laos.repository.LapnetTransactionReportRepository;
import com.mb.laos.service.ApiGeeLapnetReportService;
import com.mb.laos.service.ApiUmoneyService;
import com.mb.laos.service.LapnetAsyncReportResultsService;
import com.mb.laos.service.LapnetUmoneyTransactionReportService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.LapnetReportMapper;
import com.mb.laos.service.mapper.LapnetTransactionReportMapper;
import com.mb.laos.service.mapper.LapnetTransactionReportResponseMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.FileUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.Validator;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Data
@RequiredArgsConstructor
@Slf4j
@Transactional
public class LapnetUmoneyTransactionReportServiceImpl implements LapnetUmoneyTransactionReportService {

    private final ApiGeeLapnetReportService lapnetReportService;

    private final LapnetTransactionReportMapper transactionReportMapper;

    private final LapnetReportMapper lapnetReportMapper;

    private final LapnetTransactionReportRepository transactionReportRepository;

    private final LapnetAsyncReportResultsRepository asyncReportRepository;

    private final LapnetAsyncReportResultsService asyncReportService;

    private final CmsProperties cmsProperties;

    private final TemplateProperties templateProperties;

    private final StorageService storageService;

    private final LapnetTransactionReportResponseMapper transactionReportResponseMapper;

    private final LapnetProperties lapnetProperties;

    private final ApiUmoneyService apiUmoneyService;

    @Override
    public void asyncReport() {
        LocalDate now = LocalDate.now();

        LocalDate yesterday = now.minusDays(1);
        LocalDate twoDaysAgo = now.minusDays(2);

        // t-2
        LapnetAsyncReportResults report = this.asyncReportRepository.findByTransactionDateAndSuccessFalseAndType(twoDaysAgo, LapnetAsyncType.UMONEY);

        if (Validator.isNotNull(report)) {
            this.getReports(twoDaysAgo);
        }

        this.getReports(yesterday);

    }

    public void getReports(LocalDate date) {
        this.getLapnetSettlReport(date);
    }

    private LapnetAsyncReportResultsDTO getLapnetSettlReport(LocalDate date) {
        LapnetAsyncReportResultsDTO result;
        _log.info("Start getReport with date of data: {}", date);

        LapnetAsyncReportResultsDTO.LapnetAsyncReportResultsDTOBuilder newReport = LapnetAsyncReportResultsDTO.builder()
                .transactionDate(date);

        List<LapnetTransactionReport> rs = new ArrayList<>();

        try {
            LapnetSettlementReportResponse response = this.apiUmoneyService
                    .settlementReport(LapnetReportRequest.builder().date(date).build());

            LapnetReportDTO dto = response.getResult();

            if (Validator.isNotNull(response) && Validator.equals(response.getErrorCode(), UmoneyErrorCode.SUCCESS.getCode())) {
                if (Validator.isNotNull(dto.getTransactions())) {
                    rs = this.lapnetReportMapper.toEntity(dto.getTransactions());

                    List<LapnetTransactionReport> transactionReportOlds = this.transactionReportRepository
                            .findAllByTransactionIdIn(rs.stream().map(LapnetTransactionReport::getTransactionId)
                                    .collect(Collectors.toList()));

                    if (Validator.isNotNull(transactionReportOlds)) {
                        List<Long> transactionIdOlds = transactionReportOlds.stream()
                                .map(LapnetTransactionReport::getTransactionId).collect(Collectors.toList());

                        rs = rs.stream().filter(lapnetTransactionReport -> !transactionIdOlds
                                .contains(lapnetTransactionReport.getTransactionId())).collect(Collectors.toList());
                    }
                    rs.forEach(item -> item.setType(LapnetAsyncType.UMONEY));
                    rs = this.transactionReportRepository.saveAll(rs);

                }
                newReport.message(response.getErrorDetail()).errorCode(response.getErrorCode()).success(true)
                        .traceId(response.getClientMessageId()).reportCount(rs.size());
            }
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            newReport.message(ex.getMessage()).success(false);
        } catch (HttpResponseException ex) {
            newReport.errorCode(ex.getErrorCode()).message(ex.getErrorMessage()).success(false);
        } catch (Exception ex) {
            newReport.message(ex.getMessage()).success(false);
        } finally {
            _log.info("Got {} reports", rs.size());
            result = this.asyncReportService.save(newReport.build(), LapnetAsyncType.UMONEY);
        }
        return result;
    }

    @Override
    public void cleanTransactionReportOutDate(Instant timestamp) {
        this.transactionReportRepository.deleteByCreatedDateLessThanEqual(timestamp);
        this.asyncReportRepository.deleteByCreatedDateLessThanEqual(timestamp);
    }

    @Override
    public Page<LapnetTransactionReportDTO> search(LapnetTransactionReportSearch search) {
        LocalDate now = LocalDate.now();

        if (Validator.isNull(search.getFromDate()) && Validator.isNull(search.getToDate())) {
            search.setToDate(now);
            search.setFromDate(now.minusDays(30));
        } else if (Validator.isNull(search.getFromDate()) || Validator.isNull(search.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        } else if (search.getToDate().isBefore(search.getFromDate())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    LapnetTransactionReport.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);
        } else if (Period.between(search.getToDate().minus(this.lapnetProperties.getMaxPeriodExportData().minusDays(1)), search.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE),
                    LapnetTransactionReport.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE);
        }

        Pageable pageable = PageRequest.of(search.getPageIndex(), search.getPageSize());
        search.setType(LapnetAsyncType.UMONEY);
        ResultSet<LapnetTransactionReport> resultSet = this.transactionReportRepository.searchByKeyword(search,
                pageable);

        return new PageImpl<>(this.transactionReportMapper.toDto(resultSet.getResults()), pageable,
                resultSet.getCount());
    }

    @Override
    public void exportTransactionReport(HttpServletResponse response, LapnetTransactionReportSearch search) {
        LocalDate now = LocalDate.now();

        if (Validator.isNull(search.getFromDate()) && Validator.isNull(search.getToDate())) {
            search.setToDate(now);
            search.setFromDate(now.minusDays(30));
        } else if (Validator.isNull(search.getFromDate()) || Validator.isNull(search.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        } else if (search.getToDate().isBefore(search.getFromDate())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE),
                    LapnetTransactionReport.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);
        } else if (Period.between(search.getToDate().minus(this.lapnetProperties.getMaxPeriodExportData().minusDays(1)), search.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE),
                    LapnetTransactionReport.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE);
        }

        try (OutputStream os = response.getOutputStream();
             InputStream is = this.storageService.getExcelTemplateFromResource(
                     this.templateProperties.getLapnetUmoneyReport().getTemplateFile());) {

            Context context = new Context();
            search.setType(LapnetAsyncType.UMONEY);
            ResultSet<LapnetTransactionReport> resultSet = this.transactionReportRepository.searchByKeyword(search,
                    null);

            List<LapnetTransactionReport> reports = resultSet.getResults();

            List<LapnetTransactionReportResponse> result = this.transactionReportResponseMapper.toEntity(reports);

            String fileName = String.format(Labels.getLabels(LabelKey.LABEL_LAPNET_UMONEY_FILE_NAME),
                    DateUtil.formatStringLongTimestamp(new Date()));

            context.putVar("createdDateStartAt",
                    LocalDateUtil.getLocalDate(search.getFromDate(), DateUtil.SHORT_DATE_PATTERN_DASH));
            context.putVar("createdDateEndAt",
                    LocalDateUtil.getLocalDate(search.getToDate(), DateUtil.SHORT_DATE_PATTERN_DASH));
            context.putVar("now", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
            context.putVar("fromDate", Labels.getLabels(LabelKey.LABEL_FROM_DATE));
            context.putVar("toDate", Labels.getLabels(LabelKey.LABEL_TO_DATE));
            context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));
            context.putVar("time", Labels.getLabels(LabelKey.LABEL_LAPNET_TIME));
            context.putVar("id", Labels.getLabels(LabelKey.LABEL_LAPNET_ID));
            context.putVar("reference", Labels.getLabels(LabelKey.LABEL_REFERENCE));
            context.putVar("fromMember", Labels.getLabels(LabelKey.LABEL_LAPNET_FROM_MEMBER));
            context.putVar("fromUser", Labels.getLabels(LabelKey.LABEL_LAPNET_FROM_USER));
            context.putVar("fromAccount", Labels.getLabels(LabelKey.LABEL_LAPNET_FROM_ACCOUNT));
            context.putVar("toType", Labels.getLabels(LabelKey.LABEL_LAPNET_TO_TYPE));
            context.putVar("toAccount", Labels.getLabels(LabelKey.LABEL_LAPNET_TO_ACCOUNT));
            context.putVar("toMember", Labels.getLabels(LabelKey.LABEL_LAPNET_TO_MEMBER));
            context.putVar("amount", Labels.getLabels(LabelKey.LABEL_AMOUNT));
            context.putVar("currency", Labels.getLabels(LabelKey.LABEL_LAPNET_CURRENCY));
            context.putVar("purpose", Labels.getLabels(LabelKey.LABEL_LAPNET_PURPOSE));
            context.putVar("fee", Labels.getLabels(LabelKey.LABEL_LAPNET_FEE));
            context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
            context.putVar("localDateNow", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
            context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_TRANSACTION_TITLE));
            context.putVar("lists", result);

            JxlsHelper.getInstance().processTemplate(is, os, context);

            response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + fileName);

            response.flushBuffer();
        } catch (IOException e) {
            _log.debug("Error occurred when export when exportTransactionReport {}", e);
        }
    }

    @Override
    public void asyncReport(LocalDate date) {
        if (Validator.isNull(date)) {
            throw new BadRequestAlertException(ErrorCode.MSG101063);
        }
        LapnetAsyncReportResults report = this.asyncReportRepository.findByTransactionDateAndType(date, LapnetAsyncType.UMONEY);

        if (Validator.isNull(report) || !report.isSuccess()) {
            this.getReports(date);
        }
    }

    @Override
    public LapnetAsyncReportResultsDTO getSyncReport(LapnetTransactionReportSearch request) {
        if (Validator.isNull(request.getAsyncDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG101063);
        }
        return this.getLapnetSettlReport(request.getAsyncDate());
    }
}
