package com.mb.laos.service;

import com.mb.laos.enums.TransferType;
import com.mb.laos.model.ServicePack;
import com.mb.laos.model.dto.ServicePackDTO;
import com.mb.laos.model.dto.TransactionFeeTypeDTO;
import com.mb.laos.model.dto.TransferTypeDTO;
import com.mb.laos.model.search.ServicePackSearch;
import com.mb.laos.model.search.TransactionFeeTypeSearch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ServicePackService {

    /**
     * @param params
     * @param pageable
     * @return
     */
    Page<ServicePackDTO> searchByKeyword(ServicePackSearch params, Pageable pageable);

    /**
     * Get information bank
     *
     * @param id
     */
    ServicePackDTO getInfo(Long id);

    /**
     * create bank
     *
     * @param servicePackDTO
     * @return ServicePackDTO
     */
    ServicePackDTO create(ServicePackDTO servicePackDTO);

    /**
     * update bank
     *
     * @param servicePackDTO
     * @return ServicePackDTO
     */
    ServicePackDTO update(ServicePackDTO servicePackDTO);

    /**
     * Lock -> update status to INACTIVE (1)
     *
     * @param id
     */
    void lock(Long id);

    /**
     * UnLock -> update status to ACTIVE (1)
     *
     * @param id
     */
    void unlock(Long id);

    /**
     * DELETE -> update status to DELETE (1)
     *
     * @param id
     */
    void delete(Long id);

    Page<TransactionFeeTypeDTO> getAllTransferType(TransactionFeeTypeSearch search);

    List<String> getAllCarrierType();
}
