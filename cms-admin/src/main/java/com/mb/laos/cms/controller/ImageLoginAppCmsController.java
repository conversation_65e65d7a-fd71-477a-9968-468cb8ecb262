package com.mb.laos.cms.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.annotation.MaxSize;
import com.mb.laos.model.dto.ImageLoginAppDTO;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.service.ImageLoginAppCmsService;
import com.mb.laos.util.PropKey;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/image-login-app")
@RequiredArgsConstructor
@Validated
public class ImageLoginAppCmsController {
    private final ImageLoginAppCmsService imageLoginAppCmsService;

    @PostMapping("/icon")
	@PreAuthorize("hasPrivilege('IMAGE_LOGIN_APP_READ')")
    public ResponseEntity<InputStreamResource> getIcon(@RequestBody @Valid FileEntrySearch search) {
        return this.imageLoginAppCmsService.getIcon(search);
    }

    @PostMapping("/info")
    @PreAuthorize("hasPrivilege('IMAGE_LOGIN_APP_READ')")
    public ResponseEntity<ImageLoginAppDTO> customerInfo() {
        return ResponseEntity.ok(this.imageLoginAppCmsService.getInfo());
    }

    @InboundRequestLog
    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('IMAGE_LOGIN_APP_CREATE')")
    public ResponseEntity<ImageLoginAppDTO> create(HttpServletRequest request, @RequestParam("files")
    @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT) MultipartFile files, @Valid ImageLoginAppDTO imageLoginAppDTO) {
        return ResponseEntity.ok().body(this.imageLoginAppCmsService.create(imageLoginAppDTO, files));
    }

    @InboundRequestLog
    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('IMAGE_LOGIN_APP_WRITE')")
    public ResponseEntity<ImageLoginAppDTO> update(HttpServletRequest request, @RequestParam(name = "files", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT, required = false) MultipartFile files, @Valid ImageLoginAppDTO imageLoginAppDTO) {
        return ResponseEntity.ok().body(this.imageLoginAppCmsService.update(imageLoginAppDTO, files));
    }
}
