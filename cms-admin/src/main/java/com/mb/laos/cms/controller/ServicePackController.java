package com.mb.laos.cms.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.enums.TransferType;
import com.mb.laos.model.ServicePack;
import com.mb.laos.model.dto.ServicePackDTO;
import com.mb.laos.model.dto.TransactionFeeTypeDTO;
import com.mb.laos.model.dto.TransferTypeDTO;
import com.mb.laos.model.search.ServicePackSearch;
import com.mb.laos.model.search.TransactionFeeTypeSearch;
import com.mb.laos.service.ServicePackService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/service-pack")
@RequiredArgsConstructor
@Validated
public class ServicePackController {
    private final ServicePackService servicePackService;

    @PostMapping("/search")
    @PreAuthorize("hasAnyPrivilege('CLIENT_READ','SERVICE_PACK_READ')")
    public ResponseEntity<Page<ServicePackDTO>> search(HttpServletRequest request, @RequestBody ServicePackSearch search,
                                                    Pageable pageable) {
        return ResponseEntity.ok().body(this.servicePackService.searchByKeyword(search, pageable));
    }

    @PostMapping("/detail")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_READ')")
    public ResponseEntity<ServicePackDTO> customerInfo(@RequestBody ServicePackDTO servicePackDTO) {
        return ResponseEntity.ok(this.servicePackService.getInfo(servicePackDTO.getServicePackId()));
    }

    @InboundRequestLog
    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_CREATE')")
    public ResponseEntity<ServicePackDTO> create(HttpServletRequest request, @RequestBody @Valid ServicePackDTO servicePackDTO) {
        return ResponseEntity.ok().body(this.servicePackService.create(servicePackDTO));
    }

    @InboundRequestLog
    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_UPDATE')")
    public ResponseEntity<ServicePackDTO> update(HttpServletRequest request, @RequestBody @Valid ServicePackDTO servicePackDTO) {
        return ResponseEntity.ok().body(this.servicePackService.update(servicePackDTO));
    }

    @InboundRequestLog
    @PostMapping("/lock")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_LOCK')")
    public ResponseEntity<Boolean> lock(HttpServletRequest request, @RequestBody ServicePackDTO servicePackDTO) {
        this.servicePackService.lock(servicePackDTO.getServicePackId());

        return ResponseEntity.ok().build();
    }

    @InboundRequestLog
    @PostMapping("/unlock")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_UNLOCK')")
    public ResponseEntity<Boolean> unlock(HttpServletRequest request, @RequestBody ServicePackDTO servicePackDTO) {
        this.servicePackService.unlock(servicePackDTO.getServicePackId());

        return ResponseEntity.ok().build();
    }

    @InboundRequestLog
    @PostMapping("/delete")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_DELETE')")
    public ResponseEntity<Boolean> delete(HttpServletRequest request, @RequestBody ServicePackDTO servicePackDTO) {
        this.servicePackService.delete(servicePackDTO.getServicePackId());

        return ResponseEntity.ok().build();
    }

    @PostMapping("/search-type-service-pack")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_READ')")
    public ResponseEntity<Page<TransactionFeeTypeDTO>> searchServiceType(HttpServletRequest request, @RequestBody TransactionFeeTypeSearch search) {
        return ResponseEntity.ok().body(this.servicePackService.getAllTransferType(search));
    }

    @PostMapping("/search-carrier-type")
    @PreAuthorize("hasPrivilege('SERVICE_PACK_READ')")
    public ResponseEntity<List<String>> searchCarrierType(HttpServletRequest request) {
        return ResponseEntity.ok().body(this.servicePackService.getAllCarrierType());
    }
}
