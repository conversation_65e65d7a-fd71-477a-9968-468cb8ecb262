/**
 * 
 */
package com.mb.laos.cms.controller;

import com.mb.laos.annotation.LoginKaptcha;
import com.mb.laos.cms.request.LoginRequest;
import com.mb.laos.cms.response.TokenResponse;
import com.mb.laos.enums.OtpType;
import com.mb.laos.security.request.OtpRequest;
import com.mb.laos.security.request.OtpVerifyRequest;
import com.mb.laos.security.request.PasswordRequest;
import com.mb.laos.security.response.OtpVerifyResponse;
import com.mb.laos.service.AccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 *
 */

@RestController
@RequestMapping("/authenticate")
@RequiredArgsConstructor
public class AuthenticateController {

	private final AccountService accountService;

	@PostMapping("/refresh-token")
	public ResponseEntity<TokenResponse> refreshToken(HttpServletRequest request, HttpServletResponse response) {

		return this.accountService.refreshToken(request, response);
	}

	@GetMapping("/reset-pwd/otp")
	public ResponseEntity<Boolean> otpRequest(@RequestBody OtpRequest otpRequest) {
		this.accountService.sendOtpToResetPw(otpRequest.getPhoneNumber());

		return ResponseEntity.ok().build();
	}

	@PostMapping("/reset-pwd/verify")
	public OtpVerifyResponse otpVerify(@RequestBody OtpVerifyRequest request) {
		return this.accountService.otpVerify(request, OtpType.RESET_PASSWORD);
	}

	@PostMapping("/reset-pwd")
	public ResponseEntity<Boolean> resetPw(@RequestBody PasswordRequest request) {
		this.accountService.resetPw(request);

		return ResponseEntity.ok().build();
	}
	
	@LoginKaptcha
    @PostMapping("/login")
    public ResponseEntity<TokenResponse> authorize(HttpServletRequest request, HttpServletResponse response,
            @RequestBody @Valid LoginRequest loginRequest) {
        return this.accountService.authorize(request, response, loginRequest);
    }
}
