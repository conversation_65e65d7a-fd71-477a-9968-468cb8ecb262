package com.mb.laos.cms.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.MasterMerchantType;
import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceTypeRequest implements Serializable {
    private static final long serialVersionUID = -5802714711447344555L;

    @NotNull(message = LabelKey.ERROR_SERVICE_TYPE_IS_REQUIRED)
    private MasterMerchantType serviceType;
}
