package com.mb.laos.cms.controller;

import com.mb.laos.annotation.MaxSize;
import com.mb.laos.cms.request.InterestRateCreateRequest;
import com.mb.laos.cms.request.InterestRateUpdateRequest;
import com.mb.laos.model.dto.InterestRateDTO;
import com.mb.laos.model.dto.InterestRateDetailDTO;
import com.mb.laos.model.dto.InterestRateInfoDTO;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.model.search.InterestRateSearch;
import com.mb.laos.service.InterestRateService;
import com.mb.laos.util.PropKey;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@Controller
@RequestMapping("/interest-rate")
@RequiredArgsConstructor
@Validated
public class InterestRateController {
    private final InterestRateService interestRateService;

    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_CREATE')")
    public ResponseEntity<InterestRateDetailDTO> create(@Valid InterestRateCreateRequest request, @RequestParam(value = "file", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_INTEREST_RATE) MultipartFile file) {
        return ResponseEntity.ok().body(this.interestRateService.create(request, file));
    }

    @PostMapping("/search")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_READ')")
    public ResponseEntity<Page<InterestRateDTO>> search(@RequestBody @Valid InterestRateSearch search) {
        return ResponseEntity.ok().body(this.interestRateService.search(search));
    }

    @PostMapping("/detail")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_READ')")
    public ResponseEntity<InterestRateInfoDTO> detail(@RequestBody @Valid InterestRateDTO request) {
        return ResponseEntity.ok().body(this.interestRateService.detail(request));
    }

    @PostMapping("/lock")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_LOCK')")
    public ResponseEntity<Boolean> lock(@RequestBody @Valid InterestRateDTO request) {
        this.interestRateService.lock(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/unlock")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_UNLOCK')")
    public ResponseEntity<Boolean> unlock(@RequestBody @Valid InterestRateDTO request) {
        this.interestRateService.unlock(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/delete")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_DELETE')")
    public ResponseEntity<Boolean> delete(@RequestBody @Valid InterestRateDTO request) {
        this.interestRateService.delete(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('INTEREST_RATE_UPDATE')")
    public ResponseEntity<InterestRateDetailDTO> update(InterestRateUpdateRequest request, @RequestParam(value = "file", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_INTEREST_RATE) MultipartFile file) {
        return ResponseEntity.ok().body(this.interestRateService.update(request, file));
    }
}
