package com.mb.laos.cms.request;

import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class UpdateSectorRequest extends Request {
    @NotNull(message = LabelKey.ERROR_SECTOR_ID_IS_REQUIRED)
    private Integer customerSectorId;

    @NotNull(message = LabelKey.ERROR_CIFS_IS_REQUIRED)
    private List<@Size(max = ValidationConstraint.LENGTH.MERCHANT.MERCHANT_NAME_MAX_LENGTH, message = LabelKey.ERROR_MERCHANT_NAME_MAX_LENGTH) String> cifs;
}
