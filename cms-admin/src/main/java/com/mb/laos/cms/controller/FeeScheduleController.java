package com.mb.laos.cms.controller;

import com.mb.laos.annotation.MaxSize;
import com.mb.laos.cms.request.FeeScheduleCreateRequest;
import com.mb.laos.cms.request.FeeScheduleUpdateRequest;
import com.mb.laos.model.dto.FeeScheduleDTO;
import com.mb.laos.model.dto.FeeScheduleDetailDTO;
import com.mb.laos.model.dto.FeeScheduleInfoDTO;
import com.mb.laos.model.search.FeeScheduleSearch;
import com.mb.laos.service.FeeScheduleService;
import com.mb.laos.util.PropKey;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@RestController
@RequestMapping("/fee-schedule")
@RequiredArgsConstructor
public class FeeScheduleController {
    private final FeeScheduleService feeScheduleService;

    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_CREATE')")
    public ResponseEntity<FeeScheduleDetailDTO> create(@Valid FeeScheduleCreateRequest request, @RequestParam(value = "file", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_FEE_SCHEDULE) MultipartFile file) {
        return ResponseEntity.ok().body(this.feeScheduleService.create(request, file));
    }

    @PostMapping("/search")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_READ')")
    public ResponseEntity<Page<FeeScheduleDTO>> search(@RequestBody @Valid FeeScheduleSearch search) {
        return ResponseEntity.ok().body(this.feeScheduleService.search(search));
    }

    @PostMapping("/detail")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_READ')")
    public ResponseEntity<FeeScheduleInfoDTO> detail(@RequestBody @Valid FeeScheduleDTO request) {
        return ResponseEntity.ok().body(this.feeScheduleService.detail(request));
    }

    @PostMapping("/lock")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_LOCK')")
    public ResponseEntity<Boolean> lock(@RequestBody @Valid FeeScheduleDTO request) {
        this.feeScheduleService.lock(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/unlock")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_UNLOCK')")
    public ResponseEntity<Boolean> unlock(@RequestBody @Valid FeeScheduleDTO request) {
        this.feeScheduleService.unlock(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/delete")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_DELETE')")
    public ResponseEntity<Boolean> delete(@RequestBody @Valid FeeScheduleDTO request) {
        this.feeScheduleService.delete(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('FEE_SCHEDULE_UPDATE')")
    public ResponseEntity<FeeScheduleDetailDTO> update(FeeScheduleUpdateRequest request, @RequestParam(value = "file", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_FEE_SCHEDULE) MultipartFile file) {
        return ResponseEntity.ok().body(this.feeScheduleService.update(request, file));
    }
}
