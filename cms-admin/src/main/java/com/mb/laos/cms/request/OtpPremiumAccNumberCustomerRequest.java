package com.mb.laos.cms.request;

import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Setter
@Getter
public class OtpPremiumAccNumberCustomerRequest extends Request {

    @NotBlank(message = LabelKey.ERROR_PREMIUM_ACC_NUMBER_IS_REQUIRED)
    @Pattern(regexp = ValidationConstraint.PATTERN.LUCKY_NUMBER_ACCOUNT, message = LabelKey.ERROR_PREMIUM_ACC_NUMBER_IS_NOT_MATCH)
    private String premiumAccNumber;

    private Long customerId;

    private Double fee;
}
