package com.mb.laos.cms.request;

import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class ConfirmOtpPremiumAccNumberRequest extends Request {
    @NotBlank(message = LabelKey.ERROR_OTP_IS_MISSING)
    private String otpValue;

    @NotBlank(message = LabelKey.ERROR_TRANSACTION_ID_IS_REQUIRED)
    private String transactionId;

    @NotNull(message = LabelKey.ERROR_CUSTOMER_ID_IS_REQUIRED)
    private Long customerId;
}
