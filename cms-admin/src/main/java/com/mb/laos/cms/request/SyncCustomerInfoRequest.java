package com.mb.laos.cms.request;

import com.mb.laos.messages.LabelKey;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Setter
@Getter
public class SyncCustomerInfoRequest implements Serializable {
    private static final long serialVersionUID = -3161515350896337484L;

    @NotBlank(message = LabelKey.ERROR_ID_NUMBER_IS_EMPTY)
    private String idCardNumber;

    @NotBlank(message = LabelKey.ERROR_PHONE_NUMBER_IS_REQUIRED)
    private String phoneNumber;
}
