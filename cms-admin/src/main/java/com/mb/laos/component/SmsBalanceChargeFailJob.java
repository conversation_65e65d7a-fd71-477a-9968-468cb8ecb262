package com.mb.laos.component;

import com.mb.laos.model.dto.ConfigureAutomaticReportDTO;
import com.mb.laos.service.AutomaticReportService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Data
@Service
public class SmsBalanceChargeFailJob implements Job {
    private final Scheduler scheduler;
    private final AutomaticReportService automaticReportService;
    private static final String JOB_NAME = "SMS_BALANCE_JOB";
    private static final String TRIGGER_NAME = "SMS_BALANCE_TRIGGER";
    private static final String GROUP_NAME = "SMS_BALANCE_GROUP";

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        this.automaticReportService.sendSmsBalanceCharge();
        _log.info("SMS balance job run");
    }

    public void createSmsBalanceChargeFail(ConfigureAutomaticReportDTO dto) throws SchedulerException {
        JobDetail jobDetail = JobBuilder.newJob(SmsBalanceChargeFailJob.class)
                .withIdentity(JOB_NAME)
                .build();

        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(TRIGGER_NAME, GROUP_NAME)
                .startNow()
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInMinutes(dto.getIntervalMinute())
                        .repeatForever())
                .build();
        scheduler.scheduleJob(jobDetail, trigger);
    }

    public void updateSmsBalanceChargeFail(ConfigureAutomaticReportDTO dto) throws SchedulerException {
        Trigger oldTrigger = scheduler.getTrigger(TriggerKey.triggerKey(TRIGGER_NAME, GROUP_NAME));
        TriggerBuilder triggerBuilder = oldTrigger.getTriggerBuilder();
        Date startTime = new Date(System.currentTimeMillis() + dto.getIntervalMinute() * 60 * 1000);

        Trigger newTrigger = triggerBuilder
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInMinutes(dto.getIntervalMinute())
                        .repeatForever())
                .startAt(startTime)
                .build();

        scheduler.rescheduleJob(oldTrigger.getKey(), newTrigger);
    }

    public void deleteSmsBalanceChargeFail() throws SchedulerException {
        scheduler.deleteJob(JobKey.jobKey(JOB_NAME));
    }
}
