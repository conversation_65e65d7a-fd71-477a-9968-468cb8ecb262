package com.mb.laos.component;

import com.mb.laos.messages.Labels;
import com.mb.laos.model.dto.ConfigureAutomaticReportDTO;
import com.mb.laos.service.AutomaticReportService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.springframework.stereotype.Service;

import java.util.TimeZone;

@Slf4j
@Data
@Service
public class LapnetAutomaticReportJob implements Job {
    private final Scheduler scheduler;

    private final AutomaticReportService automaticReportService;

    private static final String JOB_NAME = "LAPNET_JOB";

    private static final String TRIGGER_NAME = "LAPNET_TRIGGER";

    private static final String GROUP_NAME = "LAPNET_GROUP";

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.automaticReportService.sendReportLapnet();
        _log.info("LAPNET job run");
    }

    public void createLapnetReportJob(ConfigureAutomaticReportDTO configureAutomaticReportDTO) throws SchedulerException {
        String sourceZone = Labels.getTimeZoneFromRequest();
        JobDetail jobDetail = JobBuilder.newJob(LapnetAutomaticReportJob.class)
                .withIdentity(JOB_NAME)
                .build();
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(TRIGGER_NAME, GROUP_NAME)
                .startNow()
                .withSchedule(CronScheduleBuilder.dailyAtHourAndMinute(configureAutomaticReportDTO.getSendTime().getHour(),configureAutomaticReportDTO.getSendTime().getMinute()).inTimeZone(TimeZone.getTimeZone(sourceZone)))
                .build();
        scheduler.scheduleJob(jobDetail, trigger);
    }

    public void updateLapnetReportJob(ConfigureAutomaticReportDTO configureAutomaticReportDTO) throws SchedulerException {
        String sourceZone = Labels.getTimeZoneFromRequest();

        Trigger oldTrigger = scheduler.getTrigger(TriggerKey.triggerKey(TRIGGER_NAME, GROUP_NAME));

        TriggerBuilder triggerBuilder = oldTrigger.getTriggerBuilder();

        Trigger newTrigger = triggerBuilder.withSchedule(CronScheduleBuilder.dailyAtHourAndMinute(configureAutomaticReportDTO.getSendTime().getHour(),configureAutomaticReportDTO.getSendTime().getMinute()).inTimeZone(TimeZone.getTimeZone(sourceZone)))
                .startNow()
                .build();

        scheduler.rescheduleJob(oldTrigger.getKey(), newTrigger);
    }

    public void deleteLapnetReportJob() throws SchedulerException {
        scheduler.deleteJob(JobKey.jobKey(JOB_NAME));
    }
}
