
# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    com.mb.laos: DEBUG
    org.springframework.cache: TRACE
    com.mb.laos.api.handler: DEBUG
    com.mb.laos.service.impl.SmsServiceImpl: DEBUG
    org.redisson: INFO
    io.netty: INFO
  file:
    max-size: 100MB
spring:
  liquibase:
    enabled: true
  jackson:
    date-format: dd-MM-yyyy
    time-zone: Asia/Ho_Chi_Minh
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
      fail-on-empty-beans: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ***************************************
    driver-class-name: oracle.jdbc.OracleDriver
    username: mb_lao_qa
    password: 123456a@
    hikari:
      auto-commit: false
      maximum-pool-size: 5
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.Oracle12cDialect
    database: ORACLE
    show-sql: false
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.temp.use_jdbc_metadata_defaults: false
      # hibernate.cache.region.factory_class: com.hazelcast.hibernate.HazelcastCacheRegionFactory
      hibernate.cache.hazelcast.instance_name: mb_cms
      hibernate.cache.hazelcast.shutdown_on_session_factory_close: false
      hibernate.cache.use_minimal_puts: true
      hibernate.cache.hazelcast.use_lite_member: true
      hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS: 30
      hibernate.generate_statistics: true
      hibernate.jdbc.batch_size: 10
      hibernate.order_inserts: true
      hibernate.format_sql: true
      # hibernate search
      hibernate.search.indexing_strategy: manual
      hibernate.search.default.directory_provider: filesystem
      hibernate.search.default.indexBase: 'indexes'
      hibernate.search.default.indexmanager: near-real-time
      #hibernate.search.default.infinispan.cachemanager_jndiname: java:comp/env/jdbc/CacheStoreDatasource
      #hibernate.search.infinispan.configuration_resourcename: hibernatesearch-infinispan.xml
  mail:
    host: smtp.gmail.com
    port: 465
    username: <EMAIL>
    password: 'VOvMZwqft7ABRpuTR1sHH9X+XTs+dEGoJaS6ELWYeCEAFjQMsFZo7eaCozPiN86Up4WhK47SnsaTMlijbvdkC13v53Xly3+YMZ2AkpN/Q4yoqggKnJe84jqxuwRIgQD6tZvVBHREKQxKhm5baQQjSgfvBzSdVEV3xgnDDACMsU5uQB+0eO+x3yTEXbpA6A2MYCmKkX3PpSiKZ14c/EBBk98GVDw8lU0w/yMU+2qApNrCHQHzzLx+yeDep7Ne7Nh7GP2vtMVpzNrbsHipPhtYhOlldjwqDhmUdVVfVe7z7XO5FbS+33vAKfrwZymsVrazHAwlHbzn/QXy2pC9lJOgRA=='
    from: <EMAIL>
    protocol: smtp
    properties:
      mail.debug: true
      mail.transport.protocol: smtp
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.ssl.enable: true
      mail.smtp.sendpartial: true
  messages:
    encoding: 'UTF-8'
    use-code-as-default-message: true
    basename: 'i18n/labels,i18n/cms_labels,i18n/lang_key'
    cache-duration: 60    # 60 second, see the ISO 8601 standard
  servlet:
    multipart:
      max-file-size: -1 #meaning total file size is unlimited
      max-request-size: -1 #meaning total request size for a multipart/form-data is unlimited.
      resolve-lazily: true
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    properties:
      org:
        quartz:
          jobStore:
            dataSource: QUATZ
            driverDelegateClass: org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            tablePrefix: QRTZ_
            isClustered: true
          dataSource:
            QUATZ:
              provider: hikaricp
              driver: oracle.jdbc.OracleDriver
              URL: ***************************************
              user: mb_lao_qa
              password: 123456a@
server:
  port: 80
  tomcat:
    accept-count: 1000 # Maximum queue length for incoming connection requests when all possible request processing threads are in use.
    max-connections: 8192 #Maximum number of connections that the server accepts and processes at any given time.
    max-http-form-post-size: 5MB #Maximum size, in bytes, of the HTTP post content.
    uri-encoding: UTF-8
    threads:
      max: 200 #Maximum number of worker threads.
    accesslog:
      buffered: false #Whether to buffer output such that it is flushed only periodically.
      directory: logs/access_log #Directory in which log files are created. Can be absolute or relative to the Tomcat base dir.
      enabled: true #Enable access log.
      file-date-format: .yyyy-MM-dd #Date format to place in the log file name.
      pattern: common #Format pattern for access logs.
      prefix: access_log #Log file name prefix.
      rename-on-rotate: false #Whether to defer inclusion of the date stamp in the file name until rotate time.
      request-attributes-enabled: true #Set request attributes for the IP address, Hostname, protocol, and port used for the request.
      rotate: true #Whether to enable access log rotation.
      suffix: .log #Log file name suffix.
  servlet:
    context-path: '/api/cms/v1'
    encoding:
      charset: 'UTF-8'
      enabled: true
      force: true
security:
  cache:
    url-patterns: '/i18n/*,/content/*,/app/*'
    max-age: 86400 # Image cache max-age in second, duration: 86400 (1 day)
  # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
  cors:
    allowed-origins: 'http://qa-mb-laos.evotek.vn,http://localhost:4200'
    allowed-methods: 'POST,OPTIONS,GET'
    allowed-headers: '*'
    exposed-headers: '*'
    allow-credentials: true
    max-age: 1800
  authentication:
    jwt:
      # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
      base64-secret: 'ajMywt0FWnZYET/u3Pm309ziD6eXRewGX+QsyHs6/Y4ascWDHAAK46ARsuUZKLcjMQxUa5mQfNTaujgm8Sg4TJh4wKGwwXBAtB2gsEm1TkZuSRo97/BEFIR5rEKefTBf41J4kcU/YndvNFJcMRXkgMoC6fvzjSVYx1fGNYxSJqO+neilVm3uBpQiuhQrhTMmrKZJYa5M9xutWfJsbQ1Tf6IQ6zlRKZqdt8edUD/mOuUwltlyC1UZCyALrcji2vFlgXu+eks6IjBkxlAT3LvwVe5Et4MoNaB+eHetIdL6epigf1nYxmLSPT3rWfQxRRmHAwSQnO+1atAubG/xQqvsTQ=='
    cookie:
      domain-name:
      enable-ssl: false
      http-only: true
      path: '/'
      same-site: 'Strict'
  login:
    max-attempt-time: 5
  password:
    max-attempt-time: 5
cache:
  config-type: 'redisson' # redis/redisson/hazelcast
  hazelcast:
    instanceName: 'mb_cms_qa'
    localIp: '127.0.0.1'
    remoteIp: '127.0.0.1'
    backup-count: 1
    management-center: # Full reference is available at: http://docs.hazelcast.org/docs/management-center/3.9/manual/html/Deploying_and_Starting.html
      enabled: false
      update-interval: 3
      url: http://*************:8180/hazelcast-mancenter
  redis:
    mode: 'standalone' # standalone, sentinel
    standalone:
      host: '*************'
      port: 6379
      password:
    sentinel:
      port: 26379
      password:
      master: masterredis
      nodes:
        #- localhost
        #- *********
        #- *********
        - redis-sentinel-1
        - redis-sentinel-2
    lettuce-pool:
      shutdown-timeout: 200 #milliseconds
      command-timeout: 200 #milliseconds
      min-idle: 1
      max-idle: 20
      max-wait-millis: 200 #milliseconds
      max-total: 20
    cache-duration: 1440 # thời gian lưu cache (đơn vị phút)
  redisson:
    mode: single #single, sentinel, cluster, replicated
    clientName: 'redis-client'
    password: # '+dshc78(jYtMAKzM'
    subscriptionsPerConnection: 5
    idleConnectionTimeout: 10000
    connectTimeout: 10000
    timeout: 3000
    retryAttempts: 3
    retryInterval: 1500
    threads: 16
    nettyThreads: 32
    transportMode: 'NIO'
    database: 0
    cache-duration: 10 # thời gian lưu cache (đơn vị phút)
    key-prefix: mb_laos_qa
    #mode single config
    single:
      address: 'redis://*************:6378' #if use ssl, the address should start with rediss://
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      connectionMinimumIdleSize: 24
      connectionPoolSize: 64
      dnsMonitoringInterval: 5000
    #mode sentinel config
    sentinel:
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes:
        #      - 'redis://127.0.0.1:26379'
        #      - 'redis://127.0.0.1:26380'
        - redis-sentinel-1
        - redis-sentinel-2
      masterName: 'masterredis'
      checkSentinelsList: false
    cluster:
      scanInterval: 20000
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes: 'rediss://clustercfg.mb-laos-redis-cluster.xar9fx.apse1.cache.amazonaws.com:6379'
    replicated:
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes: 'redis://gobiz-dev-001.rlopfv.0001.apse1.cache.amazonaws.com:6379,redis://gobiz-dev-002.rlopfv.0001.apse1.cache.amazonaws.com:6379'
  time-to-lives: # time to live in seconds
    default: -1
    token: 3600 # 1 hour
    remember-me-token: 7200 # 2 hours
    refresh-token: 86400 # 1 day
    otp: 120 # 90 seconds
    otp-verify: 3600 # lưu id xác thực otp trong 1 giờ
    temp-file: 3600 #1 hour
    login-failed: 86400 # 1 day, lưu số lần đăng nhập lỗi để hiện captcha
    captcha: 300 # 5 phút
    password-verify: 300 # 5 phút
    api-gee-token: 300 # 5 phút, lưu token api mb t24
springfox:
  documentation:
    enabled: true #enable spring fox on deverlopment mode
task:
  execution:
    pool:
      schedule:
        max-size : 5
        allow-core-thread-timeout : true
        core-size : 10
        keep-alive : 60
        queue-capacity : 100
        name : 'mb-schedule-threadpool-'
rsa:
  key-length: 2048
  algorithm: RSA/ECB/PKCS1Padding
  private-key: 'MIIEowIBAAKCAQEAmD9evqQe1hTXdRPEJKEgkTfPGYBk0c6JCY+vLn1m4pIaaDZdJmEWrug7YwRFGfE9TMKk26M3ACGOiu7Uw267Y85/Lb3maD7CplVkhjXVVcQ8aUI4t6/BFI7wLzt7DqAmlJR4FDgTDFQl1W8ZU+QpPyP8S34s9b+yvJZAPin9Pdd23uqNtBY2RFwu+QS27Q5u6IcCLFsb1wgTb3fq/umiQ4MXRzy6jsHoUQ1Z8n305oUWsxsaC6SLCJsFSJh2w6wakScGAF9hmWdGI+PkodILxhi53zM5gWFzusasW4QQJC7tElwhn4iIcJdYF1OIAhV0RQAxjfb5kZ3/1Dm7GxBYMwIDAQABAoIBADKBiz+5GFWysksvlHkGTtR97CEsm8WSO5nFZbsJdNEi4MnDhyFNQgYiGllduELD6a8GP0Rxn7RWbYAffMucd2PDxCL7hWz9rPsbBu8S+sOqBtRwWHpvPqFATeONWuReMo5FtpQWihMj8y0b3w4aeahtmqu8ntOjlXrEaWl16oHR7pa7pjYL+bHjDSModUlFSz8odOlBYOzCEO8lB4E0xbbinMxGDQ2i/ZMLrZm9yKrqz3jVLfHdEbz4v0nEcWAUJ281ZcBgJHJXpUu/66ntmGmhYSXXhloaVw7PlYw23JQfYEEtlZqujqbaILZWVNM10d7zXnL+VhRLp9mUZxIKWSECgYEAzI4WzpUaLAftjHc1qRDvQl6o42clEPgR4Agx6ulQaeVFEB3Jd+chI2XTxJl9+NoWFpGhBhLoYAuKIa2FXD8T1LmjNkzHtjWbGYia6xgCPwx3bZeNda9/jVfAbNRUNZDN6XVQBBpHM+3NcBSb66wv36qd1oxD646Z736sxfcMJ8MCgYEAvomOZH4Wwq5bsEUB7bqmgnSO9MW5zW1tahV/H/rju7XbKz3nuRFX5C5W4ju/EHJFE7R625esc/gsc4Z+v7P+o3Xj9F8bsVefAniU5R9NzMH+JXeHSfoG6Ca6mfZcjdmeBxSfzL2w2Ntf4K5cV0HQFxWbS2yPWiQzbtBcWsPwdtECgYEAtJ/rR7OljqfZfSVuvsFzZDnODC9yLY+/yQGzgmlPVpLa7yY+VqBDRMyPuJSJBgsdYgRqGcDsbJMEAEUG87PHQRpDIpmd6ClhsaRMl7d1lFLfymf+w6KakSahhfff/ATHrpSmeVjy8snTlFq+a9Z515kDEwZ8my4qg65pxc0mcJ0CgYA2iF4lquTOwWJil82Ogb5IVh3YNoMQJYKMOyzQNVlajxj0Tlp04F7cYtrhEyKDDYFxu1TyZn2oD16BjfvW2ChIFmSwu0vMvPw4k9Rho0z8DoWzqXsiFBpH9VWw3Q7uVlthQWjfC2eDGX5eaujo3GA3SyrQMnjw3q2NyLq2C6BWEQKBgHtkq7CBhIdaSk/bY9xsdKqWAHO3z0EPPjy4JPE6+MLA4vPSG7I9vq6Wlebwag8bfXETyv3geJJ9CjP8JOBqmLIKPRBiXrXJe6LpQ4Jso1+cd9LXxRNhkRf9DwtAaijYm1gkKdM7CAHLyfknn1vMzcGK82v2BOavyFC45z/QjllL'
  public-key: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmD9evqQe1hTXdRPEJKEgkTfPGYBk0c6JCY+vLn1m4pIaaDZdJmEWrug7YwRFGfE9TMKk26M3ACGOiu7Uw267Y85/Lb3maD7CplVkhjXVVcQ8aUI4t6/BFI7wLzt7DqAmlJR4FDgTDFQl1W8ZU+QpPyP8S34s9b+yvJZAPin9Pdd23uqNtBY2RFwu+QS27Q5u6IcCLFsb1wgTb3fq/umiQ4MXRzy6jsHoUQ1Z8n305oUWsxsaC6SLCJsFSJh2w6wakScGAF9hmWdGI+PkodILxhi53zM5gWFzusasW4QQJC7tElwhn4iIcJdYF1OIAhV0RQAxjfb5kZ3/1Dm7GxBYMwIDAQAB'
#  * "0 0 * * * *" = the top of every hour of every day.
#  * "*/10 * * * * *" = every ten seconds.
#  * "0 0 8-10 * * *" = 8, 9 and 10 o'clock of every day.
#  * "0 0 8,10 * * *" = 8 and 10 o'clock of every day.
#  * "0 0/30 8-10 * * *" = 8:00, 8:30, 9:00, 9:30 and 10 o'clock every day.
#  * "0 0 9-17 * * MON-FRI" = on the hour nine-to-five weekdays
#  * "0 0 0 25 12 ?" = every Christmas Day at midnight
scheduling:
  email-queue-sender:
    cron: '0 0/1 * * * *'
    enabled: false
    sent-limit: 3
    max-result: 20
    lock-at-least: PT30S
    lock-at-most: PT50S
  mass-indexing-updater:
    cron: '0 0/1 * * * *'
    enabled: true
    time-out: 5000 # job time-out (ms)
    package-scan: com.mb.laos.model
  send-notification:
    cron: '0 0/2 * * * *' # cứ 2 phút chạy 1 lần
    enabled: false
    lock-at-least: PT1M
    lock-at-most: PT1M30S
  send-notification-cms:
    cron: '0 0/2 * * * *' # cứ 2 phút chạy 1 lần
    enabled: true
    lock-at-least: PT1M
    lock-at-most: PT4M
  send-event:
    cron: '0 0/1 * * * *' # cứ 1 phút chạy 1 lần
    enabled: false
    lock-at-least: PT30S
    lock-at-most: PT1M
  clear-cache-manager:
    cron: '0 0 0 * * *' # chạy lúc nửa đêm
    enabled: false
    lock-at-least: PT3M
    lock-at-most: PT5M
  lapnet-sync-report:
    enabled: true
    cron: '0 0 2 * * *' # cứ 8h45 hàng ngày chạy 1 lần
    lock-at-least: PT1H
    lock-at-most: PT2H
    retry-quota: 5
    clean-report-out-date-after-month: 12
    max-deplay: 100
  lapnet-umoney-sync-report:
    enabled: true
    cron: '0 0 4 * * *' # cứ 8h45 hàng ngày chạy 1 lần
    lock-at-least: PT1H
    lock-at-most: PT2H
    retry-quota: 5
    clean-report-out-date-after-month: 12
    max-deplay: 100
  version-update-status:
    cron: '0 0/5 * * * *' # cứ 5 phút chạy 1 lần
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT6M
  cashout-account-amount-transferred:
    cron: '0 0 0 * * *' # chạy lúc nửa đêm
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT5M
  debit-account-amount-transferred:
    cron: '0 0 0 * * *' # chạy lúc nửa đêm
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT5M
  customer-noti-no-transaction:
    cron: '0 0/60 * * * *' # cứ 60 phút chạy 1 lần
    enabled: true
    lock-at-least: PT30S
    lock-at-most: PT1M
  customer-update-last-transaction:
    cron: '0 00 7 ? * *' # chạy lúc 7h UTC hàng ngày
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT5M
  premium-acc-number:
    cron: '0 0/30 * * * *' # 30 phút chạy một lần
    enabled: true
    lock-at-least: PT30S
    lock-at-most: PT1M
#    cron: '0 45 8 * * *' # cứ 8h45 hàng ngày chạy 1 lần
sms:
  token:
  brand-name:
  api-url:
otp:
  enable: false
  default-otp: ******** #OTP mặc định khi không bật OTP service
  number-of-digits: 8 #Độ dài OTP
  template: Nhap ma OTP %s gui ve so dien thoai %s de xac nhan. Ma OTP se het han sau %s phut
  otp-attempt: 3 # số lần request gửi otp nhưng không được sử dụng (tránh spam otp)
  duration: 3 # phút
version:
  min-time-after: PT2M # thời gian phát hành tối thiểu 2 phút kể từ thời điểm tạo
  url-store:
    ios: 'https://apps.apple.com/us/app/mb-laos/id1526248099'
    android: 'https://play.google.com/store/apps/details?id=com.mblaos&hl=vi&gl=US'
# base64
firebase:
  config: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

notification:
  end-user-type: 'USER'
  topic-system-notification: 'SYSTEM_NOTIFICATION'
  thread-pool: 10 #Số luồng tối đa khi thực hiện thông báo
  transaction-type:
    inter-bank: 222  # Nhận tiền từ bank khác về MBLAOS
    cash-in-counter: 52 # Cộng tiền (thanh toán tại quầy)
    internal-bank: 213 # Thông báo cộng tiền nội bộ
  time-between: 90 # tối đa trong khoảng thời gian 90 ngày
  time-between-event: P90D # tối đa trong khoảng thời gian 90 ngày
  timeline-between: 90 # tối đa mốc thời gian là 90 ngày
  max-period-search-data: 90 # tối đa mốc thời gian là 90 ngày cho hiển thị thông báo khác và khuyến mãi
  url-get-image-firebase: 'http://qa-mb-laos-gateway-api.evotek.vn/api/gateway/v1/notifications/public/icon'
  entry-prefix: AZ
  closure-prefix: ONLINE.AC.CLOSURE
  salaryMsg: SALARY

jasypt:
  encryptor:
    enable-decrypt-tool: true

websocket:
  endpoint: /ws
  application-prefix: /app
  topic-prefix: /topic
  topic-notification: /topic/notification/
  allowed-origins:
    - '*'

consumer:
  timeout: 600000
  default-max-per-route: 50
  max-total: 500
  timeout-test: 100
  api-gee:
    key: 'qQcBb1cRHYtMAhzLiS7EwXvRQ6caLc6s'
    secret: 'p2SQo2FruBR9KERq'
    refresh-token-before: 5 # lấy lại token trước khi hết hạn x phút
    base-url: 'https://api-sandbox.mbbank.com.vn/ms'
    base-url-root: 'https://api-sandbox.mbbank.com.vn'
    retry:
      max-attempts: 2
      max-delay: 100
    uri:
      token: 'https://api-sandbox.mbbank.com.vn/oauth2/v1/token'
      register: '/register'
      verify-acc-number-m-b: '/mbapplaos/common/v1.0/customer/infor'
      verify-acc-number-lapnet: '/mbapplaos/common/lapnet/v1.0/customer/infor'
      request-transfer: '/mbapplaos/fund/v1.0/make-request'
      verify-transfer: '/mbapplaos/fund/v1.1/make-confirm'
      revert: '/mbapplaos/fund/v1.0/revert'
      account-balance: '/mbapplaos/common/account/v1.0/balance'
      transaction-history: '/mbapplaos/common/v1.0/transaction/history'
      verify-customer: '/mbapplao/v1.0/customer/verifyCustomer'
      create-payment-account: '/mbapplao/v1.0/customer/requestCreateAccount'
      query-payment-account: '/mbapplao/v1.0/customer/requestQueryCustomer'
      confirm-create-account: '/mbapplao/v1.0/customer/confirmCreateAccount'
      confirm-query-account: '/mbapplao/v1.0/customer/confirmQueryCustomer'
      update-sector: '/mbapplao/v1.0/customer/updateSector'
      query-customer-info: '/mbapplao/v1.0/customer/getCustomer'
      cms-create-payment-account: '/mbapplao/v1.0/customer/createAccount'
      cms-update-customer-account: '/mbapplao/v1.0/customer/updateCustomer'
      lapnet-report: /mbapplaos/common/lapnet/report/settlement
      query-saving-account: '/mb-lao-saving/mbapplao/v1.0/get-saving-account'
      query-beautiful-account: '/mbapplao/v1.0/customer/queryBeautifulAccount'
      init-on-off-sms-cus: '/mbapplao/v1.0/customer/gen-sms-otp'
      create-beautiful-account-non-user: '/mbapplao/v1.0/customer/createBeautifulAccount/nonUser'
      create-beautiful-account: '/mbapplao/v1.0/customer/createBeautifulAccount/user'
  api-rabiloo:
    username: 'rabiloo'
    password: 'rabiloo'
    base-url: 'https://ekyc.rabiloo.net'
    height: 1280
    width: 720
    enable-resize: false
    id-card-regex: ^[0-9]{10,}$
    uri:
      ekyc-token-verify: '/backend/verify'
      ekyc-id-card-verify: '/api/v1/ocr/id'
    default-param:
      device-id: 'cfa923dc851ad784'
      brand-device: 'vsmart'
      os-device: 'android'
      product-name-service: 'Joy 4'
  api-umoney:
    client-id: 'MB_BANK@cli3ntId'
    client-secret: 'mbbank-s3cr3t@abc123'
    username: 'bank-mb'
    password: 'bank-mb-pw'
    settlement-report:
      username: 'fetek'
      password: 'fetek@123'
    base-url: 'https://test-api-um.unitel.com.la:8083/api/v1.0'
    base-url-settlement-report: 'https://uat-api-cvv-um.unitel.com.la:8087/api/v3'
    service-code: 'BANK_MB'
    retry:
      max-attempts: 2
      max-delay: 100
    uri:
      token-report: 'https://uat-api-cvv-um.unitel.com.la:8087/ewallet/token'
      token: 'https://test-api-um.unitel.com.la:8083/ewallet/token'
      check-ewallet-account: '/account/getInfo'
      get-otp: '/partner/requestOTP'
      payment-request: '/partner/debitAccount'
      transfer-money: '/bank/transferMoney'
      settlement-report: '/lapnet/settlementReport'
  api-ltc:
    user-id: 'LOTTERY'
    private-key: '9powBIDI2FWkpx8Mff9mGSDrV+Of2SPfa2jdyBGktdc='
    algorithm: SHA-1
    cipher-algorithm: 'AES/ECB/PKCS5Padding'
    url: 'http://ltcservice.laotel.com:5577/Services.asmx'
    base-url: 'http://Services.laotel.com/'
    soap-action:
      topup: 'http://Services.laotel.com/topup'
      payment: 'http://Services.laotel.com/payment'
      check-balance: 'http://Services.laotel.com/checkBalance'
  api-lvi:
    default-namespace: http://tempuri.org/
    url: https://api-test.lvi.la/api.asmx
    username: MB
    password: 994f4fFh@hfi956h
    soap-action:
      vehicle-package: http://tempuri.org/get_packageMV
      vehicle-type: http://tempuri.org/get_vehicle
      vehicle-package-fee: http://tempuri.org/get_insuranceMV
      vehicle-insurance: http://tempuri.org/get_vehicle_insurance
      buy-vehicle-insurance-new: http://tempuri.org/set_vehicle_insurance_new
      buy-vehicle-insurance-renew: http://tempuri.org/set_vehicle_insurance_renew
      health-package: http://tempuri.org/get_packagePA
      health-package-fee: http://tempuri.org/get_insurancePA
      buy-health-insurance-new: http://tempuri.org/set_accident_insurance_new
      delivery: http://tempuri.org/get_Delivery

  api-unitel:
    client_id: 'test_unitel'
    username: 'TEST_VPG'
    password: '23b8593edd4c1701fca1f1b4f412b151'
    public_key: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZiofOnG0q8PLTvX/WFlyAWqJ1ubFdy7wCqHOTEcoQFfo9FARssiLiToMt3P4r/5S8Hg+PvNuWxV1dKB+wuHPtmUisO9xaGTIgvP/Aki/O+dcrKso5cFI0wME//X5XlLvwNY1QawrYc92Q/+euGmqduOndXYEGQdcNIwu6u7ypEwIDAQAB'
    secret_key: 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJmKh86cbSrw8tO9f9YWXIBaonW5sV3LvAKoc5MRyhAV+j0UBGyyIuJOgy3c/iv/lLweD4+825bFXV0oH7C4c+2ZSKw73FoZMiC8/8CSL8751ysqyjlwUjTAwT/9fleUu/A1jVBrCthz3ZD/564aap246d1dgQZB1w0jC7q7vKkTAgMBAAECgYBX/J7es52LohBFbq63TJEtrMK0m/kcOwg+rbGtceBNX4rLPZFbiKmc7kkWGzI8nHXrZ36bbCqaN/fMzpV6e/lSItDx0u/JyajsC40jeuAokR4rw5d3He6z4C7ej6btdPCxoomh00qMpJszV6KhVqZSOvA6ACV/ZdndSPlw65BjgQJBAPUIB1tHb6BHtXlu8JBV9AuWt/nSMDFQ7zgIofBdnp/naeQ6ZNEkY2AO9Kk4ok+PS7wODfLzi0mqMQFh0shYwIMCQQCgag6BoXQL41F3lL7UX7UhoSEG5xzeOcgXTalgcUIv0avlOCuBmoykEoDVz7s2ek1REP/reDBR2CaFW1ALl/AxAkEAmcYtH7rIMhVurUPTUzGuE6vFz9F6DykUx9ybDIckaoPHb8S5yosElp8sKhrxue5bACzt0h/HtTZKxOxIZRVV0wJBAI+sDYwK183h7dBFV9kMU0VodBUjn5ZleBFvDDmFlvsPNn7ZHRY6HqbAr8VQBWQYi/EEYcI65kQXbQDZtMp9bbECQQCINRr7lvYvu8f0AvzhE+Z/LONXVtXefFvl3W6e4HMmeVOPNbc8vRTS5soI+ge5lxv7Ax2lHp1WfdRXBL+PNqWA'
    url: 'https://test-api-um.unitel.com.la:8080/ws'
    base-url: 'http://ws.vpg.viettel.com/'
    mti: '0200'
    success-code: '00'
    process-code:
      query-balance-prepaid-mobile: '000100'
      topup-prepaid-mobile: '000000'
      query-debt-postpaid-mobile: '000500'
      topup-postpaid-mobile: '000600'
      query-debt-postpaid-pstn: '001600'
      topup-postpaid-pstn: '001200'
      query-debt-postpaid-ftth: '002900'
      topup-postpaid-ftth: '002700'
    rest-url: 'https://test-api-um.unitel.com.la:8080/'
    uri:
      get-balance: 'api/v1/getMasterAccountBalance'

template:
  loan-online:
    template-file: 'loan_online_template.xlsx'
    template-file-name: 'loan_online_%s.xlsx'
  merchant:
    template-file: 'merchant_transaction_history_template.xlsx'
    template-file-name: 'merchant_transaction_history_%s.xlsx'
    merchant-template: 'merchant_template.xlsx'
  master-merchant:
    template-file: 'master_merchant_transaction_history_template.xlsx'
    template-file-name: 'master_merchant_transaction_history_%s.xlsx'
  master-merchant-list:
    template-file: 'master_merchant_list_template.xlsx'
    template-file-name: 'master_merchant_list_%s.xlsx'
  customer-notification:
    template-file: 'customer_notification_template.xlsx'
    template-file-name: 'customer_notification_%s.xlsx'
  transaction:
    template-file: 'transaction_history_template.xlsx'
    template-file-name: 'transaction_history_%s.xlsx'
    template-file-umoney: 'transaction_umoney_template.xlsx'
    template-file-automatic: 'transaction_umoney_template_automatic.xlsx'
    template-file-debit: 'transaction_debit_deposit_template.xlsx'
  international-transaction:
    template-file: 'international_transaction_template.xlsx'
    template-file-name: 'international_transaction_history_%s.xlsx'
  lapnet-transaction:
    template-file: 'lapnet_transaction_report.xlsx'
    template-file-name: 'Report_Lapnet_%s.xlsx'
    template-file-automatic: 'lapnet_transaction_report_automatic.xlsx'
  lapnet-umoney-report:
    template-file: 'lapnet_umoney_report.xlsx'
    template-file-name: 'Lapnet_Umoney_Report%s.xlsx'
    template-file-automatic: 'lapnet_umoney_report_automatic.xlsx'
  customer:
    template-file: 'customer_registration_template.xlsx'
    template-file-name: 'customer_registration_%s.xlsx'
    customer-import-template: 'customer_import_template.xlsx'
    customer-no-transaction-template: 'customer_no_transaction_template.xlsx'
    manual-activation-template: 'customer_manual_activation_template.xlsx'
  referral:
    template-file: 'referral_template.xlsx'
    referral-import-template: 'referral_import_template.xlsx'
    template-file-name: 'referral_%s.xlsx'
  version:
    template-file: 'version_template.xlsx'
  campaign:
    template-file: 'campaign_template.xlsx'
  config-trans-limit:
    template-file: 'config_trans_limit_template.xlsx'
    template-file-name: 'config_trans_limit_template_%.xlsx'
  number-of-transaction:
    template-file: 'number_of_transaction_template.xlsx'
    template-file-name: 'number_of_transaction_template_%.xlsx'
  sms-log:
    template-file: 'sms_log_template.xlsx'
  utility:
    template-file: 'transaction_history_utility.xlsx'
  notification-limit:
    template-file: 'transaction_notification_limit.xlsx'
    template-file-name: 'transaction_premium_account_revert_error.xlsx'
  premium-account-number-structure:
    template-file: 'premium_account_number_structure_template.xlsx'
  transaction-qrpay:
    template-file: 'transaction_qrpay_report.xlsx'
  sms-balance:
    template-file: 'sms_balance_template.xlsx'
    template-file-name: 'sms_balance_%s.xlsx'
  premium-account-number:
    template-file: 'premium_sold_account_number_template.xlsx'
    template-file-name: 'premium_account_number_template.xlsx'
  special-premium-account-number:
    template-file: 'special_premium_account_number_template.xlsx'
    template-file-name: 'special_number_account_template.xlsx'
  number-group:
    template-file: 'number_group_template.xlsx'
    template-file-name: 'number_group_%s.xlsx'
  lucky-number-account-structure:
    template-file: 'lucky_number_account_structure_template.xlsx'
    template-file-name: 'lucky_number_account_structure_%s.xlsx'
  notification-sms-balance-fee-failed:
    template-file: 'notification_sms_balance_fee_failed_template.xlsx'
    template-file-name: 'sms_balance_change_failed_template.xlsx'
  notification-history:
    template-file: 'notification_history_template.xlsx'
    template-file-name: 'notification_history_template_%s.xlsx'

loan-online:
  max-period-export-data: P90D # day
customer:
  max-period-export-data: P90D # day
  referral-code-random-limit: 8
  max-day-no-transaction: 150 # day
  max-period-search-data: 90 #day
  default-branch-code: LA0010001 # mã chi nhánh mặc định bên Lào
  currency: 'LAK'
lapnet:
  max-period-export-data: P90D # day
merchant-history:
  max-period-export-data: P90D # day
  mobile-topup:  ['UNITEL', 'LTC', 'ETL', 'TPLUS'] # nhà mạng nạp tiền điện thoại khi cấu hình phí
transaction-limit:
  minus: 2 # đơn vị phút
  length: 6 # độ dài danh sách các loại giao dịch
transaction:
  max-period-export-data: P90D # day
  length : 5 # độ dài số lượng giao dịch
interest-rate:
  max-period-export-data: P90D # day
fee_schedule:
  max-period-export-data: P90D # day
configure-automatic-report:
  max-period-export-data: P1D # day
transaction-qrpay:
  max-period-export-data: P90D # day
sms-balance-period:
  max-period-export-data: P90D # day

client:
  pw-duration: 180 # đơn vị ngày
  expire-time: 48 # đơn vị giờ
  expire-time-link-active: 5 # đơn vị phút
  expire-time-change-pw: 5 # đơn vị phút
  link-active-token: 'http://qa-mb-laos.evotek.vn/verify-user/'
  link-change-password: 'http://qa-mb-laos.evotek.vn/set-up-password/'

cms:
  campaign:
    min-banner: 1 # Số lượng banner tối thiểu
    max-banner: 5 # Số lượng banner tối đa
  default-lapnet-member-code: VMB
  default-umoney-member-code: Umoney.MB
application:
  constants:
    qr-code:
      payload-format-indicator: '000201'     # Phiên bản QR đang sử dụng
      point-of-initiation-transfer: '010212' # 11: nhiều hơn 1 giao dịch (payment)
      point-of-initiation-payment: '010211'  # 12: từng giao dịch
      merchant-account-info:
        laos-id: '40'
        umoney-id: '29'
        cam-tag:
          id: '30'
          sub-tag:
            merchant-id: '00'
            account-number: '01'
            bank-code: '02'
        lapnet-id: '38'
        transnational-id: '15'
        laoviet-id: '27'
        bcel-id: '33'
        jdb-id: '35'
        globally-id: '00'
        globally-value: 'MBLAOS'
        globally-aid-value: 'A005266284662577'
        iin-id: '01'
        iin-value: 'VMB'
        payment-type-id: '02'
        payment-type-transfer-value: '001'
        payment-type-qr-value: '002'
        receiver-id: '03'
        merchant-category-value: '5311'
        transaction-currency-value: '418'
        country-code-value: 'LA'
        merchant-city-value: 'Vientiane'
        account-number: '01'
        master-merchant-name: '02'
        merchant-id: '03'
        merchant-name: '04'
      merchant-category-id: '52'
      transaction-currency-id: '53'
      transaction-amount-id: '54'
      tip-indicator: '55'
      country-code-id: '58'
      merchant-name: '59'
      merchant-city: '60'
      merchant-account: '50'
      postal-code: '61'
      data-object-id: '62'
      cyclic-redundancy-check-id: '63'
      cyclic-redundancy-check-value: '04'
      payment-qr:
        invoice-id: '01'
        phone-number-id: '02'
        store-label-id: '03'
        loyalty-id: '04'
        referencey-label-id: '05'
        customer-label-id: '06'
        terminal-label-id: '07'
        purpose-transaction-id: '08'
      union-pay-tag:
        id: '15'
        length: '31'
        acquirer-iin: '********' # mã ngân hàng lào
        forwarding-iin: '********' # cố định
    umoney:
      account-bank: MB
      account-number: ************
      account-name: STAR FINTECH SOLE CO.,LTD
      account-type: ACCOUNT
      account-currency: LAK

dynamic-link: # https://your_subdomain.page.link/?link=your_deep_link&apn=package_name[&amv=minimum_version][&afl=fallback_link]
  sub-domain: https://mbbanklaosqa.page.link/
  deep-link: https://mbbanklaosqa.page.link/referrals?referralsCode%3D
  package-name: la.com.mbbank.mobilebanking.dev

customer-support:
  max-number: 10
premium-acc-number:
  fee-code: OTHACC
  sub-product: 719
  default-price: 50000
  discount: 1.2

migrate:
  enable: true
