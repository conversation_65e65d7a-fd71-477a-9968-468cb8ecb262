/**
 * 
 */
package com.mb.laos.service;

import java.util.List;
import com.mb.laos.model.District;
import com.mb.laos.model.Province;
import com.mb.laos.model.Ward;
import com.mb.laos.model.dto.DistrictDTO;
import com.mb.laos.model.dto.NationDTO;
import com.mb.laos.model.dto.ProvinceDTO;
import com.mb.laos.model.dto.WardDTO;

/**
 * <AUTHOR>
 *
 */
public interface AreaService {

	/**
	 * 
	 * @param provinceCode
	 * @param districtCode
	 * @return
	 */
	District getDistrict(String provinceCode, String districtCode);

	/**
	 * @param districtCode
	 * @return
	 */
	String getDistrictName(String districtCode);
	/**
	 * @param provinceCode
	 * @return
	 */
	List<DistrictDTO> getDistricts(String provinceCode);

	/**
	 * 
	 * @param provinceCode
	 * @return
	 */
	Province getProvince(String provinceCode);
	
	/**
	 * 
	 * @param provinceCode
	 * @return
	 */
	String getProvinceName(String provinceCode);

	/**
	 * @return
	 */
	List<ProvinceDTO> getProvinces();
	/**
	 * 
	 * @param districtCode
	 * @param wardCode
	 * @return
	 */
	Ward getWard(String districtCode, String wardCode);

	/**
	 * @param wardCode
	 * @return
	 */
	String getWardName(String wardCode);
	/**
	 * @param districtCode
	 * @return
	 */
	List<WardDTO> getWards(String districtCode);
	
	/**
	 * @return
	 */
	List<NationDTO> getNations();

	/**
	 * getNationNameByCode
	 *
	 * @param nationCode String
	 * @return String
	 */
	String getNationNameByCode(String nationCode);
}
