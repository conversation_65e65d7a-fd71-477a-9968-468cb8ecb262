package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.search.annotations.Analyze;
import org.hibernate.search.annotations.Analyzer;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Index;
import org.hibernate.search.annotations.Store;
import org.hibernate.search.annotations.TermVector;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "EX_NATION")
public class Nation extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -4347596415105150530L;

    @Id
    @Column(name = "NATION_CODE", unique = true, nullable = false, length = 5)
    private String nationCode;

    @Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES,
                    analyzer = @Analyzer(definition = "edgeNGram"), store = Store.YES)
    @Column(name = "NAME", nullable = false, length = 255)
    private String name;

    @Column(name = "SHORT_NAME", length = 255)
    private String shortName;

    @Column(name = "DESCRIPTION", length = 255)
    private String description;

    @Column(name = "STATUS", nullable = false, columnDefinition = "tinyint(1) default 1 ")
    private int status;
}
