/**
 * 
 */
package com.mb.laos.repository.extend;

import java.util.List;
import org.springframework.cache.annotation.Cacheable;
import com.mb.laos.cache.util.AreaCacheConstants;
import com.mb.laos.model.Province;

/**
 * <AUTHOR>
 *
 */
public interface ProvinceRepositoryExtend {
	/**
	 * @return
	 */
	@Cacheable(cacheNames = AreaCacheConstants.Province.FIND_BY_HIGHLIGHT,
			key = "T(com.mb.laos.cache.util.AreaCacheConstants).KEY", unless = "#result == null")
	List<Province> findByHighlight();
}
