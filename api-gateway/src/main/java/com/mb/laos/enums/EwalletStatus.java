package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum EwalletStatus {
    ACTIVE("ACTIVE"),
    WAIT_CANCEL("WAIT CANCEL"),
    LOCKED("LOCKED"),
    BL<PERSON>KED("BLOCKED"),
    REGISTER("REGISTER"),
    INVALID_PIN("INVALID PIN");

    private String value;

    public static EwalletStatus valueOfStatus(String value) {
        for (EwalletStatus status : EwalletStatus.values()) {
            if (Objects.equals(status.getValue(), value)) return status;
        }

        return null;
    }

    public static List<String> getWalletPayment() {
        List<String> result = new ArrayList();
        result.add(ACTIVE.value);
        result.add(LOCKED.value);
        result.add(INVALID_PIN.value);
        result.add(REGISTER.value);
        return result;
    }
}
