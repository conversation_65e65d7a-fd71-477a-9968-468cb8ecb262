package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mb.laos.api.request.Request;
import com.mb.laos.enums.AccountType;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.util.DateUtil;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
public class LviBuyHealthInsuranceRequest extends Request implements Serializable {
    private static final long serialVersionUID = 295349825831970836L;

    @NotBlank(message = LabelKey.ERROR_FULLNAME_IS_EMPTY)
    @Pattern(regexp = ValidationConstraint.PATTERN.CSV_INJECTION_REGEX, message = LabelKey.ERROR_INSURED_NAME_CSV_INJECTION)
    private String insuredName;

    @NotBlank(message = LabelKey.ERROR_PHONE_NUMBER_IS_REQUIRED)
    @Pattern(regexp = ValidationConstraint.PATTERN.PHONE_NUMBER_LAOS_REGEX, message = LabelKey.ERROR_PHONE_NUMBER_IS_INVALID)
    @Pattern(regexp = ValidationConstraint.PATTERN.CSV_INJECTION_REGEX, message = LabelKey.ERROR_INSURED_PHONE_CSV_INJECTION)
    private String insuredPhoneNumber;

    @NotNull(message = LabelKey.ERROR_DATE_OF_BIRTH_IS_REQUIRED)
    @JsonFormat(pattern = DateUtil.SHORT_TIMESTAMP_PATTERN)
    private LocalDate insuredBirthDay;

    @NotBlank(message = LabelKey.ERROR_ID_NUMBER_IS_EMPTY)
    @Pattern(regexp = ValidationConstraint.PATTERN.CSV_INJECTION_REGEX, message = LabelKey.ERROR_INSURED_ID_NUMBER_CSV_INJECTION)
    private String insuredId;

    @NotBlank(message = LabelKey.ERROR_PACKAGE_EMPTY)
    @Pattern(regexp = ValidationConstraint.PATTERN.CSV_INJECTION_REGEX, message = LabelKey.ERROR_PACKAGE_CODE_CSV_INJECTION)
    private String packageCode;

    @NotBlank(message = LabelKey.ERROR_DELIVERY_CODE_EMPTY)
    @Pattern(regexp = ValidationConstraint.PATTERN.CSV_INJECTION_REGEX, message = LabelKey.ERROR_DELIVERY_CODE_CSV_INJECTION)
    private String deliveryCode;

    @NotNull(message = LabelKey.ERROR_ID_CARD_TYPE_IS_EMPTY)
    private Long idCardTypeId;

    @NotBlank(message = LabelKey.ERROR_ACCOUNT_NUMBER_IS_REQUIRED)
    @Size(max = ValidationConstraint.LENGTH.ACCOUNT_NUMBER, message = LabelKey.ERROR_ACCOUNT_NUMBER_MAX_LENGTH)
    private String customerAccNumber;

    @NotBlank(message = LabelKey.ERROR_CUSTOMER_CURRENCY_IS_REQUIRED)
    @Size(max = ValidationConstraint.LENGTH.CURRENCY, message = LabelKey.ERROR_CURRENCY_MAX_LENGTH)
    private String customerCurrency;

    private AccountType type = AccountType.ACCOUNT;

    private OtpConfirmType otpConfirmType = OtpConfirmType.SMS;

    private boolean setDefaultAccount;
}
