package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.api.response.ListElectricMmoneyResponse;
import com.mb.laos.api.response.ListWaterMmoneyResponse;
import com.mb.laos.gateway.request.BeneficiaryConfirmRequest;
import com.mb.laos.gateway.request.BillingHistoryRequest;
import com.mb.laos.gateway.request.BillingRequest;
import com.mb.laos.gateway.request.BillingV2Request;
import com.mb.laos.gateway.request.CashoutRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request;
import com.mb.laos.gateway.request.DebitMoneyRequest;
import com.mb.laos.gateway.request.DepositMoneyRequest;
import com.mb.laos.gateway.request.InfoQrMerchantRequest;
import com.mb.laos.gateway.request.OtpBillingRequest;
import com.mb.laos.gateway.request.OtpBillingV2Request;
import com.mb.laos.gateway.request.OtpCashInRequest;
import com.mb.laos.gateway.request.OtpQrBillRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.request.OtpTransferTransRequest;
import com.mb.laos.gateway.request.ResendOtpRequest;
import com.mb.laos.gateway.request.TransactionHistoryRequest;
import com.mb.laos.gateway.response.BeneficiaryInfoResponse;
import com.mb.laos.gateway.response.BillingHistoryResponse;
import com.mb.laos.gateway.response.BillingHistoryV2Response;
import com.mb.laos.gateway.response.BillingResponse;
import com.mb.laos.gateway.response.CashInHistoryResponse;
import com.mb.laos.gateway.response.CashoutResponse;
import com.mb.laos.gateway.response.DebitMoneyResponse;
import com.mb.laos.gateway.response.DepositMoneyResponse;
import com.mb.laos.gateway.response.InfoQrMerchantResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.TransactionHistoryDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.model.search.CashInHistorySearch;
import com.mb.laos.model.search.TransactionSearchRequest;
import com.mb.laos.service.TransferService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/transfer-transaction")
@RequiredArgsConstructor
public class TransferTransController {

    private final TransferService transferService;

    @InboundRequestLog
    @PostMapping("/confirm-beneficiary")
    public ResponseEntity<BeneficiaryInfoResponse> confirmBeneficiary(
            @RequestBody @Valid BeneficiaryConfirmRequest beneficiaryConfirmRequest,
            HttpServletRequest request) {
        return ResponseEntity.ok().body(this.transferService.confirmBeneficiary(beneficiaryConfirmRequest));
    }

    @InboundRequestLog
    @PostMapping("/v2/confirm-billing")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpBillingV2(HttpServletRequest request,
                                                                          @RequestBody ConfirmOtpBillingV2Request confirmRequest) {
        return ResponseEntity.ok(this.transferService.confirmOtpBilling(confirmRequest, request));
    }

    @Deprecated
    @InboundRequestLog
    @PostMapping("/confirm-billing")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpBilling(HttpServletRequest request,
                                                                        @RequestBody ConfirmOtpBillingRequest confirmRequest) {
        return ResponseEntity.ok(this.transferService.confirmOtpBilling(confirmRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/confirm-cash-in")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpCashIn(HttpServletRequest request,
                                                                       @RequestBody @Valid OtpTransferConfirmRequest confirmRequest) {
        return ResponseEntity.ok(this.transferService.confirmOtpCashIn(confirmRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/confirm-qr-payment")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpQrPayment(HttpServletRequest request,
                                                                          @RequestBody @Valid OtpTransferConfirmRequest confirmRequest) {
        return ResponseEntity.ok(this.transferService.confirmOtpQrPayment(confirmRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/confirm-otp")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpTransfer(HttpServletRequest request,
                                                                         @RequestBody @Valid OtpTransferConfirmRequest confirmRequest) {
        return ResponseEntity.ok(this.transferService.confirmOtpTransfer(confirmRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/history")
    public ResponseEntity<List<TransactionHistoryDTO>> getTransactionHistory(HttpServletRequest request,
                                                                             @RequestBody @Valid TransactionHistoryRequest transactionHistoryRequest) {
        return ResponseEntity.ok(this.transferService.getHistory(transactionHistoryRequest));
    }

    @InboundRequestLog
    @PostMapping("/billing/history")
    public ResponseEntity<List<BillingHistoryResponse>> historyBilling(HttpServletRequest request,
                                                                       @RequestBody @Valid BillingHistoryRequest historyRequest) {
        return ResponseEntity.ok(this.transferService.getHistoryBilling(historyRequest));
    }

    @InboundRequestLog
    @PostMapping("/v2/billing/history")
    public ResponseEntity<Set<BillingHistoryV2Response>> historyBilling(HttpServletRequest request,
                                                                        @RequestBody @Valid BillingHistoryV2Search historyV2Search) {
        return ResponseEntity.ok(this.transferService.getHistoryBillingV2(historyV2Search));
    }

    @InboundRequestLog
    @PostMapping("/v2/billing/verify-billing-code")
    public ResponseEntity<BillingResponse> inquiryPhoneNumberV2(HttpServletRequest request,
                                                                @RequestBody @Valid BillingV2Request billingRequest) {
        return ResponseEntity.ok(this.transferService.verifyBillingCode(billingRequest));
    }

    @Deprecated
    @InboundRequestLog
    @PostMapping("/billing/verify-billing-code")
    public ResponseEntity<BillingResponse> inquiryPhoneNumber(HttpServletRequest request,
                                                              @RequestBody @Valid BillingRequest billingRequest) {
        return ResponseEntity.ok(this.transferService.verifyBillingCode(billingRequest));
    }

    @Deprecated
    @InboundRequestLog
    @PostMapping("/billing")
    public ResponseEntity<OtpTransferTransResponse> requestOtpBilling(HttpServletRequest request,
                                                                      @RequestBody @Valid OtpBillingRequest otpRequest) {
        return ResponseEntity.ok(this.transferService.requestOtpBilling(otpRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/v2/billing")
    public ResponseEntity<OtpTransferTransResponse> requestOtpBillingV2(HttpServletRequest request,
                                                                        @RequestBody @Valid OtpBillingV2Request otpRequest) {
        return ResponseEntity.ok(this.transferService.requestOtpBilling(otpRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/cash-in")
    public ResponseEntity<OtpTransferTransResponse> requestOtpCashIn(HttpServletRequest request,
                                                                     @RequestBody @Valid OtpCashInRequest otpRequest) {
        return ResponseEntity.ok(this.transferService.requestOtpCashIn(otpRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/request-qr-payment")
    public ResponseEntity<OtpTransferTransResponse> requestOtpQrPayment(HttpServletRequest request,
                                                                        @RequestBody @Valid OtpQrBillRequest otpRequest) {
        return ResponseEntity.ok(this.transferService.requestOtpQrPayment(otpRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/request-otp")
    public ResponseEntity<OtpTransferTransResponse> requestOtpTransfer(HttpServletRequest request,
                                                                       @RequestBody @Valid OtpTransferTransRequest otpRequest) {
        return ResponseEntity.ok(this.transferService.requestOtpTransfer(otpRequest, request));
    }

    @InboundRequestLog
    @PostMapping("/resend-otp")
    public ResponseEntity<OtpTransferTransResponse> resendOtp(HttpServletRequest request,
                                                              @RequestBody @Valid ResendOtpRequest resendOtpRequest) {
        return ResponseEntity.ok(this.transferService.resendOtp(resendOtpRequest));
    }

    @InboundRequestLog
    @PostMapping("/qr/verify-info-merchant")
    public ResponseEntity<InfoQrMerchantResponse> verifyInformationQR(HttpServletRequest request,
                                                                      @RequestBody @Valid InfoQrMerchantRequest infoQrMerchantRequest) {
        return ResponseEntity.ok(this.transferService.getInfoQrMerchant(infoQrMerchantRequest));
    }

    @InboundRequestLog
    @PostMapping("/qr/history")
    public ResponseEntity<Page<TransactionDTO>> getQrCodeTransactionHistory(HttpServletRequest request,
                                                                            @RequestBody @Valid TransactionSearchRequest transactionSearchRequest, Pageable pageable) {
        return ResponseEntity.ok().body(this.transferService.getQrHistory(transactionSearchRequest, pageable));
    }

    @InboundRequestLog
    @PostMapping("/qr/history/detail")
    public ResponseEntity<TransactionDTO> getDetailQrCodeTransactionHistory(HttpServletRequest request,
                                                                            @RequestBody TransactionSearchRequest transactionSearchRequest) {
        return ResponseEntity.ok(this.transferService.getDetailQrHistory(transactionSearchRequest));
    }

    @InboundRequestLog
    @PostMapping("/client/confirm-beneficiary")
    public ResponseEntity<BeneficiaryInfoResponse> clientConfirmBeneficiary(
            @RequestBody @Valid BeneficiaryConfirmRequest beneficiaryConfirmRequest,
            HttpServletRequest request) {
        return ResponseEntity.ok().body(this.transferService.clientConfirmBeneficiary(beneficiaryConfirmRequest));
    }

    @InboundRequestLog
    @PostMapping("/client/cashout")
    public ResponseEntity<CashoutResponse> cashOut(HttpServletRequest request,
                                                   @RequestBody CashoutRequest cashoutRequest) {
        return ResponseEntity.ok(this.transferService.cashout(request, cashoutRequest));
    }

    @InboundRequestLog
    @PostMapping("/client/debit-money")
    public ResponseEntity<DebitMoneyResponse> debitMoney(HttpServletRequest request,
                                                         @RequestBody @Valid DebitMoneyRequest debitMoneyRequest) {
        return ResponseEntity.ok(this.transferService.debitMoney(request, debitMoneyRequest));
    }

    @InboundRequestLog
    @PostMapping("/client/deposit-money")
    public ResponseEntity<DepositMoneyResponse> depositMoney(HttpServletRequest request,
                                                             @RequestBody @Valid DepositMoneyRequest depositMoneyRequest) {
        return ResponseEntity.ok(this.transferService.depositMoney(request, depositMoneyRequest));
    }

    @InboundRequestLog
    @PostMapping("/cash-in/history")
    public ResponseEntity<Set<CashInHistoryResponse>> historyCashIn(HttpServletRequest request,
                                                                    @RequestBody @Valid CashInHistorySearch historySearch) {
        return ResponseEntity.ok(this.transferService.getHistoryCashIn(historySearch));
    }
}
