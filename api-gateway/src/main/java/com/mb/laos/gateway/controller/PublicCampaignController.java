package com.mb.laos.gateway.controller;

import com.mb.laos.model.dto.CampaignDTO;
import com.mb.laos.service.CampaignService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@RequestMapping("/public/campaign")
@RequiredArgsConstructor
public class PublicCampaignController {
    private final CampaignService campaignService;


    @PostMapping("/active")
    public ResponseEntity<List<CampaignDTO>> getActiveCampaign(HttpServletRequest request, @RequestBody CampaignDTO campaignDTO) {
        return new ResponseEntity<>(this.campaignService.getActiveCampaign(campaignDTO.getPosition()), HttpStatus.OK);
    }

    @PostMapping("/banner/{imageId}/{normalizeName}")
    public ResponseEntity<InputStreamResource> getBannerImage(HttpServletRequest request, @RequestBody CampaignDTO dto,
                                                              @PathVariable("imageId") Long imageId, @PathVariable("normalizeName") String normalizeName) {
        return this.campaignService.getBannerImage(dto.getCampaignId(), imageId, normalizeName);
    }

    @PostMapping("/banner/fee")
    public ResponseEntity<InputStreamResource> getBannerImage(HttpServletRequest request) {
        return this.campaignService.getFeeBanner();
    }
}
