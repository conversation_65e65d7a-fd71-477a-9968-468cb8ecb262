package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DotpVerifyProvisionRegisterRequest extends Request {

    @ApiModelProperty(required = true)
    @NotBlank(message = LabelKey.ERROR_DOTP_MUST_NOT_BE_EMPTY)
    private String dotp;

    @ApiModelProperty(required = true)
    @NotBlank(message = LabelKey.ERROR_DEVICE_ID_MUST_NOT_BE_EMPTY)
    private String deviceId;
}
