package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MBOtpTransferRequest extends Request {
	/**
	 *
	 */
	private static final long serialVersionUID = -6537119608022933878L;

	private String mobile;

	private String debitAccount;

	private String creditAccount;

	private String amount;

	private String fee;

	private String currency;

	private String remark;

	private String otpValue;
}
