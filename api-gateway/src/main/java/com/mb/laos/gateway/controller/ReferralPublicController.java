package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.model.dto.ReferralDTO;
import com.mb.laos.model.search.ReferralSearch;
import com.mb.laos.request.ReferralCodeVerifyRequest;
import com.mb.laos.service.ReferralService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/public/referral")
@RequiredArgsConstructor
public class ReferralPublicController {

    private final ReferralService referralService;

    @PostMapping("/verify-referral-code-active")
    public ResponseEntity<ReferralDTO> verifyReferralCodeActive(@RequestBody @Valid ReferralCodeVerifyRequest request) {
        return ResponseEntity.ok().body(this.referralService.verifyReferralCodeActive(request.getReferralCode()));
    }

    @InboundRequestLog
    @PostMapping("/info")
    public ResponseEntity<ReferralDTO> info(HttpServletRequest request, @Valid @RequestBody ReferralSearch search) {
        return ResponseEntity.ok().body(this.referralService.info(search));
    }

}
