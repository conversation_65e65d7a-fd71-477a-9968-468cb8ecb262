package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.response.Response;
import com.mb.laos.enums.ConfigurationFeeType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillingResponse extends Response {
    private static final long serialVersionUID = -1431837812067487546L;

    private Integer status;

    private String networkType;

    private Long balance;

    private String fullName;

    private String type;

    private double transactionFee;

    private double transactionDiscount;

    private long totalAmount;

    private long discountFixed;

    private ConfigurationFeeType configurationFeeType;

    private LocalDateTime billingAt;

    private String provinceCode;

    private String titleLa;

    private String titleEn;

}
