package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.response.Response;
import lombok.Builder;
import lombok.Data;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PhoneInquiryResponse extends Response {
    private String fullName;

    private Integer status;

    private String networkType;

    private String balance;
}
