package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentWaterAndElectricRequest extends Request {
    private static final long serialVersionUID = 6008080343994332932L;

    private String accName;

    private String transactionId;

    private String accNo;

    private Long amount;

    private Integer ewId;

    private String proCode;

    private String phoneUser;

    private String title;

    private String remark;
}
