package com.mb.laos.gateway.controller;

import javax.servlet.http.HttpServletRequest;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.model.dto.BeneficiaryRecentlyDTO;
import com.mb.laos.request.BeneficiaryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.mb.laos.model.dto.BeneficiaryDTO;
import com.mb.laos.model.search.BeneficiarySearch;
import com.mb.laos.service.BeneficiaryService;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/beneficiary")
@RequiredArgsConstructor
public class BeneficiaryController {
    private final BeneficiaryService beneficiaryService;

    @PostMapping("/search")
    public ResponseEntity<Page<BeneficiaryDTO>> search(HttpServletRequest request,
                    @RequestBody BeneficiarySearch beneficiarySearch, Pageable pageable) {
        return ResponseEntity.ok(this.beneficiaryService.search(beneficiarySearch, pageable));
    }
    
    @PostMapping("/search/recently")
    public ResponseEntity<Page<BeneficiaryDTO>> searchRecently(HttpServletRequest request,
                    @RequestBody BeneficiarySearch beneficiarySearch, Pageable pageable) {
        return ResponseEntity.ok(this.beneficiaryService.searchRecently(beneficiarySearch, pageable));
    }

    @PostMapping("/v2/search/recently")
    public ResponseEntity<Page<BeneficiaryRecentlyDTO>> searchRecentlyV2(HttpServletRequest request, Pageable pageable) {
        return ResponseEntity.ok(this.beneficiaryService.searchRecentlyV2(pageable));
    }

    @InboundRequestLog
    @PostMapping("/delete")
    public ResponseEntity<Void>delete(@RequestBody BeneficiaryRequest request, HttpServletRequest httpServletRequest) {
        this.beneficiaryService.delete(request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @InboundRequestLog
    @PostMapping("/update")
    public ResponseEntity<Void> update(@RequestBody BeneficiaryRequest request, HttpServletRequest httpServletRequest) {
        this.beneficiaryService.update(request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
