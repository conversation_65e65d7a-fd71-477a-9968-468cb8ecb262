package com.mb.laos.service;

import com.mb.laos.model.dto.BeneficiaryRecentlyDTO;
import com.mb.laos.request.BeneficiaryRequest;
import com.mb.laos.model.Beneficiary;
import com.mb.laos.model.dto.TransactionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.mb.laos.model.dto.BeneficiaryDTO;
import com.mb.laos.model.search.BeneficiarySearch;

public interface BeneficiaryService {

    /**
     * tìm kiếm tài khoản thụ hưởng
     *
     * @param beneficiarySearch
     * @return
     */
    Page<BeneficiaryDTO> search(BeneficiarySearch params, Pageable pageable);

    /**
     * Danh sách người thụ hưởng gần đây
     * @param beneficiarySearch
     * @param pageable
     * @return
     */
    Page<BeneficiaryDTO> searchRecently(BeneficiarySearch params, Pageable pageable);

    /**
     * Danh sách người thụ hưởng gần đây V2
     * @param beneficiarySearch
     * @param pageable
     * @return
     */
    Page<BeneficiaryRecentlyDTO> searchRecentlyV2(Pageable pageable);

    void delete(BeneficiaryRequest request);

    void update(BeneficiaryRequest request);

    Beneficiary createBeneficiary(TransactionDTO request);
}
