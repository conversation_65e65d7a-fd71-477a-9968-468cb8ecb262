package com.mb.laos.service;


import com.mb.laos.gateway.request.DotpCheckDeviceRegisteredRequest;
import com.mb.laos.gateway.request.DotpConfirmOtpRequest;
import com.mb.laos.gateway.request.DotpConfirmQrTokenRequest;
import com.mb.laos.gateway.request.DotpDeleteDeviceRequest;
import com.mb.laos.gateway.request.DotpVerifyProvisionRegisterRequest;
import com.mb.laos.gateway.request.DotpVerifyQrCodeRequest;
import com.mb.laos.gateway.request.SmsDotpRequest;
import com.mb.laos.gateway.response.DotpResponse;
import com.mb.laos.model.dto.DeviceInfoDTO;
import com.mb.laos.model.dto.DotpDTO;
import com.mb.laos.model.dto.QrCodeGeneralDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface DotpService {

    /**
     * <PERSON><PERSON><PERSON> tra thiết bị đã đăng ký D-O<PERSON> chưa
     *
     * @return Boolean
     */
    Boolean checkDeviceRegistered(DotpCheckDeviceRegisteredRequest registeredRequest, HttpServletRequest request);

    /**
     * Yêu cầu đăng ký D-OTP trên thiết bị mới
     *
     * @return QrCodeGeneralDTO
     */
    QrCodeGeneralDTO initQrToken(DotpCheckDeviceRegisteredRequest registeredRequest, HttpServletRequest request);

    /**
     * Verify QR code trên thiết bị cũ
     *
     * @param request VerifyQrCodeDOTPRequest
     */
    DotpResponse verifyQrCodeOnOldDevice(DotpVerifyQrCodeRequest request);

    /**
     * Xác thực mã dotp thiết bị cũ trên thiết bị mới
     *
     * @param request DotpConfirmQrTokenRequest
     */
    DotpResponse confirmQrToken(DotpConfirmQrTokenRequest qrTokenRequest, HttpServletRequest request);

    /**
     * Yêu cầu SMS OTP để đăng ký D-OTP
     * @param smsDotpRequest
     * @param request
     */
    void sendSMS(SmsDotpRequest smsDotpRequest, HttpServletRequest request);

    /**
     * Lấy thông tin Token và encKey để đăng ký D-OTP lần đầu
     * @param dotpConfirmOtpRequest
     * @param request
     * @return
     */
    DotpResponse getInfoToRegister(DotpConfirmOtpRequest dotpConfirmOtpRequest, HttpServletRequest request);

    /**
     * Xác thực D-OTP lần đầu tiên
     * @param dotpVerifyProvisionRequest
     * @param request
     */
    void verifyProvision(DotpVerifyProvisionRegisterRequest dotpVerifyProvisionRequest, HttpServletRequest request);

    /**
     * Danh sách thiết bị đã đăng ký D-OTP
     * @param request
     */
    List<DotpDTO> findRegisterDevice(HttpServletRequest request);

    /**
     * Xoá thiết bị đã đăng ký D-OTP
     * @param deleteDeviceRequest
     * @param request
     */
    List<DeviceInfoDTO> deleteDevice(HttpServletRequest request, DotpDeleteDeviceRequest deleteDeviceRequest);

    DotpDTO getCustomerDevice(String deviceId);

    /**
     * Yêu cầu xoá thiết bị đã đăng ký D-OTP
     * @param requestCancel
     * @param request
     */
    DotpResponse requestCancel(HttpServletRequest request, SmsDotpRequest requestCancel);
}
