package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.InquiryTransactionStatusRequest;
import com.mb.laos.api.request.ListElectricMmoneyRequest;
import com.mb.laos.api.request.ListWaterMmoneyRequest;
import com.mb.laos.api.request.PaymentElectricRequest;
import com.mb.laos.api.request.PaymentWaterMmoneyRequest;
import com.mb.laos.api.request.RevertTransactionRequest;
import com.mb.laos.api.request.VerifyElectricMmoneyRequest;
import com.mb.laos.api.request.VerifyWaterMmoneyRequest;
import com.mb.laos.api.response.ListElectricMmoneyResponse;
import com.mb.laos.api.response.ListWaterMmoneyResponse;
import com.mb.laos.api.response.PaymentElectricMmoneyResponse;
import com.mb.laos.api.response.PaymentWaterMmoneyResponse;
import com.mb.laos.api.response.VerifyElectricMmoneyResponse;
import com.mb.laos.api.response.VerifyWaterMmoneyResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.MBApiConstantProperties;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.MmoneyServiceName;
import com.mb.laos.enums.TransactionFeeTypeCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request;
import com.mb.laos.gateway.request.OtpUtilityRequest;
import com.mb.laos.gateway.request.UtilityRequest;
import com.mb.laos.gateway.response.BillingHistoryV2Response;
import com.mb.laos.gateway.response.BillingResponse;
import com.mb.laos.gateway.response.FeeTransactionResponse;
import com.mb.laos.gateway.response.MBOtpTransConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.model.Customer;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionMerchant;
import com.mb.laos.model.Utility;
import com.mb.laos.model.dto.InquiryTransactionStatusDTO;
import com.mb.laos.model.dto.MmoneyBillingDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.UtilityDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.repository.MerchantRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.ServicePackRepository;
import com.mb.laos.repository.TransactionMerchantRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.repository.UtilityRepository;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ApiMmoneyService;
import com.mb.laos.service.MmoneyEncryptService;
import com.mb.laos.service.TransactionService;
import com.mb.laos.service.TransferService;
import com.mb.laos.service.UtilityService;
import com.mb.laos.service.mapper.TransactionMapper;
import com.mb.laos.service.mapper.UtilityMapper;
import com.mb.laos.util.GetterUtil;
import com.mb.laos.util.NumberUtil;
import com.mb.laos.util.RandomGenerator;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UtilityServiceImpl implements UtilityService {

    private final ApiMmoneyService apiMmoneyService;
    private final MoneyAccountRepository moneyAccountRepository;
    private final MmoneyEncryptService mmoneyEncryptService;
    private final ConsumerProperties consumerProperties;
    private final TransactionService transactionService;
    private final MerchantRepository merchantRepository;
    private final CustomerProperties customerProperties;
    private final TransactionMerchantRepository transactionMerchantRepository;
    private final TransactionMapper transactionMapper;
    private final ApiGeeTransferService apiGeeTransferService;
    private final TransactionRepository transactionRepository;
    private final MBApiConstantProperties mbApiConstantProperties;
    private final TransferService transferService;
    private final UtilityRepository utilityRepository;
    private final UtilityMapper utilityMapper;

    private final ServicePackRepository servicePackRepository;

    @Override
    public List<ListWaterMmoneyResponse> waterSuppliers(HttpServletRequest request) {
        this.getCustomerLogin();
        return this.apiMmoneyService.listWater(ListWaterMmoneyRequest.builder().build());
    }

    @Override
    public List<ListElectricMmoneyResponse> electricSupplies(HttpServletRequest request) {
        this.getCustomerLogin();
        return this.apiMmoneyService.listElectric(ListElectricMmoneyRequest.builder().build());
    }

    @Override
    public BillingResponse verifyBillingCode(UtilityRequest billingRequest) {

        Customer customer = this.getCustomerLogin();
        LocalDateTime now = LocalDateTime.now();

        BillingResponse billingResponse = BillingResponse.builder().billingAt(now).build();
        BillingType billingType = BillingType.getBillingType(billingRequest.getBillingType());

        MoneyAccount moneyAccount = moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(billingRequest.getAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1035));

        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        String fullName;
        long amount;
        String transactionId = RandomGenerator.generateRandomNumber(1, 20);
        String phoneUser = NumberUtil.formatPhoneNumber(customer.getUsername());

        MmoneyBillingDTO mmoneyBillingDTO = this.verifySupplies(billingRequest.getProviderId(), billingType.name());

        if (Validator.equals(billingType.getType(), BillingType.WATER.getType())) {
            VerifyWaterMmoneyResponse verifyWaterMmoneyResponse = null;

            verifyWaterMmoneyResponse = this.apiMmoneyService.verifyWater(
                    VerifyWaterMmoneyRequest.builder()
                            .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                            .transactionId(transactionId)
                            .phoneUser(this.mmoneyEncryptService.encrypt(phoneUser))
                            .accNo(mmoneyEncryptService.encrypt(billingRequest.getBillingCode()))
                            .ewId(billingRequest.getProviderId())
                            .build()
            );

            if (Validator.isNull(verifyWaterMmoneyResponse) || !verifyWaterMmoneyResponse.isSuccess()) {
                throw new BadRequestAlertException(ErrorCode.MSG3004);
            }

            amount = GetterUtil.getLong(verifyWaterMmoneyResponse.getDebit());
            fullName = mmoneyEncryptService.decrypt(verifyWaterMmoneyResponse.getAccName());

        } else if (Validator.equals(billingType.getType(), BillingType.ELECTRIC.getType())) {
            VerifyElectricMmoneyResponse verifyElectricMmoneyResponse = null;

            verifyElectricMmoneyResponse = this.apiMmoneyService.verifyElectric(
                    VerifyElectricMmoneyRequest.builder()
                            .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                            .transactionId(transactionId)
                            .phoneUser(this.mmoneyEncryptService.encrypt(phoneUser))
                            .accNo(mmoneyEncryptService.encrypt(billingRequest.getBillingCode()))
                            .ewId(billingRequest.getProviderId())
                            .build()
            );

            if (Validator.isNull(verifyElectricMmoneyResponse) || !verifyElectricMmoneyResponse.isSuccess()) {
                throw new BadRequestAlertException(ErrorCode.MSG3004);
            }

            amount = GetterUtil.getLong(verifyElectricMmoneyResponse.getDebit());
            fullName = mmoneyEncryptService.decrypt(verifyElectricMmoneyResponse.getAccName());

        } else {
            throw new BadRequestAlertException(ErrorCode.MSG1170);
        }

        billingResponse.setBalance(amount);
        billingResponse.setFullName(fullName);
        billingResponse.setProvinceCode(mmoneyBillingDTO.getProCode());
        billingResponse.setTitleLa(mmoneyBillingDTO.getTitleLa());
        billingResponse.setTitleEn(mmoneyBillingDTO.getTitleEn());

        if (Validator.isNull(billingResponse.getBalance()) || billingResponse.getBalance() == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101021);
        }

        // Tính phí giao dịch
        FeeTransactionResponse feeTransactionResponse;
        if (BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType()) != null) {
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.valueOf(BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType())),
                    billingResponse.getBalance(), this.customerProperties.getDefaultCurrency());

            billingResponse.setTransactionDiscount(feeTransactionResponse.getDiscount());
            billingResponse.setTransactionFee(feeTransactionResponse.getFee());
            billingResponse.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
            billingResponse.setDiscountFixed(feeTransactionResponse.getDiscountFixed());

        }

        long totalAmount = Math.round(billingResponse.getBalance() + billingResponse.getTransactionFee() - billingResponse.getDiscountFixed() - billingResponse.getBalance() * billingResponse.getTransactionDiscount() / 100);

        // Số tiền theo công thức nhỏ hơn tiền bill => số tiền thanh toán = bill
        if (totalAmount < billingResponse.getBalance()) {
            totalAmount = billingResponse.getBalance();
        }

        billingResponse.setTotalAmount(totalAmount);
        billingResponse.setStatus(
                moneyAccount.getAvailableAmount() < billingResponse.getTotalAmount() ?
                        EntityStatus.INACTIVE.getStatus() : EntityStatus.ACTIVE.getStatus());

        return billingResponse;
    }

    private MmoneyBillingDTO verifySupplies(Integer ewId, String billingType) {
        MmoneyBillingDTO mmoneyBillingDTO = new MmoneyBillingDTO();

        if (Validator.equals(BillingType.WATER.name(), billingType)) {
            List<ListWaterMmoneyResponse> list = this.apiMmoneyService.listWater(ListWaterMmoneyRequest.builder().build());

            ListWaterMmoneyResponse waterResponse = list.stream().filter(item -> Validator.equals(ewId, item.getProviderId())).findFirst()
                    .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101103));

            mmoneyBillingDTO.setProCode(waterResponse.getCode());
            mmoneyBillingDTO.setEwId(waterResponse.getProviderId());
            mmoneyBillingDTO.setTitleLa(waterResponse.getTitleLA());
            mmoneyBillingDTO.setTitleEn(waterResponse.getTitleEN());
        } else {
            List<ListElectricMmoneyResponse> list = this.apiMmoneyService.listElectric(ListElectricMmoneyRequest.builder().build());
            ListElectricMmoneyResponse electricResponse = list.stream().filter(item -> Validator.equals(ewId, item.getProviderId())).findFirst()
                    .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101103));

            mmoneyBillingDTO.setProCode(electricResponse.getCode());
            mmoneyBillingDTO.setEwId(electricResponse.getProviderId());
            mmoneyBillingDTO.setTitleLa(electricResponse.getTitleLA());
            mmoneyBillingDTO.setTitleEn(electricResponse.getTitleEN());
        }

        return mmoneyBillingDTO;
    }

    @Override
    public OtpTransferTransResponse requestOtpBilling(OtpUtilityRequest request, HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        UtilityRequest billingRequest = new UtilityRequest(request.getBillCode(), request.getAccountNumber(), request.getBillingType(), request.getProviderId());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setConfigurationFeeType(billing.getConfigurationFeeType());
        transactionDTO.setMessage(StringUtil.join(new String[]{"MMONEY", request.getBillCode()}, StringPool.SPACE));
        transactionDTO.setTransactionAmount(Validator.isNotNull(billing.getBalance()) ? billing.getBalance().doubleValue() : billing.getBalance());
        transactionDTO.setTransactionFee(billing.getTransactionFee());
        transactionDTO.setDiscount(billing.getTransactionDiscount());
        transactionDTO.setDiscountFixed(billing.getDiscountFixed());
        transactionDTO.setTotalAmount(Validator.isNotNull(billing.getTotalAmount()) ? (double) billing.getTotalAmount() : 0D);

        OtpTransferTransResponse otpTransferTransResponse = transferService.makeTransferToMerchant(transactionDTO, transactionDTO.getBillingType().name(), customer, request.isSetDefaultAccount());

        UtilityDTO utilityDTO = UtilityDTO.builder()
                .accountName(billing.getFullName())
                .titleLa(billing.getTitleLa())
                .titleEn(billing.getTitleEn())
                .providerId(request.getProviderId())
                .transferTransactionId(otpTransferTransResponse.getTransferTransactionId())
                .provinceCode(billing.getProvinceCode())
                .build();

        this.utilityRepository.save(this.utilityMapper.toEntity(utilityDTO));

        return otpTransferTransResponse;
    }

    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    @Override
    public OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingV2Request request, HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }
        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        Integer ewId = this.utilityRepository.findByTransferTransactionId(transaction.getTransferTransactionId()).getProviderId();

        // gọi lại api check số tiền cần thanh toán
        UtilityRequest billingRequest = new UtilityRequest(transaction.getTarget(),
                transaction.getCustomerAccNumber(), BillingType.valueOf(transaction.getBillingType()).getType(), ewId);
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // kiểm tra lại số tiền bill và số tiền tạo giao dịch có trùng khớp?
        if (!Validator.equals(Long.valueOf(transaction.getTransactionAmount().longValue()), billing.getBalance())) {
            throw new BadRequestAlertException(ErrorCode.MSG1164);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getBillingPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getBillingPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getBillingPayment().getServiceType());

        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        this.transactionService.createNotification(transactionDTO);

        return response;
    }


    private OtpTransferConfirmResponse confirmTransferToMerchant(TransactionDTO transactionDTO, String otp, String deviceId) {

        // kiểm tra trạng thái của giao dịch
        if (Validator.equals(transactionDTO.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        Transaction transaction = this.transactionMapper.toEntity(transactionDTO);

        // limit transaction
        this.transactionService.checkTransactionLimit(String.valueOf(transactionDTO.getTransferType()), transactionDTO.getCustomerId(),
                transactionDTO.getCustomerAccNumber(), transactionDTO.getTransactionAmount(), transactionDTO.getType(), transaction);

        Optional<TransactionMerchant> transactionMerchant = this.transactionMerchantRepository
                .findByTransferTransactionId(transactionDTO.getTransferTransactionId());

        if (!transactionMerchant.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100010);
        }

        Merchant merchant = this.merchantRepository
                .findByMerchantIdAndStatus(transactionMerchant.get().getMerchantId(),
                        EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        MBOtpTransConfirmResponse response = this.transferService.confirmFundTransferMB(transactionDTO, otp, deviceId);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transactionDTO.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {

                transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
                this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            }
        }

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            _log.info("Transaction has been failed, {}, status {}", transactionDTO.getTransactionId(),
                    response.getTransactionStatus());

            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        // gọi sang nhà cung cấp để thanh toán
        this.makeBillPayment(response, transactionDTO, transactionMerchant.get());

        transactionDTO.setTransactionStatus(TransactionStatus.SUCCESS);
        transactionDTO.setTransactionFinishTime(Instant.now());
        transactionDTO.setTransactionCode(response.getT24ReferenceNumber());

        this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now())
                .transactionCode(response.getT24ReferenceNumber())
                .transactionFee(transactionDTO.getTransactionFee())
                .transactionId(transactionDTO.getTransactionId())
                .build();
    }


    private void makeBillPayment(MBOtpTransConfirmResponse response, TransactionDTO transactionDTO, TransactionMerchant transactionMerchant) {
        String transactionId = RandomGenerator.generateRandomNumber(1, 20);

        PaymentWaterMmoneyResponse paymentWaterMmoneyResponse = null;
        PaymentElectricMmoneyResponse paymentElectricMmoneyResponse = null;

        Utility utility = this.utilityRepository.findByTransferTransactionId(transactionDTO.getTransferTransactionId());

        String phoneUser = NumberUtil.formatPhoneNumber(this.getCustomerLogin().getUsername());

        String accName = this.mmoneyEncryptService.encrypt(utility.getAccountName());
        Integer ewId = utility.getProviderId();
        String provinceCode = utility.getProvinceCode();
        String titleLa = utility.getTitleLa();

        if (Validator.equals(BillingType.WATER, transactionDTO.getBillingType())) {
            paymentWaterMmoneyResponse = this.apiMmoneyService.paymentWater(
                    PaymentWaterMmoneyRequest.builder()
                            .accName(accName)
                            .transactionId(transactionId)
                            .accNo(this.mmoneyEncryptService.encrypt(transactionDTO.getTarget()))
                            .amount(Validator.isNotNull(transactionDTO.getTransactionAmount().longValue()) ? transactionDTO.getTransactionAmount().longValue() : 0L)
                            .proCode(provinceCode)
                            .ewId(ewId)
                            .phoneUser(this.mmoneyEncryptService.encrypt(phoneUser))
                            .title(titleLa)
                            .remark(transactionDTO.getMessage())
                            .build()
            );

        } else if (Validator.equals(BillingType.ELECTRIC, transactionDTO.getBillingType())) {
            transactionId = RandomGenerator.generateRandomNumber(1, 20);
            paymentElectricMmoneyResponse = this.apiMmoneyService.paymentElectric(
                    PaymentElectricRequest.builder()
                            .accName(accName)
                            .transactionId(transactionId)
                            .accNo(this.mmoneyEncryptService.encrypt(transactionDTO.getTarget()))
                            .amount(Validator.isNotNull(transactionDTO.getTransactionAmount().longValue()) ? transactionDTO.getTransactionAmount().longValue() : 0L)
                            .ewId(ewId)
                            .proCode(provinceCode)
                            .phoneUser(this.mmoneyEncryptService.encrypt(phoneUser))
                            .title(titleLa)
                            .remark(transactionDTO.getMessage())
                            .build()
            );
        } else {
            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1170);
        }

        // save lai transaction Id phia nha mang de trace log
        transactionMerchant.setMerchantTransactionId(transactionId);
        transactionMerchantRepository.save(transactionMerchant);

        // Nếu Call API thất bại -> revert giao dịch
        if (!(Validator.isNotNull(paymentElectricMmoneyResponse) && paymentElectricMmoneyResponse.isSuccess()
                || Validator.isNotNull(paymentWaterMmoneyResponse) && paymentWaterMmoneyResponse.isSuccess())
        ) {
            boolean isSuccessTrans = false;

            String serviceName = Validator.equals(BillingType.WATER, transactionDTO.getBillingType()) ? MmoneyServiceName.WATER.getServiceName() : MmoneyServiceName.ELECTRIC.getServiceName();
            isSuccessTrans = this.transferService.checkTransactionMmoney(transactionId, serviceName);

            if (Validator.equals(false, isSuccessTrans)
                    || Validator.equals(Objects.requireNonNull(paymentElectricMmoneyResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                    || Validator.equals(Objects.requireNonNull(paymentWaterMmoneyResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
            ) {
                this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                        .t24ReferenceNumber(response.getT24ReferenceNumber())
                        .transactionId(response.getReferenceNumber())
                        .build());

                transactionDTO.setTransactionStatus(TransactionStatus.FAILED);

                this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            }
        }
    }

    @Override
    public List<BillingHistoryV2Response> getHistoryBilling(BillingHistoryV2Search request) {
        BillingType billingType = BillingType.getBillingType(request.getBillingType());

        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        Customer customer = this.getCustomerLogin();

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());
        List<UtilityDTO> utilities = this.utilityRepository.getBillingHistory(request, pageable, customer.getCustomerId());

        return utilities.stream()
                .distinct()
                .limit(request.getPageSize())
                .map(utilityDTO -> BillingHistoryV2Response.builder()
                        .billingCode(utilityDTO.getBillingCode())
                        .telco(utilityDTO.getProvinceCode())
                        .titleEn(utilityDTO.getTitleEn())
                        .build()).collect(Collectors.toList());
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }
}
