package com.mb.laos.service.impl;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.mb.laos.enums.NationCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.model.dto.BeneficiaryRecentlyDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.repository.BankRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.InternationalPaymentRepository;
import com.mb.laos.repository.TransactionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.Resource;
import com.mb.laos.request.BeneficiaryRequest;
import com.mb.laos.model.Bank;
import com.mb.laos.model.Beneficiary;
import com.mb.laos.model.FileEntry;
import com.mb.laos.model.ResultSet;
import com.mb.laos.model.dto.BeneficiaryDTO;
import com.mb.laos.model.search.BeneficiarySearch;
import com.mb.laos.repository.BeneficiaryRepository;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.service.BeneficiaryService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.BeneficiaryMapper;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class BeneficiaryServiceImpl implements BeneficiaryService {
    private final BeneficiaryRepository beneficiaryRepository;

    private final BeneficiaryMapper beneficiaryMapper;

    private final StorageService storageService;

    private final BankRepository bankRepository;

    private final TransactionRepository transactionRepository;

    private final InternationalPaymentRepository internationalPaymentRepository;

    private final CustomerRepository customerRepository;

    @Override
    public Page<BeneficiaryDTO> search(BeneficiarySearch params, Pageable pageable) {
        Long customerId = GwSecurityUtils.getCustomerLoginId()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));

        params.setCustomerId(customerId);
        params.setStatus(EntityStatus.ACTIVE.getStatus());

        ResultSet<Beneficiary> results = this.beneficiaryRepository.searchByKeyword(params, pageable);

//        List<Beneficiary> result = this.beneficiaryRepository.search(params, pageable);

        List<BeneficiaryDTO> beneficiaryDTOS = this.beneficiaryMapper.toDto(results.getResults());

        List<Long> bankIds =
                beneficiaryDTOS.stream().map(BeneficiaryDTO::getBeneficiaryBankId).collect(Collectors.toList());

        if (Validator.isNotNull(bankIds)) {
            beneficiaryDTOS.forEach(beneficiaryDTO -> {
                List<FileEntry> fileEntries =
                        this.storageService.getFileEntries(Bank.class.getName(), beneficiaryDTO.getBeneficiaryBankId(), Resource.ICON);
                if (Validator.isNotNull(fileEntries)) {
                    beneficiaryDTO.setIcon(storageService.getFileInfo(fileEntries.stream().findFirst().get()));
                }
            });
        }

        return new PageImpl<>(beneficiaryDTOS, pageable, this.beneficiaryRepository.count(params));
    }

    @Override
    public Page<BeneficiaryDTO> searchRecently(BeneficiarySearch params, Pageable pageable) {
        Long customerId = GwSecurityUtils.getCustomerLoginId()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));

        params.setCustomerId(customerId);
        params.setStatus(EntityStatus.ACTIVE.getStatus());
        params.setOrderByColumn(Beneficiary.SortName.LAST_MODIFIED_DATE_SORT);
        params.setOrderByType(QueryUtil.DESC);

        ResultSet<Beneficiary> results = this.beneficiaryRepository.searchByKeyword(params, pageable);

        List<BeneficiaryDTO> beneficiaryDTOS = this.beneficiaryMapper.toDto(results.getResults());

        List<Long> bankIds =
                beneficiaryDTOS.stream().map(BeneficiaryDTO::getBeneficiaryBankId).collect(Collectors.toList());

        if (Validator.isNotNull(bankIds)) {
            List<FileEntry> fileEntries =
                    this.storageService.getFileEntries(Bank.class.getName(), bankIds, Resource.ICON);

            beneficiaryDTOS.forEach(beneficiaryDTO -> {
                Optional<FileEntry> fileEntriesOfBank = fileEntries.stream()
                        .filter(fileEntry -> Validator.equals(fileEntry.getClassPk(),
                                beneficiaryDTO.getBeneficiaryBankId()))
                        .findFirst();

                fileEntriesOfBank.ifPresent(
                        fileEntry -> beneficiaryDTO.setIcon(this.storageService.getFileInfo(fileEntry)));
            });
        }

        return new PageImpl<>(beneficiaryDTOS, pageable, this.beneficiaryRepository.count(params));
    }

    @Override
    public Page<BeneficiaryRecentlyDTO> searchRecentlyV2(Pageable pageable) {
        Long customerId = GwSecurityUtils.getCustomerLoginId()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));

        BeneficiarySearch beneficiarySearch = new BeneficiarySearch();
        beneficiarySearch.setCustomerId(customerId);
        beneficiarySearch.setStatus(EntityStatus.ACTIVE.getStatus());
        beneficiarySearch.setTransactionStatus(TransactionStatus.SUCCESS);
        beneficiarySearch.setTransferType(TransferType.TRANSFER_MONEY);
        beneficiarySearch.setType(TransferTransactionType.QR_CODE_INTERNAL_MERCHANT);

        Long count = this.transactionRepository.countHistory(beneficiarySearch, pageable);
        if (count == 0) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        List<BeneficiaryRecentlyDTO> beneficiaryRecentlyDTOs = this.transactionRepository.searchHistory(beneficiarySearch, pageable);

        List<Long> bankIds = beneficiaryRecentlyDTOs.stream().map(BeneficiaryRecentlyDTO::getBeneficiaryBankId).collect(Collectors.toList());

        if (Validator.isNotNull(bankIds)) {
            List<FileEntry> fileEntries =
                    this.storageService.getFileEntries(Bank.class.getName(), bankIds, Resource.ICON);

            beneficiaryRecentlyDTOs.forEach(beneficiaryRecentlyDTO -> {
                Optional<FileEntry> fileEntriesOfBank = fileEntries.stream()
                        .filter(fileEntry -> Validator.equals(fileEntry.getClassPk(),
                                beneficiaryRecentlyDTO.getBeneficiaryBankId()))
                        .findFirst();

                fileEntriesOfBank.ifPresent(
                        fileEntry -> beneficiaryRecentlyDTO.setIcon(this.storageService.getFileInfo(fileEntry)));
            });
        }

        return new PageImpl<>(beneficiaryRecentlyDTOs, pageable, count);
    }

    @Override
    public void delete(BeneficiaryRequest request) {
        Beneficiary beneficiary = this.beneficiaryRepository.findById(request.getBeneficiaryId()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1036));

        BeneficiaryDTO beneficiaryDTO = this.beneficiaryMapper.toDto(beneficiary);

        beneficiaryDTO.delete();

        this.beneficiaryRepository.save(this.beneficiaryMapper.toEntity(beneficiaryDTO));
    }

    @Override
    public void update(BeneficiaryRequest request) {
        Beneficiary beneficiary = this.beneficiaryRepository.findById(request.getBeneficiaryId()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1036));

        BeneficiaryDTO beneficiaryDTO = this.beneficiaryMapper.toDto(beneficiary);

        beneficiaryDTO.update(request);

        this.beneficiaryRepository.save(this.beneficiaryMapper.toEntity(beneficiaryDTO));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Beneficiary createBeneficiary(TransactionDTO request) {
        BeneficiaryDTO beneficiaryDTO = new BeneficiaryDTO(request);

        Beneficiary beneficiary = this.beneficiaryRepository
                .findByBeneficiaryAccountNumberAndStatusAndCustomerIdAndBeneficiaryBankCode(
                        beneficiaryDTO.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus(),
                        beneficiaryDTO.getCustomerId(), beneficiaryDTO.getBeneficiaryBankCode());

        if (Validator.isNull(beneficiary)) {
            Optional<Bank> bankOptional = this.bankRepository.findByBankCodeAndStatusAndNation(
                    request.getBankCode(), EntityStatus.ACTIVE.getStatus(), NationCode.LA.name());

            if (bankOptional.isPresent()) {
                Bank bank = bankOptional.get();
                beneficiaryDTO.setBeneficiaryBankName(bank.getBankName());
                beneficiaryDTO.setBeneficiaryBankId(bank.getBankId());
            }

            beneficiary = this.beneficiaryMapper.toEntity(beneficiaryDTO);
        } else {
            beneficiary.setType(beneficiaryDTO.getType());
            beneficiary.setLastModifiedDate(Instant.now());
        }

        if (Validator.isNotNull(beneficiary.getBeneficiaryBankName()) && Validator.isNotNull(beneficiary.getBeneficiaryBankId())) {
            this.beneficiaryRepository.save(beneficiary);
        }

        return beneficiary;
    }
}
