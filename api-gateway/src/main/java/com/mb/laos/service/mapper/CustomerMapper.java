/**
 * 
 */
package com.mb.laos.service.mapper;

import java.time.LocalDate;
import java.util.List;

import com.mb.laos.model.dto.CustomerDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.enums.Gender;
import com.mb.laos.model.Customer;
import com.mb.laos.model.IdCardType;
import com.mb.laos.model.dto.CustomerInformationDTO;
import com.mb.laos.model.dto.CustomerInformationDTO.PersonalID;
import com.mb.laos.repository.IdCardTypeRepository;
import com.mb.laos.util.Validator;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class CustomerMapper implements EntityMapper<CustomerInformationDTO, Customer> {

    @Autowired
    private IdCardTypeRepository idCardTypeRepository;

	@Mapping(source = "customerId", target = "cif")
	@Mapping(source = "phoneNo", target = "phoneNumber")
	@Mapping(source = "phoneNo", target = "username")
	@Mapping(source = "englishName", target = "fullname")
	@Mapping(source = "sector", target = "customerSectorId")
	@Mapping(source = "personalIDs", target = "idCardNumber", qualifiedByName = "mapToIdCode")
	@Mapping(source = "personalIDs", target = "issuePlace", qualifiedByName = "mapToIssuePlace")
	@Mapping(source = "personalIDs", target = "issueDate", qualifiedByName = "mapToIdIssueDate")
	@Mapping(target = "customerId", ignore = true)
    public abstract Customer toEntity(CustomerInformationDTO dto);

    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "lastModifiedDate", ignore = true)
    public abstract Customer toEntity(CustomerDTO customerDTO);
    
    @AfterMapping
    protected void addIdCardType(@MappingTarget CustomerInformationDTO dto, Customer entity) {
        CustomerInformationDTO.PersonalID personalId = null;

        if (Validator.isNull(dto.getPersonalIDs())) {
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }

        personalId = dto.getPersonalIDs().get(0);

        if (Validator.isNull(personalId) || Validator.isNull(personalId.getIdType())
                        || Validator.isNull(dto.getGender())) {
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }

        IdCardType idCardType = this.idCardTypeRepository.findByCode(personalId.getIdType());

        if (Validator.isNull(idCardType)) {
            _log.error("Cannot find Id Card Type with code {}", personalId.getIdType());
            
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
        
        entity.setIdCardTypeId(idCardType.getIdCardTypeId());
    }
    
    @AfterMapping
    protected void addGender(@MappingTarget CustomerInformationDTO dto, Customer entity) {
        Gender gender = Gender.valueOf(dto.getGender());

        if (Validator.isNull(gender)) {
            _log.error("Cannot find gender with code {}", dto.getGender());

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
        
        entity.setGender(gender.getValue());
    }
    
    @Named("mapToIdCode")
    protected String mapToIdCode(List<PersonalID> personalIDs) {
        if (Validator.isNull(personalIDs)) {
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
        
        return personalIDs.get(0).getIdCode();
    }
    
    @Named("mapToIssuePlace")
    protected String mapToIssuePlace(List<PersonalID> personalIDs) {
        if (Validator.isNull(personalIDs)) {
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
        
        return personalIDs.get(0).getIssuePlace();
    }
    
    @Named("mapToIdIssueDate")
    protected LocalDate mapToIdIssueDate(List<PersonalID> personalIDs) {
        if (Validator.isNull(personalIDs)) {
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
        
        return personalIDs.get(0).getIdIssueDate();
    }
}
