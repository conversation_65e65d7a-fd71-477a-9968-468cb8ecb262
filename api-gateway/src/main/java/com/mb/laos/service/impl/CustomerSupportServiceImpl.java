package com.mb.laos.service.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.CustomerSupport;
import com.mb.laos.model.CustomerSupportPhoneNumber;
import com.mb.laos.model.dto.CustomerSupportDTO;
import com.mb.laos.repository.CustomerSupportPhoneNumberRepository;
import com.mb.laos.repository.CustomerSupportRepository;
import com.mb.laos.service.CustomerSupportService;
import com.mb.laos.service.mapper.CustomerSupportPhoneNumberMapper;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
@RequiredArgsConstructor
public class CustomerSupportServiceImpl implements CustomerSupportService {
    private final CustomerSupportPhoneNumberMapper customerSupportPhoneNumberMapper;
    private final CustomerSupportRepository customerSupportRepository;
    private final CustomerSupportPhoneNumberRepository customerSupportPhoneNumberRepository;

    @Override
    public CustomerSupportDTO requestCustomerSupport() {
        CustomerSupportDTO response = new CustomerSupportDTO();
        CustomerSupport customerSupport = this.customerSupportRepository.findByStatus(EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(customerSupport)) {
            List<CustomerSupportPhoneNumber> customerSupportPhoneNumbers = this.customerSupportPhoneNumberRepository
                    .findAllByCustomerSupportIdAndStatus(customerSupport.getCustomerSupportId(), EntityStatus.ACTIVE.getStatus());

            response.setAddress(customerSupport.getAddress());
            response.setEmail(customerSupport.getEmail());
            response.setCustomerSupportPhoneNumbers(Validator.isNotNull(customerSupportPhoneNumbers) ? this.customerSupportPhoneNumberMapper.toDto(customerSupportPhoneNumbers) : null);
        }

        return response;
    }
}
