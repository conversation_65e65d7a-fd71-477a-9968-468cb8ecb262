package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.AccNumberVerifyRequest;
import com.mb.laos.api.request.AccountBalanceRequest;
import com.mb.laos.api.request.AccountMoney;
import com.mb.laos.api.request.AddInfoList;
import com.mb.laos.api.request.Amount;
import com.mb.laos.api.request.ApiUnitelRequest;
import com.mb.laos.api.request.AuthenMethod;
import com.mb.laos.api.request.CashoutTransferRequest;
import com.mb.laos.api.request.ChargeInfo;
import com.mb.laos.api.request.CheckTransactionMmoneyRequest;
import com.mb.laos.api.request.CreditBank;
import com.mb.laos.api.request.DebitMoneyTransferRequest;
import com.mb.laos.api.request.DepositMoneyTransferRequest;
import com.mb.laos.api.request.ETLPaymentTopupWSRequest;
import com.mb.laos.api.request.InquiryTransactionStatusRequest;
import com.mb.laos.api.request.MobilePaymentRequest;
import com.mb.laos.api.request.OtpTransferRequest;
import com.mb.laos.api.request.OtpVerifyTransferRequest;
import com.mb.laos.api.request.PaymentInternetLtcRequest;
import com.mb.laos.api.request.PaymentPostpaidLtcRequest;
import com.mb.laos.api.request.PaymentPstnLtcRequest;
import com.mb.laos.api.request.RevertTransactionRequest;
import com.mb.laos.api.request.TransactionHistoryMBRequest;
import com.mb.laos.api.request.TransferAccountRequest;
import com.mb.laos.api.request.ValidateMobileNumberRequest;
import com.mb.laos.api.request.VerifyInternetLtcMmoneyRequest;
import com.mb.laos.api.request.VerifyPhoneNumberRequest;
import com.mb.laos.api.request.VerifyPostPaidLtcMmoneyRequest;
import com.mb.laos.api.request.VerifyPstnMmoneyPstnRequest;
import com.mb.laos.api.response.BestTelecomResponse;
import com.mb.laos.api.response.CheckBalanceLtcResponse;
import com.mb.laos.api.response.CheckTransactionMmoneyResponse;
import com.mb.laos.api.response.PaymentInternetLtcResponse;
import com.mb.laos.api.response.PaymentPostPaidLtcResponse;
import com.mb.laos.api.response.PaymentPstnLtcResponse;
import com.mb.laos.api.response.QueryDebtUnitelResponse;
import com.mb.laos.api.response.QueryEtlResponse;
import com.mb.laos.api.response.TopupEtlResponse;
import com.mb.laos.api.response.TopupUnitelResponse;
import com.mb.laos.api.response.TransferAccountResponse;
import com.mb.laos.api.response.VerifyInternetLtcResponse;
import com.mb.laos.api.response.VerifyPostPaidLtcResponse;
import com.mb.laos.api.response.VerifyPstnLtcResponse;
import com.mb.laos.api.response.VerifyUmoneyAccountResponse;
import com.mb.laos.api.util.ApiConstants;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConstantProperties;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.InternationalPaymentProperties;
import com.mb.laos.configuration.MBApiConstantProperties;
import com.mb.laos.configuration.OtpProperties;
import com.mb.laos.configuration.QrProperties;
import com.mb.laos.configuration.TransferTransProperties;
import com.mb.laos.enums.AccountType;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.Channel;
import com.mb.laos.enums.CommonCategory;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.FromAccountType;
import com.mb.laos.enums.InternationalBankCode;
import com.mb.laos.enums.InternationalQuery;
import com.mb.laos.enums.MasterMerchantType;
import com.mb.laos.enums.MerchantCode;
import com.mb.laos.enums.MmoneyServiceName;
import com.mb.laos.enums.NationCode;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.OtpType;
import com.mb.laos.enums.SaveStatus;
import com.mb.laos.enums.Sector;
import com.mb.laos.enums.T24TransferType;
import com.mb.laos.enums.TargetType;
import com.mb.laos.enums.TelcoType;
import com.mb.laos.enums.TemplateCode;
import com.mb.laos.enums.TemplateField;
import com.mb.laos.enums.TransactionFeeTypeCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.enums.UmoneyConfigurationType;
import com.mb.laos.gateway.request.BeneficiaryConfirmRequest;
import com.mb.laos.gateway.request.BillingHistoryRequest;
import com.mb.laos.gateway.request.BillingRequest;
import com.mb.laos.gateway.request.BillingV2Request;
import com.mb.laos.gateway.request.CashoutRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request;
import com.mb.laos.gateway.request.DebitMoneyRequest;
import com.mb.laos.gateway.request.DepositMoneyRequest;
import com.mb.laos.gateway.request.FeeTransactionRequest;
import com.mb.laos.gateway.request.InfoQrMerchantRequest;
import com.mb.laos.gateway.request.OtpBillingRequest;
import com.mb.laos.gateway.request.OtpBillingV2Request;
import com.mb.laos.gateway.request.OtpCashInRequest;
import com.mb.laos.gateway.request.OtpQrBillRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.request.OtpTransferInsuranceRequest;
import com.mb.laos.gateway.request.OtpTransferTransRequest;
import com.mb.laos.gateway.request.ResendOtpRequest;
import com.mb.laos.gateway.request.TransactionHistoryRequest;
import com.mb.laos.gateway.response.BeneficiaryInfoResponse;
import com.mb.laos.gateway.response.BillingHistoryResponse;
import com.mb.laos.gateway.response.BillingHistoryV2Response;
import com.mb.laos.gateway.response.BillingResponse;
import com.mb.laos.gateway.response.CashInHistoryResponse;
import com.mb.laos.gateway.response.CashoutResponse;
import com.mb.laos.gateway.response.DebitMoneyResponse;
import com.mb.laos.gateway.response.DepositMoneyResponse;
import com.mb.laos.gateway.response.FeeTransactionResponse;
import com.mb.laos.gateway.response.InfoQrMerchantResponse;
import com.mb.laos.gateway.response.MBOtpTransConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Bank;
import com.mb.laos.model.Beneficiary;
import com.mb.laos.model.CashoutAccount;
import com.mb.laos.model.Common;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.Currency;
import com.mb.laos.model.Customer;
import com.mb.laos.model.CustomerEkyc;
import com.mb.laos.model.DOTP;
import com.mb.laos.model.DebitAccount;
import com.mb.laos.model.InternationalPayment;
import com.mb.laos.model.IntlPaymentCacheDTO;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.NotiTransaction;
import com.mb.laos.model.Notification;
import com.mb.laos.model.Telco;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionClient;
import com.mb.laos.model.TransactionMerchant;
import com.mb.laos.model.dto.AccNumberVerifyDTO;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.InquiryTransactionStatusDTO;
import com.mb.laos.model.dto.InternationalPaymentDTO;
import com.mb.laos.model.dto.MoneyAccountDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.ReqFundTransferDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.TransactionHistoryDTO;
import com.mb.laos.model.dto.VerifyFundTransferDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.model.search.CashInHistorySearch;
import com.mb.laos.model.search.TransactionSearchRequest;
import com.mb.laos.repository.BankRepository;
import com.mb.laos.repository.BeneficiaryRepository;
import com.mb.laos.repository.CashoutAccountRepository;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.CustomerEkycRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.DebitAccountRepository;
import com.mb.laos.repository.DotpRepository;
import com.mb.laos.repository.InternationalPaymentRepository;
import com.mb.laos.repository.IntlPaymentCacheRepository;
import com.mb.laos.repository.MerchantRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.TelcoRepository;
import com.mb.laos.repository.TransactionClientRepository;
import com.mb.laos.repository.TransactionMerchantRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.request.QrVerifyRequest;
import com.mb.laos.response.QrCodeResponse;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.security.util.SecurityConstants;
import com.mb.laos.service.ApiBestTelecomService;
import com.mb.laos.service.ApiETLService;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ApiLtcService;
import com.mb.laos.service.ApiMmoneyService;
import com.mb.laos.service.ApiUnitelService;
import com.mb.laos.service.BeneficiaryService;
import com.mb.laos.service.ConsumerOtpService;
import com.mb.laos.service.DotpService;
import com.mb.laos.service.EwalletService;
import com.mb.laos.service.MmoneyEncryptService;
import com.mb.laos.service.QrCodeService;
import com.mb.laos.service.SendService;
import com.mb.laos.service.TransactionService;
import com.mb.laos.service.TransferService;
import com.mb.laos.service.mapper.InternationalPaymentMapper;
import com.mb.laos.service.mapper.MoneyAccountMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.service.mapper.TransactionMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.GetterUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.MD5Generator;
import com.mb.laos.util.NumberUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.RandomGenerator;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class TransferServiceImpl implements TransferService {
    private final CustomerRepository customerRepository;

    private final TransactionMapper transactionMapper;

    private final TransactionRepository transactionRepository;

    private final MoneyAccountRepository moneyAccountRepository;

    private final MoneyAccountMapper moneyAccountMapper;

    private final ApiGeeTransferService apiGeeTransferService;

    private final MBApiConstantProperties mbApiConstantProperties;

    private final OtpProperties otpProperties;

    private final CustomerProperties customerProperties;

    private final BankRepository bankRepository;

    private final MerchantRepository merchantRepository;

    private final TransactionService transactionService;

    private final CustomerEkycRepository customerEkycRepository;

    private final ContentTemplateRepository contentTemplateRepository;

    private final NotificationRepository notificationRepository;

    private final NotiTransactionRepository notiTransactionRepository;

    private final NotificationMapper notificationMapper;

    private final TransactionMerchantRepository transactionMerchantRepository;

    private final ApiLtcService apiLtcService;

    private final ConsumerProperties consumerProperties;

    private final ConsumerOtpService consumerOtpService;

    private final EwalletService ewalletService;

    private final BeneficiaryService beneficiaryService;

    private final DotpService dotpService;

    private final QrProperties qrProperties;

    private final TransferTransProperties transferTransProperties;

    private final QrCodeService qrCodeService;

    private final TelcoRepository telcoRepository;

    private final ApiUnitelService apiUnitelService;

    private final ApiETLService apiETLService;

    private final ApiBestTelecomService apiBestTelecomService;

    private final ConstantProperties constantProperties;

    private final TransactionClientRepository transactionClientRepository;

    private final CashoutAccountRepository cashoutAccountRepository;

    private final DebitAccountRepository debitAccountRepository;

    private final ApiMmoneyService apiMmoneyService;

    private final MmoneyEncryptService mmoneyEncryptService;

    private final SendService sendService;

    private final CommonRepository commonRepository;

    private final DotpRepository dotpRepository;

    private final FeeServiceImpl feeService;

    private final InternationalPaymentRepository internationalPaymentRepository;

    private final InternationalPaymentMapper internationalPaymentMapper;

    private final IntlPaymentCacheRepository intlPaymentCacheRepository;

    private final BeneficiaryRepository beneficiaryRepository;

    @Override
    public BeneficiaryInfoResponse confirmBeneficiary(BeneficiaryConfirmRequest request) {
        List<Bank> bank = this.bankRepository.findByBankCodeAndStatus(request.getBankCode(), EntityStatus.ACTIVE.getStatus());
        if (bank.isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG100162);
        }
        // BankCode == MB ? call truy vấn nội bộ : call truy vấn lapnet
        if (Validator.equals(this.mbApiConstantProperties.getInBanKTransfer().getChannel(), request.getBankCode())) {
            return this.internalBankMB(request.getBeneficiaryAccountNumber());
        } else {
            return this.interbank(request);
        }
    }

    @Override
    public MBOtpTransConfirmResponse confirmFundTransferMB(TransactionDTO transaction, String otp, String deviceId) {
        // get information customer
        Customer customer = getCustomerLogin();

        // debit account
        AccountMoney debitAccount = new AccountMoney();

        debitAccount.setAccountNumber(transaction.getCustomerAccNumber());
        debitAccount.setAccountName(customer.getFullname());
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        debitAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // credit account
        AccountMoney creditAccount = new AccountMoney();

        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.getType())) {
            creditAccount.setAccountType(this.mbApiConstantProperties.getOtpGen().getQrType());
            creditAccount.setAccountNumber(transaction.getQrCodeValue());
        } else {
            creditAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
            creditAccount.setAccountNumber(transaction.getBeneficiaryAccountNumber());
        }
        creditAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // amount
        Amount amount = new Amount();

        double amountTransaction = this.transactionService.getAmountTransaction(transaction);

        amount.setAmount(Validator.isNotNull(amountTransaction) ? StringUtil.formatNumberDouble((long) amountTransaction) : String.valueOf(0L));
        amount.setCurrency(this.mbApiConstantProperties.getInBanKTransfer().getCurrency());

        // create request send to t24 system
        String userId = customer.getCif() + customer.getIdCardNumber();
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(otp)
                .type(transaction.getOtpType().name() != null ? transaction.getOtpType() : OtpConfirmType.SMS)
                .userId(userId)
                .transData(MD5Generator.md5(transaction.getTransData()))
                .deviceId(deviceId)
                .build();
        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            authenMethod.setToken(dotpService.getCustomerDevice(deviceId).getToken());
        }
        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp)
                .transferType(transaction.getT24TransferType())
                .channel(transaction.getT24Channel())
                .serviceType(transaction.getT24ServiceType())
                .requestId(transaction.getTransactionId())
                .remark(transaction.getMessage())
                .branchCode(transaction.getBranchCode())
                .creditAccount(creditAccount)
                .debitAccount(debitAccount)
                .amount(amount)
                .authenMethod(authenMethod)
                .build();

        this.enrichVerifyTransferRequest(otpVerifyTransferRequest, transaction);

        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                this.apiGeeTransferService::verifyFundTransfer,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber())
                .referenceNumber(reqFundTransferDTO.getReferenceNumber())
                .transactionStatus(reqFundTransferDTO.getTransactionStatus())
                .transactionId(reqFundTransferDTO.getTransactionId())
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode())
                .build();
    }

    @Override
    public OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingRequest request, HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        // kiểm tra trạng thái bill
        BillingRequest billingRequest = new BillingRequest(transaction.getTarget());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // kiểm tra lại số tiền bill và số tiền tạo giao dịch có trùng khớp?
        if (!Validator.equals(Long.valueOf(transaction.getTransactionAmount().longValue()), billing.getBalance())) {
            throw new BadRequestAlertException(ErrorCode.MSG1164);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getBillingPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getBillingPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getBillingPayment().getServiceType());

        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    @Override
    public OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingV2Request request,
                                                        HttpServletRequest httpServletRequest) {

        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }
        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }
        TelcoType telco = this.getTelcoByTransaction(transaction);

        // gọi lại api check số tiền cần thanh toán
        BillingV2Request billingRequest = new BillingV2Request(transaction.getTarget(), telco.name(),
                transaction.getCustomerAccNumber(), BillingType.valueOf(transaction.getBillingType()).getType());
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // kiểm tra lại số tiền bill và số tiền tạo giao dịch có trùng khớp?
        if (!Validator.equals(Long.valueOf(transaction.getTransactionAmount().longValue()), billing.getBalance())) {
            throw new BadRequestAlertException(ErrorCode.MSG1164);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getBillingPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getBillingPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getBillingPayment().getServiceType());

        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    @Override
    public OtpTransferConfirmResponse confirmOtpCashIn(OtpTransferConfirmRequest request,
                                                       HttpServletRequest httpServletRequest) {

        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getCashInTransferApi().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getCashInTransferApi().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getCashInTransferApi().getServiceType());

        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        // Gửi thông báo
        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    @Override
    public OtpTransferConfirmResponse confirmOtpQrPayment(OtpTransferConfirmRequest request,
                                                          HttpServletRequest httpServletRequest) {

        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getQrPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getQrPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getQrPayment().getServiceType());

        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

//        this.transactionService.createNotification(transactionDTO);

        return response;
    }


    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public OtpTransferConfirmResponse confirmOtpTransfer(OtpTransferConfirmRequest request,
                                                         HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        Optional<InternationalPayment> interPayment = this.internationalPaymentRepository
                .findByTransferTransactionId(transaction.getTransferTransactionId());

        verifyTransaction(transaction);

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // phân loại giao dịch tiền
        this.setT24Channel(transaction, transactionDTO);

        // tài khoản nhận
        transactionDTO.setBeneficiaryAccountNumber(transactionDTO.getTarget());
        MBOtpTransConfirmResponse response;
        // confirm OTP
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(transactionDTO.getBankCode())
                && !Validator.equals(transactionDTO.getTransactionCurrency(), customerProperties.getDefaultCurrency())) {
            // chuyển khoản nội bộ ngoại tệ
            response = this.confirmFundTransferMBCurrencyInternal(transactionDTO, request.getOtp(), request.getDeviceId());
        } else {

            response = Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT) && interPayment.isPresent() ?
                    this.confirmFundTransferInterPayment(transactionDTO, request.getOtp(), request.getDeviceId(), interPayment.get())
                    : this.confirmFundTransferMB(transactionDTO, request.getOtp(), request.getDeviceId());
        }

        String transactionCode = response.getT24ReferenceNumber();

        if (Validator.equals(TransactionStatus.TIMEOUT, response.getTransactionStatus())) {
            _log.info("Transaction has been timeout, {}", transactionDTO.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {
                throw new BadRequestAlertException(ErrorCode.MSG1023);
            } else {
                transactionCode = inquiryTransactionStatusDTO.getT24ReferenceNumber();
            }
        }

        if (Validator.equals(TransactionStatus.FAILED, response.getTransactionStatus())) {
            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            Transaction trans = this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));
            this.updateInterPayment(interPayment, this.transactionMapper.toDto(trans));

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        }

        if (Validator.equals(TransactionStatus.SUCCESS, response.getTransactionStatus())) {
            IntlPaymentCacheDTO cache = this.intlPaymentCacheRepository.get(transactionDTO.getTransactionId());

            if (Validator.isNotNull(cache)) {
                this.intlPaymentCacheRepository.evict(cache.getTransactionId());
                this.intlPaymentCacheRepository.evict(transactionDTO.getTransactionId());
            }

            Beneficiary beneficiary = this.beneficiaryRepository
                    .findByBeneficiaryAccountNumberAndStatusAndCustomerIdAndBeneficiaryBankCode(
                            transaction.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus(),
                            transaction.getCustomerId(), transaction.getBankCode());
            if (Validator.isNotNull(beneficiary)) {
                beneficiary.setTransferTransactionId(transaction.getTransferTransactionId());
                this.beneficiaryRepository.save(beneficiary);
            }
        }

        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transaction.setTransactionFinishTime(Instant.now());
        transaction.setTransactionCode(transactionCode);

        // luu giao dich vao db
        this.transactionRepository.save_(transaction);

        TransactionDTO transDTO = this.transactionMapper.toDto(transaction);

        this.updateInterPayment(interPayment, transDTO);

        // Gửi thông báo
        this.createNotification(transDTO, customer.getCustomerId());

        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now())
                .isSuccess(!StringUtil.isBlank(transactionCode))
                .transactionCode(transactionCode)
                .transactionFee(transaction.getTransactionFee())
                .transactionId(transDTO.getTransactionId())
                .build();
    }

    private void setT24Channel(Transaction transaction, TransactionDTO transactionDTO) {
        if (!Validator.equals(transaction.getBankCode(),
                this.mbApiConstantProperties.getInBanKTransfer().getChannel())) {
            transactionDTO.setT24TransferType(this.mbApiConstantProperties.getLapnetTransfer().getTransferType());
            transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getLapnetTransfer().getServiceType());
            transactionDTO.setT24Channel(this.mbApiConstantProperties.getLapnetTransfer().getChannel());
        } else {
            transactionDTO.setT24TransferType(this.mbApiConstantProperties.getInBanKTransfer().getTransferType());
            // phân loại chuyển khoản bằng qr
            if (Validator.equals(transactionDTO.getType(), TransferTransactionType.QR_CODE_INTERNAL)) {
                transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getInBanKTransfer().getServiceTypeQr());
            } else {
                transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getInBanKTransfer().getServiceType());
            }
            transactionDTO.setT24Channel(this.mbApiConstantProperties.getInBanKTransfer().getChannel());
        }
    }

    private void updateInterPayment(Optional<InternationalPayment> interPayment, TransactionDTO transDTO) {
        if (Validator.equals(transDTO.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            if (!interPayment.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1048);
            }

            InternationalPaymentDTO dto = this.internationalPaymentMapper.toDto(interPayment.get());
            BeanUtils.copyProperties(transDTO, dto, Transaction.FieldName.BENEFICIARY_CURRENCY);
            this.internationalPaymentRepository.save(this.internationalPaymentMapper.toEntity(dto));
        }
    }

    private MBOtpTransConfirmResponse confirmFundTransferInterPayment(TransactionDTO transaction, String otp, String deviceId, InternationalPayment interPayment) {
        // get information customer
        Customer customer = getCustomerLogin();
        AuthenMethod authenMethod = null;

        Common common = getCommon(interPayment.getBeneficiaryCurrency());

        // debit account
        AccountMoney debitAccount = new AccountMoney();

        debitAccount.setAccountNumber(interPayment.getCustomerAccNumber());
        debitAccount.setAccountName(customer.getFullname());
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        debitAccount.setAccountCurrency(interPayment.getTransactionCurrency());

        // credit account
        AccountMoney creditAccount = new AccountMoney();
        creditAccount.setAccountName(interPayment.getBeneficiaryCustomerName());
        creditAccount.setAccountType(AccountType.QR.name());
        creditAccount.setAccountCurrency(CurrencyType.LAK.name());
        creditAccount.setAccountNumber(interPayment.getQrCodeValue());

        // amount
        Amount amount = new Amount();
        amount.setAmount(String.valueOf(interPayment.getTransactionAmount().longValue()));
        amount.setCurrency(this.mbApiConstantProperties.getInBanKTransfer().getCurrency());

        Amount.InternationalPaymentInfo internationalPaymentInfo = Amount.InternationalPaymentInfo.builder()
                .toNation(common.getValue())
                .originalAmount(String.valueOf(interPayment.getForeignAmount().longValue()))
                .exchangeRate(interPayment.getExchangeRate().toString())
                .exchangeCurrency(interPayment.getBeneficiaryCurrency())
                .build();
        amount.setInternationalPaymentInfo(internationalPaymentInfo);

        ChargeInfo chargeInfo = new ChargeInfo();
        chargeInfo.setAmount(String.valueOf((long) transaction.getTransactionFee()));
        chargeInfo.setCurrency(CurrencyType.LAK.name());
        chargeInfo.setAccountNumber(transaction.getCustomerAccNumber());
        ChargeInfo.InternationalPaymentFeeInfo internationalPaymentFeeInfo = ChargeInfo.InternationalPaymentFeeInfo.builder()
                .toNation(common.getValue())
                .originalFeeAmount(String.valueOf(interPayment.getForeignFee().longValue()))
                .exchangeRate(interPayment.getExchangeRate().toString())
                .exchangeCurrency(interPayment.getBeneficiaryCurrency())
                .build();
        chargeInfo.setInternationalPaymentFeeInfo(internationalPaymentFeeInfo);

        CreditBank creditBank = new CreditBank();
        creditBank.setCode(interPayment.getPaymentSystem());
        creditBank.setName(interPayment.getPaymentSystem());

        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            String userId = customer.getCif() + customer.getIdCardNumber();

            Optional<DOTP> dotp = this.dotpRepository
                    .findByDeviceIdAndCustomerIdAndStatus(deviceId, customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!dotp.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG100155);
            }

            authenMethod = AuthenMethod.builder()
                    .otpValue(otp)
                    .type(OtpConfirmType.DOTP)
                    .token(dotp.get().getToken())
                    .userId(userId)
                    .transData(MD5Generator.md5(transaction.getTransData()))
                    .deviceId(deviceId)
                    .build();
        }

        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp)
                .requestId(transaction.getTransactionId())
                .transferType(T24TransferType.INTERNATIONAL_PAYMENT.name())
                .channel(Channel.LAPNET.name())
                .serviceType(this.mbApiConstantProperties.getLapnetTransfer().getServiceType())
                .remark(transaction.getMessage())
                .branchCode(transaction.getBranchCode())
                .debitAccount(debitAccount)
                .creditAccount(creditAccount)
                .amount(amount)
                .creditBank(creditBank)
                .chargeInfo(chargeInfo)
                .authenMethod(authenMethod)
                .build();

        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? this.apiGeeTransferService::verifyFundTransfer :
                        this.apiGeeTransferService::verifyFundTransferOtherCurrency,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber())
                .referenceNumber(reqFundTransferDTO.getReferenceNumber())
                .transactionStatus(reqFundTransferDTO.getTransactionStatus())
                .transactionId(reqFundTransferDTO.getTransactionId())
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode())
                .build();
    }

    public void verifyTransaction(Transaction transaction) {
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        double amount = Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)
                && Validator.equals(transaction.getBankCode(), StringPool.CAMBODIA)
                ? transaction.getForeignAmount() : transaction.getTransactionAmount();

        if (Validator.equals(transaction.getBankCode(), InternationalBankCode.NAPAS.name())
            && Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            amount = transaction.getTransactionAmount();
            this.transactionService.checkTransactionLimitLaoVN(String.valueOf(transaction.getTransferType()), transaction.getCustomerId(),
                   transaction.getCustomerAccNumber(), amount, transaction.getType(), transaction);
        } else {
            this.transactionService.checkTransactionLimit(String.valueOf(transaction.getTransferType()), transaction.getCustomerId(),
                    transaction.getCustomerAccNumber(), amount, transaction.getType(), transaction);
        }
    }

    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public CashoutResponse cashout(HttpServletRequest request, CashoutRequest cashoutRequest) {
        // lấy request call verify T24 để check tên tài khoản khớp với dữ liệu truyền vào hay không
        BeneficiaryConfirmRequest beneficiaryConfirmRequest = new BeneficiaryConfirmRequest();
        beneficiaryConfirmRequest.setBeneficiaryAccountNumber(cashoutRequest.getCreditAccount().getAccountNumber());
        if (Validator.equals(cashoutRequest.getCreditAccount().getAccountType(), AccountType.QR.name())) {
            beneficiaryConfirmRequest.setType(AccountType.QR);
        } else {
            beneficiaryConfirmRequest.setType(AccountType.ACCOUNT);
        }
        beneficiaryConfirmRequest.setBankCode(cashoutRequest.getCreditBank());
        BeneficiaryInfoResponse beneficiaryInfoResponse = this.clientConfirmBeneficiary(beneficiaryConfirmRequest);
        if (!Validator.equals(StringUtil.trim(beneficiaryInfoResponse.getBeneficiaryName()), StringUtil.trim(cashoutRequest.getCreditAccount().getAccountName()))) {
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        }

        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        CashoutTransferRequest cashoutTransferRequest = new CashoutTransferRequest();
        cashoutTransferRequest.setServiceType(this.mbApiConstantProperties.getCashout().getServiceType());
        cashoutTransferRequest.setTransferType(this.mbApiConstantProperties.getCashout().getTransferType());
        AccountMoney debitAccount = new AccountMoney();
        TransactionClient transactionClient = new TransactionClient();

        CashoutAccount cashoutAccount = null;

        List<CashoutAccount> cashoutAccounts = null;

        if (clientName.equals("umoney")) {
            cashoutAccounts = this.cashoutAccountRepository.findAllByStatusIsNotOrderByIdDesc(EntityStatus.DELETED.getStatus());

            cashoutAccount = this.getCashoutAccountManualOrAuto(cashoutRequest, cashoutAccounts);
            debitAccount.setAccountNumber(cashoutAccount.getAccountNumber());
            debitAccount.setAccountName(cashoutAccount.getAccountName());
            debitAccount.setAccountType(cashoutAccount.getAccountType());
            debitAccount.setAccountCurrency(cashoutAccount.getAccountCurrency());

            cashoutAccounts.remove(cashoutAccount);
        }
        if (this.mbApiConstantProperties.getCashout().getMbCodes().contains(cashoutRequest.getCreditBank())) {
            if (cashoutRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
                transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
            } else {
                transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
                transactionClient.setQrCodeValue(cashoutRequest.getCreditAccount().getAccountNumber());
            }
            if (clientName.equals("umoney")) {
                cashoutTransferRequest.setChannel("UMONEY");
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG1005);
            }
        } else {
            cashoutTransferRequest.setChannel(this.mbApiConstantProperties.getCashout().getChannelLapnet());
            if (cashoutRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
                transactionClient.setType(TransferTransactionType.INTER_BANK);
            } else {
                transactionClient.setType(TransferTransactionType.QR_CODE_LAPNET_TRANSFER);
                transactionClient.setQrCodeValue(cashoutRequest.getCreditAccount().getAccountNumber());
            }
        }
        cashoutTransferRequest.setAmount(cashoutRequest.getAmount());
        cashoutTransferRequest.setDebitAccount(debitAccount);
        cashoutTransferRequest.setCreditAccount(cashoutRequest.getCreditAccount());
        cashoutTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode());
        cashoutTransferRequest.setCreditBank(new CreditBank(cashoutRequest.getCreditBank()));
        cashoutTransferRequest.setChargeInfo(cashoutRequest.getChargeInfo());
        cashoutTransferRequest.setRemark(cashoutRequest.getRemark());

        transactionClient.setTransferType(TransferType.CASH_OUT);
        transactionClient.setClientName(clientName);
        transactionClient.setClientAccountName(debitAccount.getAccountName());
        transactionClient.setClientAccountNumber(debitAccount.getAccountNumber());
        transactionClient.setTarget(cashoutTransferRequest.getCreditAccount().getAccountNumber());
        transactionClient.setTransactionStartTime(Instant.now());
        transactionClient.setTransactionFinishTime(Instant.now());
        transactionClient.setTransactionFee(cashoutTransferRequest.getChargeInfo() != null ? Double.parseDouble(cashoutTransferRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(cashoutTransferRequest.getAmount().getAmount()));
        transactionClient.setTransactionCurrency(cashoutTransferRequest.getAmount().getCurrency());
        transactionClient.setBranchCode(cashoutTransferRequest.getBranchCode());
        transactionClient.setBankCode(cashoutTransferRequest.getCreditBank().getCode());
        transactionClient.setBeneficiaryAccountNumber(cashoutTransferRequest.getCreditAccount().getAccountNumber());
        transactionClient.setBeneficiaryCustomerName(cashoutTransferRequest.getCreditAccount().getAccountName());
        transactionClient.setMessage(cashoutTransferRequest.getRemark());
        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID));
        if (Validator.isNotNull(beneficiaryInfoResponse) && Validator.isNotNull(beneficiaryInfoResponse.getReferenceNumber())) {
            cashoutTransferRequest.setApiGeeTransactionId(beneficiaryInfoResponse.getReferenceNumber());
        } else {
            cashoutTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID));
        }
        VerifyFundTransferDTO response = this.apiGeeTransferService.cashout(cashoutTransferRequest);
        transactionClient.setTransactionStatus(response.getTransactionStatus());
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }
        transactionClient.setTransactionId(response.getReferenceNumber());
        transactionClient.setTransactionCode(response.getT24ReferenceNumber());
        transactionClientRepository.save(transactionClient);

        if (Validator.isNotNull(cashoutAccount)) {
            this.cashoutAccountRepository.addTransferedAmount(Long.parseLong(cashoutRequest.getAmount().getAmount()), cashoutAccount.getId());

            if (Validator.equals(cashoutAccount.getConfigurationType(), UmoneyConfigurationType.AUTO.name())) {
                cashoutAccounts.forEach(item -> item.setStatus(EntityStatus.INACTIVE.getStatus()));
                cashoutAccount.setStatus(EntityStatus.ACTIVE.getStatus());
            }
        }

        return CashoutResponse.builder()
                .referenceNumber(response.getReferenceNumber())
                .t24ReferenceNumber(response.getT24ReferenceNumber())
                .transactionStatus(response.getTransactionStatus())
                .transactionId(transactionClient.getTransactionId())
                .build();
    }

    // @TODO MBLAOS23P2-4058: Chuyển tiền Umoney sang Lapnet
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public DebitMoneyResponse debitMoney(HttpServletRequest request, DebitMoneyRequest debitMoneyRequest) {
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        this.validateDebitTransferUmoney(debitMoneyRequest);

        Optional<DebitAccount> debitAccount = null;

        if (clientName.equals("umoney")) {
            debitAccount = this.debitAccountRepository
                    .findByAccountNumberAndStatus(debitMoneyRequest.getDebitAccount().getAccountNumber(), EntityStatus.ACTIVE.getStatus());
        }


        // validate mã giao dịch hoạch toán
        if (Validator.isNull(debitMoneyRequest.getChargeInfo().getDescription())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DESCRIPTION_IS_REQUIRED,
                            new Object[]{}),
                    TransactionClient.class.getSimpleName(), LabelKey.ERROR_DESCRIPTION_IS_REQUIRED);
        }

        if (!debitAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101276);
        } else if (!Validator.equals(debitAccount.get().getAccountName(), debitMoneyRequest.getDebitAccount().getAccountName())) {
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        } else if (Double.parseDouble(debitMoneyRequest.getAmount().getAmount()) > debitAccount.get().getTransactionLimit()) {
            throw new BadRequestAlertException(ErrorCode.MSG1106);
        }

        // Lưu thông tin Client
        TransactionClient transactionClient = new TransactionClient();
        transactionClient.setTransferType(TransferType.DEBIT);
        transactionClient.setBankCode(debitMoneyRequest.getCreditBank().getCode());
        transactionClient.setBankName(debitMoneyRequest.getCreditBank().getName());
        transactionClient.setMessage(debitMoneyRequest.getRemark());
        transactionClient.setClientName(clientName);
        transactionClient.setClientAccountName(debitMoneyRequest.getCreditAccount().getAccountName());
        transactionClient.setClientAccountNumber(debitMoneyRequest.getCreditAccount().getAccountNumber());
        transactionClient.setTarget(debitMoneyRequest.getCreditAccount().getAccountNumber());

        transactionClient.setBeneficiaryAccountNumber(debitMoneyRequest.getDebitAccount().getAccountNumber());
        transactionClient.setBeneficiaryCustomerName(debitMoneyRequest.getDebitAccount().getAccountName());

        transactionClient.setTransactionStartTime(Instant.now());
        transactionClient.setTransactionFinishTime(Instant.now());
        transactionClient.setTransactionFee(debitMoneyRequest.getChargeInfo() != null ? Double.parseDouble(debitMoneyRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(debitMoneyRequest.getAmount().getAmount()));
        transactionClient.setTransactionCurrency(debitMoneyRequest.getAmount().getCurrency());
        transactionClient.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionClient.setDescription(debitMoneyRequest.getChargeInfo().getDescription());

        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID));
        transactionClient.setFromMember(debitMoneyRequest.getFromMember());
        transactionClient.setFromUser(debitMoneyRequest.getFromUser());
        transactionClient.setFromUserFullName(debitMoneyRequest.getFromUserFullName());
        transactionClient.setFromAccount(debitMoneyRequest.getFromAccount());

        if (debitMoneyRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
            transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
            transactionClient.setQrCodeValue(debitMoneyRequest.getCreditAccount().getAccountNumber());
        }

        DebitMoneyTransferRequest debitMoneyTransferRequest = new DebitMoneyTransferRequest();
        debitMoneyTransferRequest.setAmount(debitMoneyRequest.getAmount());
        debitMoneyTransferRequest.setChargeInfo(debitMoneyRequest.getChargeInfo());
        debitMoneyTransferRequest.setCreditBank(debitMoneyRequest.getCreditBank());
        debitMoneyTransferRequest.setCreditAccount(debitMoneyRequest.getCreditAccount());
        debitMoneyTransferRequest.setDebitAccount(debitMoneyRequest.getDebitAccount());
        debitMoneyTransferRequest.setRemark(debitMoneyRequest.getRemark());
        debitMoneyTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode());
        debitMoneyTransferRequest.setServiceType(this.mbApiConstantProperties.getDepositDebit().getServiceTypeDebit());
        debitMoneyTransferRequest.setChannel(this.mbApiConstantProperties.getDepositDebit().getChannel());
        debitMoneyTransferRequest.setTransferType(this.mbApiConstantProperties.getDepositDebit().getTransferType());
        debitMoneyTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID));
        VerifyFundTransferDTO response = this.apiGeeTransferService.debitMoney(debitMoneyTransferRequest);
        transactionClient.setTransactionStatus(response.getTransactionStatus());
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }
        transactionClient.setTransactionId(response.getReferenceNumber());
        transactionClient.setTransactionCode(response.getT24ReferenceNumber());
        transactionClientRepository.save(transactionClient);

        if (debitAccount.isPresent()) {
            this.debitAccountRepository.addTransferedAmount(Long.parseLong(debitMoneyRequest.getAmount().getAmount()), debitAccount.get().getId());
        }

        return DebitMoneyResponse.builder()
                .referenceNumber(response.getReferenceNumber())
                .t24ReferenceNumber(response.getT24ReferenceNumber())
                .transactionStatus(response.getTransactionStatus())
                .transactionId(transactionClient.getTransactionId())
                .build();
    }

    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public DepositMoneyResponse depositMoney(HttpServletRequest request, DepositMoneyRequest depositMoneyRequest) {
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        this.validateDepositTransferUmoney(depositMoneyRequest);

        Optional<DebitAccount> debitAccount = null;
        if (clientName.equals("umoney")) {
            debitAccount = this.debitAccountRepository.findByAccountNumberAndStatus(
                    depositMoneyRequest.getCreditAccount().getAccountNumber(), EntityStatus.ACTIVE.getStatus());
        }

        if (!debitAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101276);
        } else if (!Validator.equals(debitAccount.get().getAccountName(), depositMoneyRequest.getCreditAccount().getAccountName())) {
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        } else if (Double.parseDouble(depositMoneyRequest.getAmount().getAmount()) > debitAccount.get().getTransactionLimit()) {
            throw new BadRequestAlertException(ErrorCode.MSG1106);
        }

        // Lưu thông tin Client
        TransactionClient transactionClient = new TransactionClient();
        transactionClient.setTransferType(TransferType.DEPOSIT);
        transactionClient.setBankCode(depositMoneyRequest.getCreditBank().getCode());
        transactionClient.setBankName(depositMoneyRequest.getCreditBank().getName());
        transactionClient.setMessage(depositMoneyRequest.getRemark());

        transactionClient.setClientName(clientName);
        transactionClient.setClientAccountName(depositMoneyRequest.getDebitAccount().getAccountName());
        transactionClient.setClientAccountNumber(depositMoneyRequest.getDebitAccount().getAccountNumber());
        transactionClient.setTarget(depositMoneyRequest.getDebitAccount().getAccountNumber());

        transactionClient.setBeneficiaryAccountNumber(depositMoneyRequest.getCreditAccount().getAccountNumber());
        transactionClient.setBeneficiaryCustomerName(depositMoneyRequest.getCreditAccount().getAccountName());

        transactionClient.setTransactionStartTime(Instant.now());
        transactionClient.setTransactionFinishTime(Instant.now());
        transactionClient.setTransactionFee(depositMoneyRequest.getChargeInfo() != null ? Double.parseDouble(depositMoneyRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(depositMoneyRequest.getAmount().getAmount()));
        transactionClient.setTransactionCurrency(depositMoneyRequest.getAmount().getCurrency());
        transactionClient.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionClient.setDescription(depositMoneyRequest.getChargeInfo().getDescription());

        transactionClient.setFromMember(depositMoneyRequest.getFromMember());
        transactionClient.setFromUser(depositMoneyRequest.getFromUser());
        transactionClient.setFromUserFullName(depositMoneyRequest.getFromUserFullName());
        transactionClient.setFromAccount(depositMoneyRequest.getFromAccount());
        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID));

        if (depositMoneyRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
            transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
            transactionClient.setQrCodeValue(depositMoneyRequest.getCreditAccount().getAccountNumber());
        }

        DepositMoneyTransferRequest depositMoneyTransferRequest = new DepositMoneyTransferRequest();
        depositMoneyTransferRequest.setAmount(depositMoneyRequest.getAmount());
        depositMoneyTransferRequest.setChargeInfo(depositMoneyRequest.getChargeInfo());
        depositMoneyTransferRequest.setCreditBank(depositMoneyRequest.getCreditBank());
        depositMoneyTransferRequest.setCreditAccount(depositMoneyRequest.getCreditAccount());
        depositMoneyTransferRequest.setDebitAccount(depositMoneyRequest.getDebitAccount());
        depositMoneyTransferRequest.setRemark(depositMoneyRequest.getRemark());
        depositMoneyTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode());
        depositMoneyTransferRequest.setServiceType(this.mbApiConstantProperties.getDepositDebit().getServiceTypeDeposit());
        depositMoneyTransferRequest.setChannel(this.mbApiConstantProperties.getDepositDebit().getChannel());
        depositMoneyTransferRequest.setTransferType(this.mbApiConstantProperties.getDepositDebit().getTransferType());
        depositMoneyTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID));
        VerifyFundTransferDTO response = this.apiGeeTransferService.depositMoney(depositMoneyTransferRequest);
        transactionClient.setTransactionStatus(response.getTransactionStatus());
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }
        transactionClient.setTransactionId(response.getReferenceNumber());
        transactionClient.setTransactionCode(response.getT24ReferenceNumber());
        transactionClientRepository.save(transactionClient);

        if (debitAccount.isPresent()) {
            this.debitAccountRepository.addedAmount(Long.parseLong(depositMoneyRequest.getAmount().getAmount()), debitAccount.get().getId());
        }

        return DepositMoneyResponse.builder()
                .referenceNumber(response.getReferenceNumber())
                .t24ReferenceNumber(response.getT24ReferenceNumber())
                .transactionStatus(response.getTransactionStatus())
                .transactionId(transactionClient.getTransactionId())
                .build();
    }

    private CashoutAccount getCashoutAccountManualOrAuto(CashoutRequest cashoutRequest, List<CashoutAccount> cashoutAccounts) {

        if (Validator.isNull(cashoutRequest.getAmount().getAmount())) {
            throw new BadRequestAlertException(ErrorCode.MSG1138);
        }

        CashoutAccount cashoutAccount = cashoutAccounts.stream().filter(item -> Validator.equals(EntityStatus.ACTIVE.getStatus(), item.getStatus())).findFirst()
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101054));

        if (Validator.equals(UmoneyConfigurationType.AUTO.name(), cashoutAccount.getConfigurationType())) {

            cashoutAccounts = cashoutAccounts.stream()
                    .filter(item -> item.getAmountTransferred() + Long.parseLong(cashoutRequest.getAmount().getAmount()) <= item.getTransactionLimit())
                    .sorted((i1, i2) -> i2.getStatus() - i1.getStatus())
                    .collect(Collectors.toList());

            List<String> cashoutAccountNumbers = cashoutAccounts.stream().map(CashoutAccount::getAccountNumber).collect(Collectors.toList());

            List<String> cifCashoutAccounts = cashoutAccounts.stream().map(CashoutAccount::getCif).filter(Validator::isNotNull).collect(Collectors.toList());

            Set<String> usedCif = new HashSet<>();

            boolean checkAvailableBalance = false;

            for (String cif : cifCashoutAccounts) {
                if (usedCif.contains(cif)) {
                    continue;
                }
                usedCif.add(cif);

                AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder().custCode(cif)
                        .build();

                List<AccountBalanceDTO> accountBalanceDTOList = this.apiGeeTransferService
                        .checkAccountBalance(accountBalanceRequest);

                if (Validator.isNull(accountBalanceDTOList)) {
                    throw new BadRequestAlertException(ErrorCode.MSG100029);
                }

                accountBalanceDTOList = accountBalanceDTOList.stream().filter(item -> cashoutAccountNumbers.contains(item.getAccountNumber())).collect(Collectors.toList());

                for (String accountNumber : cashoutAccountNumbers) {
                    Optional<AccountBalanceDTO> accountBalanceDTOOptional = accountBalanceDTOList.stream().filter(a -> a.getAccountNumber().equals(accountNumber)).findFirst();
                    if (accountBalanceDTOOptional.isPresent()) {
                        AccountBalanceDTO accountBalanceDTO = accountBalanceDTOOptional.get();
                        if (Validator.isNotNull(accountBalanceDTO.getAvailableBalance())
                                && Long.parseLong(accountBalanceDTO.getAvailableBalance()) > 0
                                && Long.parseLong(accountBalanceDTO.getAvailableBalance()) >= Long.parseLong(cashoutRequest.getAmount().getAmount())) {
                            cashoutAccount = cashoutAccounts.stream().filter(item -> Validator.equals(item.getAccountNumber(), accountBalanceDTO.getAccountNumber())).findFirst().get();
                            checkAvailableBalance = true;
                            break;
                        }
                    }
                }

                if (checkAvailableBalance) {
                    break;
                }
            }

            if (!checkAvailableBalance) {
                throw new BadRequestAlertException(ErrorCode.MSG101096);
            }
        } else {
            if (Long.parseLong(cashoutRequest.getAmount().getAmount()) > cashoutAccount.getTransactionLimit()) {
                throw new BadRequestAlertException(ErrorCode.MSG1158);
            }
        }

        return cashoutAccount;
    }

    @Override
    public BeneficiaryInfoResponse clientConfirmBeneficiary(BeneficiaryConfirmRequest request) {
        if (this.mbApiConstantProperties.getCashout().getMbCodes().contains(request.getBankCode())) {
            //truong hop quet QR cua MB thi cung goi sang lapnet de truy van
            //do QR theo chuan lapnet
            if (request.getType().equals(AccountType.QR)) {
                return this.getCustomerInterBankInfo(request);
            }
            return this.internalBankMB(request.getBeneficiaryAccountNumber());
        } else {
            return this.getCustomerInterBankInfo(request);
        }
    }

    @Override
    public List<TransactionHistoryDTO> getHistory(TransactionHistoryRequest request) {
        Customer customer = this.getCustomerLogin();

        // check account number of customer
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository
                .findByAccountNumberAndCustomerIdAndStatus(request.getAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1038));

        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1038);
        }

        // yyyyMMdd
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern(DateUtil.SHORT_TIMESTAMP_PATTERN_NON_DASH);

        if (request.getFromDate().isAfter(LocalDate.now()) || request.getToDate().isAfter(LocalDate.now())) {
            throw new BadRequestAlertException(ErrorCode.MSG1060);
        }

        if (request.getFromDate().isAfter(request.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1058);
        }

        if (ChronoUnit.DAYS.between(request.getFromDate(), request.getToDate()) > this.customerProperties
                .getExceedDate() - 1) {
            throw new BadRequestAlertException(ErrorCode.MSG1096);
        }

        String fromDate = request.getFromDate().format(formatters);
        String toDate = request.getToDate().format(formatters);

        TransactionHistoryMBRequest mbRequest = TransactionHistoryMBRequest.builder()
                .accountNumber(request.getAccountNumber())
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        List<TransactionHistoryDTO> transactionHistoryDTOS =
                this.apiGeeTransferService.getTransactionHistory(mbRequest);

        // format transactionDate
        transactionHistoryDTOS.forEach(t -> {
            String transactionDate = t.getTransactionDate();

            if (Validator.isNotNull(transactionDate) && transactionDate.matches(DateUtil.LONG_DATE_NO_DASH_REGEX)) {
                String td = LocalDateUtil.changeFormat(transactionDate, DateUtil.LONG_DATE_NO_DASH,
                        DateUtil.LONG_DATE_PATTERN);

                if (Validator.isNotNull(td)) {
                    t.setTransactionDate(td);
                }
            }
        });

        // reverse list
        Collections.reverse(transactionHistoryDTOS);

        return transactionHistoryDTOS;
    }

    @Override
    public List<BillingHistoryResponse> getHistoryBilling(BillingHistoryRequest request) {
        Customer customer = this.getCustomerLogin();

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        List<Transaction> transactions = this.transactionRepository.getBillingHistories(customer.getCustomerId(),
                pageable);

        List<BillingHistoryResponse> responses = new ArrayList<>();

        transactions.forEach(transaction -> {
            BillingHistoryResponse billingHistoryResponse = BillingHistoryResponse.builder()
                    .billingCode(transaction.getTarget()).transactionAmount(Validator.isNotNull(transaction.getTransactionAmount()) ? transaction.getTransactionAmount().longValue() : 0L)
                    .transactionCurrency(transaction.getTransactionCurrency()).build();
            if (Validator.isNull(transaction.getTransactionCurrency())) {
                billingHistoryResponse.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
            }
            responses.add(billingHistoryResponse);
        });

        return responses;
    }

    @Override
    public Set<BillingHistoryV2Response> getHistoryBillingV2(BillingHistoryV2Search request) {

        BillingType billingType = BillingType.getBillingType(request.getBillingType());

        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        Customer customer = this.getCustomerLogin();

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        List<Transaction> transactions = this.transactionRepository.getBillingHistoriesV2(request, pageable, customer.getCustomerId());

        Set<BillingHistoryV2Response> responses = new LinkedHashSet<>();

        transactions.forEach(transaction -> {

            TelcoType telcoType = this.getTelcoByTransactionHistory(transaction);

            Optional<Telco> telco = telcoRepository.findByTelcoCodeAndStatus(telcoType.name(), EntityStatus.ACTIVE.getStatus());

            if (telco.isPresent()) {
                BillingHistoryV2Response billingHistoryResponse = BillingHistoryV2Response.builder()
                        .billingCode(transaction.getTarget())
                        .telco(telco.get().getTelcoName())
                        .build();

                responses.add(billingHistoryResponse);
            }
        });

        return responses;
    }

    @Override
    public InfoQrMerchantResponse getInfoQrMerchant(InfoQrMerchantRequest request) {

        // get thông tin QR theo tài khoản merchant
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(request.getMerchantId(),
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // kiem tra merchantId có phải của backend app sinh ra hay không,... nghiệp vụ merchant chưa có
        return InfoQrMerchantResponse.builder()
                .merchantName(merchant.getMerchantName())
                .bankCode(merchant.getMerchantBankCode())
                .accountNumber(merchant.getMerchantAccountNumber())
                .build();
    }

    @Override
    public OtpTransferTransResponse requestOtpBilling(OtpBillingV2Request request,
                                                      HttpServletRequest httpServletRequest) {

        Customer customer = this.getCustomerLogin();

        BillingV2Request billingRequest = new BillingV2Request(request.getBillCode(), request.getTelcoCode(), request.getAccountNumber(), request.getBillingType());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setMessage(StringUtil.join(new String[]{request.getTelcoCode(), request.getBillCode()}, StringPool.SPACE));
        transactionDTO.setConfigurationFeeType(billing.getConfigurationFeeType());
        transactionDTO.setTransactionAmount(Validator.isNotNull(billing.getBalance()) ? billing.getBalance().doubleValue() : 0D);
        transactionDTO.setTransactionFee(billing.getTransactionFee());
        transactionDTO.setDiscount(billing.getTransactionDiscount());
        transactionDTO.setDiscountFixed(billing.getDiscountFixed());

        return this.makeTransferToMerchant(transactionDTO, request.getTelcoCode(), customer, request.isSetDefaultAccount());
    }

    @Override
    public OtpTransferTransResponse requestOtpBilling(OtpBillingRequest request, HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        BillingRequest billingRequest = new BillingRequest(request.getBillCode());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setMessage(StringUtil.join(new String[]{
                billing.getNetworkType(), request.getBillCode()}, StringPool.SPACE));
        transactionDTO.setTransactionAmount(Validator.isNotNull(billing.getBalance()) ? billing.getBalance().doubleValue() : 0L);

        if (BillingType.getTransactionFeeCodeByType(request.getType()) != null) {
            FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.valueOf(BillingType.getTransactionFeeCodeByType(request.getType())),
                    Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, this.customerProperties.getDefaultCurrency());

            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }
        return this.makeTransferToMerchant(transactionDTO, billing.getNetworkType(), customer, request.isSetDefaultAccount());
    }

    @Override
    public OtpTransferTransResponse requestOtpCashIn(OtpCashInRequest request,
                                                     HttpServletRequest httpServletRequest) {

        // verify umoney account
        String umoneyAccountName = null;
        VerifyUmoneyAccountResponse umoneyAccountResponse = this.ewalletService.verifyCashAccount(request.getEwalletAccountNumber());

        if (Validator.isNotNull(umoneyAccountResponse)) {
            umoneyAccountName = umoneyAccountResponse.getAccountName();
        }

        // validate số tiền giao dịch
        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request, umoneyAccountName);

        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                TransactionFeeTypeCode.E_WALLET_RECHARGE, Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, this.customerProperties.getDefaultCurrency());

        transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
        transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
        transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());

        Customer customer = this.getCustomerLogin();

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());

        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());

        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        transactionDTO.setMessage(StringUtil.join(new String[]{
                request.getAccountNumber(), request.getMessage()
        }, StringPool.SPACE));

        return this.makeTransferToMerchant(transactionDTO, MerchantCode.UMONEY.name(), customer, request.isSetDefaultAccount());
    }

    @Override
    public OtpTransferTransResponse requestOtpQrPayment(OtpQrBillRequest request,
                                                        HttpServletRequest httpServletRequest) {
        // validate số tiền giao dịch
        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        //lấy ra merchant theo merchant code, không phải master merchant
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(request.getMerchantId(),
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request, request.getMerchantId(), merchant.getMerchantName());

        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfMerchantTransaction(
                TransactionFeeTypeCode.INTERNAL_QR_BILLING, merchant.getMerchantId(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, this.customerProperties.getDefaultCurrency());

        transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
        transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
        transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());

        Customer customer = this.getCustomerLogin();

        transactionDTO.setMessage(StringUtil.join(
                new String[]{
                        customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_QR_PAYMENT),
                        request.getMerchantId()
                }, StringPool.SPACE));

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());

        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        transactionDTO.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());

        transactionDTO.setType(TransferTransactionType.QR_CODE_INTERNAL_MERCHANT);

        transactionDTO.setBankCode(mbApiConstantProperties.getQrPayment().getChannel());

        return this.makeTransferToMerchant(transactionDTO, merchant.getMerchantCode(), customer, request.isSetDefaultAccount());
    }

    public void verifyBalance(Transaction transaction, Customer customer, double rate, InternationalPayment internationalPayment) {
        if (!Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            return;
        }

        AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder().custCode(customer.getCif())
                .build();

        List<AccountBalanceDTO> accountBalanceDTOList = this.apiGeeTransferService
                .checkAccountBalance(accountBalanceRequest);

        if (Validator.isNull(accountBalanceDTOList)) {
            throw new BadRequestAlertException(ErrorCode.MSG100100);
        }

        String amount = accountBalanceDTOList.stream()
                .filter(item -> Validator.equals(item.getAccountNumber(), transaction.getCustomerAccNumber()))
                .map(AccountBalanceDTO::getAvailableBalance).findFirst().orElse(StringPool.NUMBER_0);

        double amountForeign = Math.round(Double.parseDouble(amount) / rate);

        if (amountForeign < transaction.getForeignAmount()) {
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            internationalPayment.setTransactionStatus(TransactionStatus.FAILED);

            this.transactionRepository.save(transaction);
            this.internationalPaymentRepository.save(internationalPayment);

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1108);
        }
    }

    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public OtpTransferTransResponse requestOtpTransfer(OtpTransferTransRequest request,
                                                       HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();
        double rate = 0L;
        IntlPaymentCacheDTO cache = null;

        if (Validator.isNotNull(request.getTransactionId())) {
            cache = setOtpTransferTransRequest(request);
        }
        // validate thông tin
        this.validateTransferTrans(request, cache);

        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(request.getType(), AccountType.QR)) {
            request.setBeneficiaryQrValue(request.getBeneficiaryAccountNumber());
        }

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        // tạm thời lấy default branch code vì bên MB Laos hiện tại chỉ có 1 chi nhánh duy nhất
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        // kiem tra so tai khoan ghi no va so tai khoan ghi co
        // tài khoản ghi nợ
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                request.getCustomerAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1038));

        MoneyAccount moneyAccountOfBenefit = this.moneyAccountRepository.findByAccountNumberAndStatus(
                request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null);

        this.checkTransactionLimitCustomerAndValidateMoneyAccount(moneyAccountOfCustomer, request, customer, null, moneyAccountOfBenefit);

        transactionDTO.setCustomerId(customer.getCustomerId());

        // Gen message
        if (Validator.isNull(request.getRemark())) {
            transactionDTO.setMessage(StringUtil.join(
                    new String[]{
                            customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_TRANSFER_MONEY)
                    }, StringPool.SPACE));
        }
        // check ekyc và thời gian hết hạn đổi mật khẩu
        CustomerEkyc ekyc = this.customerEkycRepository.findByCustomerIdAndStatusNot(customer.getCustomerId(), EntityStatus.DELETED.getStatus());

        boolean ekycRequired = Validator.isNull(ekyc) && customer.isEkycRequired();

        // Do bỏ rule hết hạn thay đổi mật khẩu. Nên không check pwExpiredTime
        if (ekycRequired) {
            throw new BadRequestAlertException(ErrorCode.MSG101094);
        }

        FeeTransactionResponse feeTransactionResponse;

        // tài khoản ghi có
        // kiem tra neu la chuyen khoan lapnet thi khong check tai khoan nhan
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())) {
            // lay tien phi giao dich noi bo trong cau hinh
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.TRANSFER_INTERNAL_BANK, Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, moneyAccountOfCustomer.getCurrency());

            MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                            request.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                    .orElse(null);

            this.checkTransactionLimitCustomerAndValidateMoneyAccount(moneyAccountOfCustomer, request, customer, feeTransactionResponse, null);

            Customer beneficiaryCustomer =
                    Objects.nonNull(creditAccount)
                            ? this.customerRepository.findByCustomerIdAndReferenceIdIsNull(
                            creditAccount.getCustomerId())
                            : null;

            if (Objects.isNull(beneficiaryCustomer)) {
                _log.info("BeneficiaryCustomer with account number {} has not any MBLAOS user yet", request.getBeneficiaryAccountNumber());
                // nếu khách hàng hưởng thụ chưa có tài khoản mb laos thì gọi sang bên t24 check tk
                BeneficiaryInfoResponse beneficiaryInfo = this.internalBankMB(request.getBeneficiaryAccountNumber());

                transactionDTO.setBeneficiaryCustomerName(beneficiaryInfo.getBeneficiaryName());
            } else {
                transactionDTO.setBeneficiaryCustomerId(beneficiaryCustomer.getCustomerId());
                transactionDTO.setBeneficiaryCustomerName(beneficiaryCustomer.getFullname());
            }
            transactionDTO.setType(Validator.equals(request.getType(), AccountType.QR) ? TransferTransactionType.QR_CODE_INTERNAL : TransferTransactionType.INTERNAL_BANK);
        } else {
            //bo sung tinh phi QR lapnet
            AccountType type = request.getType();

            String qrCodeValue = Validator.isNotNull(cache) ? cache.getQrCodeValue() : request.getBeneficiaryQrValue();
            // call sang T24 de check tai khoan nhan
            AccNumberVerifyRequest accNumberVerifyRequest = AccNumberVerifyRequest.builder()
                    .toAccount(AccountType.QR.equals(type) ? qrCodeValue : request.getBeneficiaryAccountNumber())
                    .toMember(request.getBeneficiaryBankCode())
                    .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                    .fromUserFullName(customer.getFullname())
                    .fromAccountType(FromAccountType.PERSONAL.name())
                    .internationalQuery(InternationalQuery.N.name())
                    .fromAccount(request.getCustomerAccNumber())
                    .fromUser(customer.getFullname())
                    .build();

            this.isInterPayment(request, type, accNumberVerifyRequest, transactionDTO, cache);

            AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(accNumberVerifyRequest);
            if (Validator.isNull(accNumberVerifyDTO) || Validator.isNull(accNumberVerifyDTO.getAccountName())) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            rate = Validator.isNotNull(accNumberVerifyDTO.getCrossBorderExchangeRate()) ?
                    Double.parseDouble(accNumberVerifyDTO.getCrossBorderExchangeRate().getRate()) : 0L;

            transactionDTO.setTransactionAmount(rate > 0 ? Math.round(Objects.requireNonNull(cache).getForeignAmount() * rate) : request.getAmount());
            transactionDTO.setTransactionId(accNumberVerifyDTO.getReferenceNumber());
            transactionDTO.setQrCodeValue(Validator.isNotNull(cache) ? cache.getQrCodeValue() : request.getBeneficiaryQrValue());
            transactionDTO.setBeneficiaryCustomerName(Validator.isNotNull(cache) ? cache.getBeneficiaryCustomerName() : transactionDTO.getBeneficiaryCustomerName());
            transactionDTO.setTarget(Validator.isNotNull(cache) ? cache.getQrCodeValue() : transactionDTO.getTarget());

            if (AccountType.QR.equals(type)) {
                //thanh toan merchant lapnet
                //mien phi neu trong thang:ut giao dich < feeFreeTransaction, tong tien < feeFreeAmount
                //khong thoa man thi lay phi trong cau hinh CMS
                feeTransactionResponse = getLapnetTransactionFee(request, transactionDTO, accNumberVerifyDTO, moneyAccountOfCustomer, cache);
            } else {
                transactionDTO.setType(TransferTransactionType.INTER_BANK);
                feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                        TransactionFeeTypeCode.TRANSFER_INTER_BANK,
                        Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L,
                        moneyAccountOfCustomer.getCurrency());
            }
        }

        if (Validator.isNotNull(feeTransactionResponse)) {
            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }

        if (Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency())
                && Validator.equals(request.getBeneficiaryCurrency(), customerProperties.getDefaultCurrency())) {
            transactionDTO.setTransactionAmount((double) Math.round(request.getAmount()));
        }

        if (Validator.equals(TransferTransactionType.QR_CODE_INTERNAL, transactionDTO.getType())) {
            transactionDTO.setBeneficiaryAccountNumber(transactionDTO.getTarget());
        }

        // call API MB để thực hiện tạo OTP
        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        this.saveInterPayment(request, transferSaved, customer, rate, feeTransactionResponse, cache);

        try {
            Beneficiary beneficiary = beneficiaryService.createBeneficiary(transactionDTO);
            // lưu danh bạ thụ hưởng nếu gạt nút lưu
            if (request.isSaveBeneficiary()) {
                beneficiary.setIsSaved(SaveStatus.SAVED.getStatus());
                beneficiaryRepository.save(beneficiary);
            }
            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount()) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), request.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("requestOtpTransfer, after requestOTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transactionId(transferSaved.getTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }


    private IntlPaymentCacheDTO setOtpTransferTransRequest(OtpTransferTransRequest request) {
        IntlPaymentCacheDTO cache = this.intlPaymentCacheRepository.get(request.getTransactionId());

        if (Validator.isNull(cache)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        request.setBeneficiaryQrValue(cache.getQrCodeValue());
        request.setBeneficiaryBankCode(cache.getBeneficiaryBankCode());
        request.setAmount(cache.getForeignAmount());
        request.setBeneficiaryCurrency(cache.getBeneficiaryCurrency());

        return cache;
    }

    private void isInterPayment(OtpTransferTransRequest request, AccountType type, AccNumberVerifyRequest accNumberVerifyRequest,
                                TransactionDTO transactionDTO, IntlPaymentCacheDTO dto) {
        if (Validator.equals(type, AccountType.QR) &&
                (Validator.equals(request.getBeneficiaryCurrency(), CurrencyType.KHR.name()))
                || Validator.equals(request.getBeneficiaryCurrency(), CurrencyType.VND.name())) {

            if (Validator.isNull(request.getTransactionId())) {
                throw new BadRequestAlertException(ErrorCode.MSG101199);
            }

            if (Validator.isNull(dto)) {
                throw new BadRequestAlertException(ErrorCode.MSG100072);
            }

            Common common = getCommon(request.getBeneficiaryCurrency());

            accNumberVerifyRequest.setInternationalQuery(InternationalQuery.Y.name());
            accNumberVerifyRequest.setToNation(common.getValue());
            accNumberVerifyRequest.setToMember(common.getDescription());

            transactionDTO.setForeignAmount(dto.getForeignAmount());
            transactionDTO.setBankCode(request.getBeneficiaryBankCode());
        }
    }

    private void saveInterPayment(OtpTransferTransRequest request, Transaction transferSaved, Customer customer, double rate,
                                  FeeTransactionResponse feeTransactionResponse, IntlPaymentCacheDTO cache) {
        if (Validator.equals(transferSaved.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            InternationalPaymentDTO dto = new InternationalPaymentDTO();

            if (Validator.isNull(feeTransactionResponse.getForeignFee())) {
                throw new BadRequestAlertException(ErrorCode.MSG1143);
            }

            dto.setTransferTransactionId(transferSaved.getTransferTransactionId());
            dto.setCustomerId(customer.getCustomerId());
            dto.setExchangeRate(rate);
            dto.setBeneficiaryCurrency(cache.getBeneficiaryCurrency());
            dto.setForeignFee(feeTransactionResponse.getForeignFee());
            dto.setNationCode(this.commonRepository
                    .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(),
                            cache.getBeneficiaryCurrency(), EntityStatus.ACTIVE.getStatus()).map(Common::getValue)
                    .orElse(null));

            BeanUtils.copyProperties(transferSaved, dto);
            dto.setPaymentSystem(getCommon(dto.getBeneficiaryCurrency()).getDescription());
            InternationalPayment internationalPayment = this.internationalPaymentRepository.save(this.internationalPaymentMapper.toEntity(dto));

            this.verifyBalance(transferSaved, customer, rate, internationalPayment);
            this.intlPaymentCacheRepository.put(transferSaved.getTransactionId(), IntlPaymentCacheDTO.builder().transactionId(request.getTransactionId()).build());
        }
    }

    private FeeTransactionResponse getLapnetTransactionFee(OtpTransferTransRequest request, TransactionDTO transactionDTO,
                                                           AccNumberVerifyDTO accNumberVerifyDTO, MoneyAccount moneyAccountOfCustomer,
                                                           IntlPaymentCacheDTO cache) {
        FeeTransactionResponse feeTransactionResponse;

        String qrCodeValue = Validator.isNotNull(cache) ? cache.getQrCodeValue() : request.getBeneficiaryQrValue();
        QrCodeResponse qrCodeResponse = qrCodeService.verifyQrCode(new QrVerifyRequest(qrCodeValue));

        if (Validator.equals(qrProperties.getMerchantAccountInfo().getPaymentTypeQrValue(), qrCodeResponse.getPaymentType())) {
            FeeTransactionRequest feeTransRequest = new FeeTransactionRequest();
            feeTransRequest.setTransactionAmount(request.getAmount());
            feeTransRequest.setBeneficiaryCurrency(request.getBeneficiaryCurrency());

            if (Validator.isNotNull(cache) && !Validator.equals(cache.getBeneficiaryCurrency(), CurrencyType.LAK.name())) {
                transactionDTO.setType(TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);
                transactionDTO.setBeneficiaryCustomerName(accNumberVerifyDTO.getAccountName());

                feeTransRequest.setTransactionId(request.getTransactionId());
                feeTransRequest.setBeneficiaryQrValue(cache.getQrCodeValue());
                feeTransRequest.setTransactionAmount(cache.getForeignAmount());
                feeTransRequest.setBeneficiaryBankCode(cache.getBeneficiaryBankCode());

                feeTransactionResponse = this.feeService.getFeeLapnetInterPayment(feeTransRequest, accNumberVerifyDTO);
            } else {
                transactionDTO.setType(TransferTransactionType.QR_CODE_LAPNET_MERCHANT);
                feeTransactionResponse = this.feeService.getFeeQrLapnetBilling(feeTransRequest, accNumberVerifyDTO, request.getCustomerCurrency());
            }
        } else {
            transactionDTO.setType(TransferTransactionType.QR_CODE_LAPNET_TRANSFER);
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.TRANSFER_INTER_BANK,
                    Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L,
                    moneyAccountOfCustomer.getCurrency());
        }
        return feeTransactionResponse;
    }

    @Override
    public OtpTransferTransResponse requestOtpTransferInsurance(OtpTransferInsuranceRequest request) {
        Customer customer = this.getCustomerLogin();

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        // tạm thời lấy default branch code vì bên MB Laos hiện tại chỉ có 1 chi nhánh duy nhất
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        // kiem tra so tai khoan ghi no va so tai khoan ghi co
        // tài khoản ghi nợ
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                request.getCustomerAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1038));

        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        }

        transactionDTO.setCustomerId(customer.getCustomerId());

        // Gen message
        if (Validator.isNull(request.getRemark())) {
            transactionDTO.setMessage(StringUtil.join(
                    new String[]{
                            customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_TRANSFER_MONEY)
                    }, StringPool.SPACE));
        }
        // check ekyc và thời gian hết hạn đổi mật khẩu
        CustomerEkyc ekyc = this.customerEkycRepository.findByCustomerIdAndStatusNot(customer.getCustomerId(), EntityStatus.DELETED.getStatus());

        boolean ekycRequired = Validator.isNull(ekyc) && customer.isEkycRequired();

        // Do bỏ rule hết hạn thay đổi mật khẩu. Nên không check pwExpiredTime
        if (ekycRequired) {
            throw new BadRequestAlertException(ErrorCode.MSG101094);
        }

        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                TransactionFeeTypeCode.INSURANCE, Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, moneyAccountOfCustomer.getCurrency());

        // tài khoản ghi có
        // kiem tra neu la chuyen khoan lapnet thi khong check tai khoan nhan
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())) {
            // lay tien phi giao dich noi bo trong cau hinh

            MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                            request.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                    .orElse(null);

            Customer beneficiaryCustomer =
                    Objects.nonNull(creditAccount)
                            ? this.customerRepository.findByCustomerIdAndReferenceIdIsNull(
                            creditAccount.getCustomerId())
                            : null;

            if (Objects.isNull(beneficiaryCustomer)) {
                _log.info("BeneficiaryCustomer with account number {} has not any MBLAOS user yet",
                        request.getBeneficiaryAccountNumber());
                // nếu khách hàng hưởng thụ chưa có tài khoản mb laos thì gọi sang bên t24 check tk
//                BeneficiaryInfoResponse beneficiaryInfo = this.internalBankMB(request.getBeneficiaryAccountNumber());

                BeneficiaryInfoResponse beneficiaryInfo = BeneficiaryInfoResponse.builder()
                        .beneficiaryName(transactionDTO.getBeneficiaryCustomerName())
                        .accountNumber(transactionDTO.getBeneficiaryAccountNumber())
                        .build();
                transactionDTO.setBeneficiaryCustomerName(beneficiaryInfo.getBeneficiaryName());
            } else {
                transactionDTO.setBeneficiaryCustomerId(beneficiaryCustomer.getCustomerId());
                transactionDTO.setBeneficiaryCustomerName(beneficiaryCustomer.getFullname());
            }
            transactionDTO.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            //bo sung tinh phi QR lapnet
            AccountType type = request.getType();
            transactionDTO.setType(TransferTransactionType.INTER_BANK);

            // call sang T24 de check tai khoan nhan
            AccNumberVerifyRequest accNumberVerifyRequest = AccNumberVerifyRequest.builder()
                    .toAccount(request.getBeneficiaryAccountNumber())
                    .toMember(request.getBeneficiaryBankCode())
                    .toType(this.mbApiConstantProperties.getOtpGen().getAccountType())
                    .fromUserFullName(customer.getFullname())
                    .fromAccountType(FromAccountType.PERSONAL.name())
                    .internationalQuery(InternationalQuery.N.name())
                    .fromAccount(request.getCustomerAccNumber())
                    .fromUser(customer.getFullname())
                    .build();

            AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(accNumberVerifyRequest);
            if (Validator.isNull(accNumberVerifyDTO) || Validator.isNull(accNumberVerifyDTO.getAccountName())) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            transactionDTO.setTransactionId(accNumberVerifyDTO.getReferenceNumber());
        }

        if (Validator.isNotNull(feeTransactionResponse)) {
            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }

        // call API MB để thực hiện tạo OTP
        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount()) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), request.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("requestOtpTransferInsurance, after requestOTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transactionId(transferSaved.getTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }

    @Override
    public OtpTransferTransResponse resendOtp(ResendOtpRequest request) {
        Transaction transaction = this.transactionRepository
                .findById(request.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1048));

        OtpTransferRequest mbOtpTransferRequest = OtpTransferRequest.builder()
                .accountNumber(transaction.getCustomerAccNumber())
                .accountType(this.mbApiConstantProperties.getOtpGen().getAccountType())
                .build();

        ReqFundTransferDTO response = this.apiGeeTransferService.requestFundTransfer(mbOtpTransferRequest);

        transaction.setTransactionId(response.getReferenceNumber());

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transaction.getTransferTransactionId())
                .transactionId(transaction.getTransactionId())
                .build();
    }

    @Override
    public void setDefaultAccountTransfer(Long customerId, String accountNumber) {
        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository.findByCustomerIdAndStatus(
                customerId, EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        moneyAccountDTOS.forEach(MoneyAccountDTO::isNotDefault);

        moneyAccountDTOS.forEach(item -> {
            if (Validator.equals(item.getAccountNumber(), accountNumber)) {
                item.setIsDefault();
            }
        });

        this.moneyAccountRepository.saveAll(this.moneyAccountMapper.toEntity(moneyAccountDTOS));
    }

    /**
     * validate các trường thông tin trong request
     *
     * @param request OtpTransferTransRequest
     */
    public void validateTransferTrans(OtpTransferTransRequest request, IntlPaymentCacheDTO cache) {
        this.validateAmount(request);

        if (Validator.equals(request.getCustomerAccNumber(), request.getBeneficiaryAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG1053);
        }

        if (Validator.equals(request.getType(), AccountType.ACCOUNT) && StringUtil.isBlank(request.getBeneficiaryAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG1031);
        }


        if (Validator.isNull(cache)) {
            if (Validator.isNull(request.getBeneficiaryBankCode())) {
                throw new BadRequestAlertException(ErrorCode.MSG101065);
            }

            if (Validator.isNull(request.getBeneficiaryCurrency())) {
                throw new BadRequestAlertException(ErrorCode.MSG101037);
            }

            if (Validator.isNull(request.getBeneficiaryCustomerName())) {
                throw new BadRequestAlertException(ErrorCode.MSG101084);
            }
        }

        String nationCode = NationCode.LA.name();
        String beneficiaryCurrency = Validator.isNotNull(cache) ? cache.getBeneficiaryCurrency() : request.getBeneficiaryCurrency();
        String beneficiaryBankCode = Validator.isNotNull(cache) ? cache.getBeneficiaryBankCode() : request.getBeneficiaryBankCode();

        if (!Validator.equals(beneficiaryCurrency, CurrencyType.LAK.name())) {
            nationCode = this.commonRepository
                    .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(), beneficiaryCurrency, EntityStatus.ACTIVE.getStatus())
                    .map(Common::getValue).orElse(NationCode.LA.name());
        }

        this.bankRepository.findByBankCodeAndStatusAndNation(beneficiaryBankCode, EntityStatus.ACTIVE.getStatus(), nationCode)
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100162));
    }

    private void validateAmount(OtpTransferTransRequest request) {
        if (Validator.isNull(request.getAmount())) {
            throw new BadRequestAlertException(ErrorCode.MSG1138);
        }

        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        if (request.getAmount() == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG100169);
        }
    }

    @Override
    public BillingResponse verifyBillingCode(BillingV2Request billingRequest) {
        Customer customer = this.getCustomerLogin();

        LocalDateTime now = LocalDateTime.now();
        Long amount = 0L;
        String fullName = null;
        BillingType billingType = BillingType.getBillingType(billingRequest.getBillingType());
        boolean isPhoneNumber = billingType.equals(BillingType.PSTN) || billingType.equals(BillingType.POSTPAID_MOBILE);

        Telco telco = this.telcoRepository.findByTelcoCodeAndStatus(
                billingRequest.getTelcoCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        MoneyAccount moneyAccount = moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(billingRequest.getAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1035));

        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        BillingResponse billingResponse = BillingResponse.builder()
                .billingAt(now)
                .networkType(telco.getTelcoName())
                .build();

        // call api sang bên nhà mạng
        //UNITEL
        if (Validator.equals(telco.getTelcoCode(), TelcoType.UNITEL.name())) {

            ApiUnitelRequest queryRequest = ApiUnitelRequest.builder()
                    .mti(consumerProperties.getApiUnitel().getMti())
                    .billingType(billingType)
                    .transTime(InstantUtil.formatStringLongTimestamp(Instant.now(), ZoneId.of(StringPool.UTC)))
                    .systemTrace(this.apiUnitelService.generateTransactionId())
                    .clientId(consumerProperties.getApiUnitel().getClientId())
                    .build();

            if (billingType.equals(BillingType.INTERNET)) {
                queryRequest.setCustomerCode(billingRequest.getBillingCode());
            } else {
                queryRequest.setMsisdn(billingRequest.getBillingCode());
            }

            QueryDebtUnitelResponse queryDebtResponse = this.apiUnitelService.queryDebtPostpaidSubscriber(queryRequest);

            if (Objects.isNull(queryDebtResponse) || !queryDebtResponse.isSuccess()) {
                if (isPhoneNumber) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                } else {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }
            }

            amount = queryDebtResponse.getDebt();
            billingResponse.setBalance(amount);
            billingResponse.setFullName(billingRequest.getBillingCode());

            //LTC
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.name()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.name())) {
            VerifyInternetLtcResponse verifyInternetLtcResponse = null;
            VerifyPostPaidLtcResponse verifyPostPaidLtcResponse = null;
            VerifyPstnLtcResponse verifyPstnLtcResponse = null;
            String transactionId = RandomGenerator.generateRandomNumber(1, 23);
            String msisdn = NumberUtil.formatPhoneNumber(billingRequest.getBillingCode());

            if (Validator.equals(billingType, BillingType.INTERNET)) {
                verifyInternetLtcResponse = this.apiMmoneyService.verifyInternet(
                        VerifyInternetLtcMmoneyRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type(BillingType.INTERNET.toString())
                                .build()
                );

                if (Validator.isNull(verifyInternetLtcResponse) || !verifyInternetLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }

                amount = Long.valueOf(verifyInternetLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyInternetLtcResponse.getName());
            } else if (Validator.equals(billingType, BillingType.POSTPAID_MOBILE)) {
                verifyPostPaidLtcResponse = this.apiMmoneyService.verifyPostPaid(
                        VerifyPostPaidLtcMmoneyRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type("POSTPAID")
                                .build()
                );

                if (Validator.isNull(verifyPostPaidLtcResponse) || !verifyPostPaidLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                }

                amount = GetterUtil.getLong(verifyPostPaidLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyPostPaidLtcResponse.getName());
            } else if (Validator.equals(billingType, BillingType.PSTN)) {
                verifyPstnLtcResponse = this.apiMmoneyService.verifyPstn(
                        VerifyPstnMmoneyPstnRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type(BillingType.PSTN.toString())
                                .build()
                );

                if (Validator.isNull(verifyPstnLtcResponse) || !verifyPstnLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                }

                amount = GetterUtil.getLong(verifyPstnLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyPstnLtcResponse.getName());
            }

            billingResponse.setBalance(amount);
            billingResponse.setFullName(fullName);
        }
        // ETL
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.ETL.name())) {
            QueryEtlResponse queryEtlResponse = new QueryEtlResponse();

            String transactionId = this.apiETLService.generateTransactionId();
            if (billingType.equals(BillingType.INTERNET)) {
                queryEtlResponse = this.apiETLService.queryInternetDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            } else if (billingType.equals(BillingType.PSTN)) {
                queryEtlResponse = this.apiETLService.queryPSTNDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            } else if (billingType.equals(BillingType.POSTPAID_MOBILE)) {
                queryEtlResponse = this.apiETLService.queryPostPaidDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            }

            if (Objects.isNull(queryEtlResponse) || !queryEtlResponse.isSuccess()) {
                if (isPhoneNumber) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                } else {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }
            }

            amount = Long.valueOf(queryEtlResponse.getUnPaidAmount());
            billingResponse.setBalance(amount);
            billingResponse.setFullName(queryEtlResponse.getCustomerName());
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.BTC.name())) {
            BestTelecomResponse bestTelecomResponse = new BestTelecomResponse();

            if (billingType.equals(BillingType.POSTPAID_MOBILE)) {
                bestTelecomResponse = this.apiBestTelecomService.validateMobileNumber(this.bestTelecomRequest(billingRequest.getBillingCode()));
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG1170);
            }

            amount = Math.round(Math.ceil(bestTelecomResponse.getBillOutStandings().stream().mapToDouble(item -> Double.parseDouble(item.getBalOutstandingAmt())).sum()));
            billingResponse.setBalance(amount);
            billingResponse.setFullName(billingRequest.getBillingCode());
        } else {
            throw new BadRequestAlertException(ErrorCode.MSG1170);
        }

//         validate số tiền thanh toán hóa đơn
        if (Validator.isNull(amount) || amount == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101021);
        }

        // Tính phí giao dịch
        FeeTransactionResponse feeTransactionResponse;
        if (BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType()) != null) {
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.valueOf(BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType())),
                    amount, this.customerProperties.getDefaultCurrency());

            billingResponse.setTransactionDiscount(feeTransactionResponse.getDiscount());
            billingResponse.setTransactionFee(feeTransactionResponse.getFee());
            billingResponse.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
            billingResponse.setDiscountFixed(feeTransactionResponse.getDiscountFixed());
        }

        long totalAmount = Math.round(billingResponse.getBalance() + billingResponse.getTransactionFee() - billingResponse.getDiscountFixed() - billingResponse.getBalance() * billingResponse.getTransactionDiscount() / 100);
        billingResponse.setTotalAmount(totalAmount);
        billingResponse.setStatus(
                moneyAccount.getAvailableAmount() < billingResponse.getTotalAmount() ?
                        EntityStatus.INACTIVE.getStatus() : EntityStatus.ACTIVE.getStatus());
        return billingResponse;
    }

    @Override
    public BillingResponse verifyBillingCode(BillingRequest billingRequest) {
        // call api sang bên nhà mạng
        LocalDateTime now = LocalDateTime.now();
        CheckBalanceLtcResponse checkBalanceLtcResponse = this.apiLtcService.checkBalanceResult(
                VerifyPhoneNumberRequest.builder().phoneNumber(billingRequest.getBillingCode()).build());

        if (Validator.isNull(checkBalanceLtcResponse) ||
                !Validator.equals(checkBalanceLtcResponse.getHeader().getResultCode(),
                        this.consumerProperties.getApiLtc().getSuccessCode())) {
            throw new BadRequestAlertException(ErrorCode.MSG3004);
        }

        Long amount = GetterUtil.getLong(checkBalanceLtcResponse.getBalance());
        // validate số tiền thanh toán hóa đơn

        if (Validator.isNull(amount) || amount == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1163);
        }

        BillingResponse billingResponse = BillingResponse.builder()
                .networkType(checkBalanceLtcResponse.getNetworkType())
                .balance(amount)
                .billingAt(now)
                .type(checkBalanceLtcResponse.getType())
                .build();

        if (Validator.equals(checkBalanceLtcResponse.getNetworkType(), TelcoType.LTC.name())) {
            billingResponse.setFullName(billingRequest.getBillingCode());
        }

        return billingResponse;
    }

    @Override
    public Page<TransactionDTO> getQrHistory(TransactionSearchRequest request, Pageable pageable) {

        Customer customer = this.getCustomerLogin();
        String countryCodeValue = qrProperties.getMerchantAccountInfo().getCountryCodeValue();
        LocalDateTime localDateTime = LocalDateTime.now(ZoneOffset.UTC).plusHours(7);

        if (request.getFromDate().isAfter(localDateTime.toLocalDate()) || request.getToDate().isAfter(localDateTime.toLocalDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1060);
        }

        if (request.getFromDate().isAfter(request.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1058);
        }

        if (Period.between(request.getToDate().minus(this.transferTransProperties.getMaxPeriod().minusDays(1)), request.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(ErrorCode.MSG1096);
        }

        if (request.getFromDate().isBefore(LocalDate.now().minus(this.transferTransProperties.getExceedDatePeriod().minusDays(1)))
                || request.getToDate().isBefore(LocalDate.now().minus(this.transferTransProperties.getExceedDatePeriod().minusDays(1)))) {
            throw new BadRequestAlertException(ErrorCode.MSG1148);
        }

        List<MoneyAccount> moneyAccounts = moneyAccountRepository.findAllByCustomerIdAndStatus(
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        List<String> customerAccounts = moneyAccounts.stream().map(MoneyAccount::getAccountNumber).collect(Collectors.toList());

        List<TransferTransactionType> types = TransferTransactionType.getQrTransaction();
        request.setTypes(types);
        request.setTransactionStatus(Collections.singletonList(TransactionStatus.SUCCESS));
        request.setHasPageable(true);
        request.setCustomerAccNumbers(customerAccounts);
        request.setHistory(true);

        List<TransactionDTO> transactions = this.transactionMapper.toDto(this.transactionRepository.searchTransaction(request, pageable));

        List<String> bankCodes = transactions.stream().map(TransactionDTO::getBankCode).collect(Collectors.toList());
        List<Bank> banks = this.bankRepository.findAllByBankCodeInAndStatusNot(bankCodes, EntityStatus.DELETED.getStatus());

        List<String> merchantCodes = transactions.stream().map(TransactionDTO::getTarget).collect(Collectors.toList());

        // lấy merchant không lấy master merchant
        List<Merchant> merchants = this.merchantRepository.findAllByMerchantCodeInAndStatusNotAndParentIdNotNull(merchantCodes, EntityStatus.DELETED.getStatus());

        Optional<Bank> bankMb = this.bankRepository.findByBankCodeAndStatusAndNation(
                this.mbApiConstantProperties.getInBanKTransfer().getChannel(),
                EntityStatus.ACTIVE.getStatus(), request.getNation());

        transactions.forEach(item -> {
            if (TransferTransactionType.getQrLapnetTransaction().contains(item.getType())) {
                item.setBeneficiaryAccountNumber(null);
            }
            banks.forEach(bank -> {
                if (!Validator.equals(item.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
                    if (Validator.equals(bank.getBankCode(), item.getBankCode()) && Validator.equals(bank.getNation(), countryCodeValue)) {
                        item.setBankName(bank.getBankName());
                        item.setNation(countryCodeValue);
                    }
                } else {
                    if (Validator.equals(bank.getBankCode(), item.getBankCode())) {
                        item.setBankName(bank.getBankName());
                        item.setNation(bank.getNation());
                    }
                }
            });
            merchants.forEach(merchant -> {
                if (Validator.equals(merchant.getMerchantCode(), item.getTarget())) {
                    item.setMerchant(merchant);
                }
            });
            if (TransferTransactionType.QR_CODE_INTERNAL_MERCHANT.equals(item.getType()) && bankMb.isPresent()) {
                item.setBankName(bankMb.get().getBankName());
            }
        });

        return new PageImpl<>(transactions, pageable, transactions.size());
    }

    @Override
    public Set<CashInHistoryResponse> getHistoryCashIn(CashInHistorySearch request) {

        Customer customer = this.getCustomerLogin();

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        List<Transaction> transactions = this.transactionRepository.getCashInHistories(request, pageable, customer.getCustomerId());

        Set<CashInHistoryResponse> responses = new LinkedHashSet<>();

        transactions.forEach(transaction -> {

            CashInHistoryResponse cashInHistoryResponse = CashInHistoryResponse.builder()
                    .phoneNumber(transaction.getTarget())
                    .accountName(transaction.getBeneficiaryCustomerName())
                    .amount(transaction.getTransactionAmount().longValue())
                    .build();
            responses.add(cashInHistoryResponse);
        });

        return responses;
    }

    @Override
    public TransactionDTO getDetailQrHistory(TransactionSearchRequest request) {

        Customer customer = this.getCustomerLogin();

        if (Validator.isNull(request.getTransferTransactionId())) {
            throw new BadRequestAlertException(ErrorCode.MSG100171);
        }

        List<MoneyAccount> moneyAccounts = moneyAccountRepository.findAllByCustomerIdAndStatus(
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        MoneyAccount moneyAccount = moneyAccounts.stream()
                .filter(item -> item.getAccountNumber().equals(request.getCustomerAccNumber()))
                .findFirst().orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1038));

        Optional<Transaction> transaction = this.transactionRepository.findByTransferTransactionIdAndCustomerAccNumberAndTransactionStatusAndTypeIn(request.getTransferTransactionId(), moneyAccount.getAccountNumber(), TransactionStatus.SUCCESS, TransferTransactionType.getQrTransaction());

        if (!transaction.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100167);
        }

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction.get());

        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.get().getType())) {
            transactionDTO.setBeneficiaryAccountNumber(null);
        }

        Bank bank;
        if (Validator.equals(transactionDTO.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            bank = this.bankRepository.findByBankCodeAndNationAndStatusNot(transactionDTO.getBankCode(), request.getNation(), EntityStatus.DELETED.getStatus());

            Optional<InternationalPayment> interPayment = this.internationalPaymentRepository.findByTransferTransactionId(transactionDTO.getTransferTransactionId());
            transactionDTO.setBeneficiaryCurrency(
                    interPayment
                            .map(InternationalPayment::getBeneficiaryCurrency)
                            .orElse(transactionDTO.getTransactionCurrency())
            );
        } else {
            bank = this.bankRepository.findByBankCodeAndNationAndStatusNot(this.mbApiConstantProperties.getInBanKTransfer().getChannel(), request.getNation(), EntityStatus.DELETED.getStatus());
            transactionDTO.setBeneficiaryCurrency(transactionDTO.getTransactionCurrency());
        }

        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(transactionDTO.getTarget(), EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(bank)) {
            transactionDTO.setBankName(bank.getBankName());
            transactionDTO.setNation(bank.getNation());
        }

        if (Validator.isNotNull(merchant)) {
            transactionDTO.setMerchant(merchant);
        }

        return transactionDTO;
    }

    private OtpTransferConfirmResponse confirmTransferToMerchant(TransactionDTO transactionDTO, String otp, String deviceId) {

        // kiểm tra trạng thái của giao dịch
        if (Validator.equals(transactionDTO.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        Transaction transaction = this.transactionMapper.toEntity(transactionDTO);
        double amount = Validator.equals(transactionDTO.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)
                ? transactionDTO.getForeignAmount() : transactionDTO.getTransactionAmount();

        // limit transaction
        this.transactionService.checkTransactionLimit(String.valueOf(transactionDTO.getTransferType()), transactionDTO.getCustomerId(),
                transactionDTO.getCustomerAccNumber(), amount, transactionDTO.getType(), transaction);

        Optional<TransactionMerchant> transactionMerchant = this.transactionMerchantRepository
                .findByTransferTransactionId(transactionDTO.getTransferTransactionId());

        if (!transactionMerchant.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100010);
        }

        Merchant merchant = this.merchantRepository
                .findByMerchantIdAndStatus(transactionMerchant.get().getMerchantId(),
                        EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }


        MBOtpTransConfirmResponse response = this.confirmFundTransferMB(transactionDTO, otp, deviceId);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transactionDTO.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {

                transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
                this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            }
        }

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            _log.info("Transaction has been failed, {}, status {}", transactionDTO.getTransactionId(),
                    response.getTransactionStatus());

            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        // Thanh toán hóa đơn
        if (Validator.isNotNull(transactionDTO.getBillingType())) {
            Optional<Telco> telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus());
            this.makeBillPayment(response, telco, transactionDTO, transactionMerchant.get());
        }

        if (Validator.equals(merchant.getMerchantCode(), MerchantCode.UMONEY.name())) {
            TransferAccountResponse transferUmoneyResponse = this.ewalletService.transferUmoney(
                    TransferAccountRequest.builder()
                            .phoneNumber(transactionDTO.getTarget())
                            .content(transactionDTO.getMessage())
                            .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                            .bankAccount(transactionDTO.getCustomerAccNumber())
                            .bankTransId(response.getT24ReferenceNumber())
                            .build());

            // revert giao dịch
            if (Validator.isNull(transferUmoneyResponse) ||
                    !Validator.equals(
                            transferUmoneyResponse.getErrorCode(),
                            this.consumerProperties.getApiUmoney().getSuccessCode())) {

                this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                        .t24ReferenceNumber(response.getT24ReferenceNumber())
                        .transactionId(response.getReferenceNumber())
                        .build());

                throw new BadRequestAlertException(ErrorCode.MSG1162);
            } else {
                transactionDTO.setExtraTransactionCode(transferUmoneyResponse.getTransId());
            }
        }
        transactionDTO.setTransactionStatus(TransactionStatus.SUCCESS);
        transactionDTO.setTransactionFinishTime(Instant.now());
        transactionDTO.setTransactionCode(response.getT24ReferenceNumber());

        this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

        try {
            beneficiaryService.createBeneficiary(transactionDTO);
        } catch (Exception e) {
            _log.error("request Otp, after request OTP error:", e);
        }

        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now())
                .transactionCode(response.getT24ReferenceNumber())
                .transactionFee(transactionDTO.getTransactionFee())
                .transactionId(transactionDTO.getTransactionId())
                .build();
    }

    private String formatMoney(Long price) {
        DecimalFormat decimalFormat = new DecimalFormat(StringPool.CURRENCY_FORMAT);
        return decimalFormat.format(price);
    }

    private String formatMoney(Double price) {
        DecimalFormat decimalFormat = new DecimalFormat(StringPool.CURRENCY_FORMAT_DECIMAL_FOREIGN);
        return decimalFormat.format(price);
    }

    private void createNotification(TransactionDTO transactionDTO, Long customerId) {
        Map<String, String> valuesMapDebitAccount = new HashMap<>();
        Map<String, String> valuesMapCreditAccount = new HashMap<>();

        String transactionFinishTime = transactionDTO.formatTransactionFinishTime();

        // transfer
        valuesMapDebitAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapDebitAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapDebitAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
        valuesMapDebitAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());
        valuesMapDebitAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + formatMoney(transactionDTO.getActualTransactionAmount()));
        valuesMapDebitAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapDebitAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapDebitAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapDebitAccount.put((TemplateField.MESSAGE.name()), StringUtil.replaceHtmlCharacter(transactionDTO.getMessage()));
        valuesMapDebitAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionDTO.getTransactionCode());

        // receive (chuyển khoản liên ngân hàng không có thông báo biến động số dư tài khoản nhận tiền)
        valuesMapCreditAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapCreditAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getTarget());
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + formatMoney(transactionDTO.getTransactionAmount()));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        valuesMapCreditAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionDTO.getTransactionCode());

        ContentTemplate templateTransferMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_TRANSFER_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        ContentTemplate templateTransferLapnet = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_TRANSFER_LAPNET.name(), EntityStatus.ACTIVE.getStatus()
        );

        NotificationDTO notiForReceiver = new NotificationDTO();
        NotificationDTO notiForSender = new NotificationDTO();

        if (Validator.isNotNull(templateTransferMoney) || Validator.isNotNull(templateTransferLapnet)) {
            notiForReceiver.setNotificationStatus(NotificationStatus.PENDING);
            notiForReceiver.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForReceiver.setTargetType(TargetType.USER);
            notiForReceiver.setEndUserType(EndUserType.CUSTOMER);
            notiForReceiver.setClassPk(transactionDTO.getCustomerId());
            notiForReceiver.setPublishTime(LocalDateTime.now());

            if (Validator.equals(transactionDTO.getBankCode(), this.constantProperties.getUmoney().getAccountBank())) {
                notiForReceiver.setTitle(Labels.getLabels(templateTransferMoney.getTitle()));
                notiForReceiver
                        .setContent(StringUtil.replaceMapValue(templateTransferMoney.getContent(), valuesMapDebitAccount));
                notiForReceiver.setDescription(templateTransferMoney.getDescription());
            } else {
                notiForReceiver.setTitle(Labels.getLabels(templateTransferLapnet.getTitle()));
                notiForReceiver
                        .setContent(StringUtil.replaceMapValue(templateTransferLapnet.getContent(), valuesMapDebitAccount));
                notiForReceiver.setDescription(templateTransferLapnet.getDescription());
            }

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForReceiver));
            notiForReceiver = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            this.notiTransactionRepository.save(notiTransaction);
        }


        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_RECEIVE_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney)) {

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // Neu giao dich la chuyen tien cho các tài khoản bên ngoài hệ thống => ko gửi
            // thông báo
            List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                    .findByAccountNumberAndStatus(transactionDTO.getTarget(), EntityStatus.ACTIVE.getStatus());

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            for (MoneyAccount moneyAccount : moneyAccounts) {
                Long beneficiaryCustomerId = moneyAccount.getCustomerId();

                notiForSender.setClassPk(beneficiaryCustomerId);

                notiForSender.setPublishTime(LocalDateTime.now());

                notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
                notiForSender.setContent(
                        StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
                notiForSender.setDescription(templateReceiveMoney.getDescription());

                Notification notificationAfterSaved = this.notificationRepository
                        .save(this.notificationMapper.toEntity(notiForSender));

                notiForSender = this.notificationMapper.toDto(notificationAfterSaved);

                // save notification transaction
                NotiTransaction notiTransaction = new NotiTransaction();

                notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
                notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
                notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

                notiTransactions.add(notiTransaction);
            }

            if (Validator.equals(notiForSender.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
                this.sendService.sendNotification(notiForSender);
            }
            if (Validator.equals(notiForReceiver.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
                this.sendService.sendNotification(notiForReceiver);
            }

            this.notiTransactionRepository.saveAll(notiTransactions);
        }
    }

    private void createTransactionMerchant(Long transferTransactionId, Long merchantId, Integer merchantType) {
        TransactionMerchant transactionMerchant = new TransactionMerchant();

        transactionMerchant.setTransferTransactionId(transferTransactionId);

        transactionMerchant.setMerchantId(merchantId);

        transactionMerchant.setType(merchantType);

        this.transactionMerchantRepository.save(transactionMerchant);
    }

    private void enrichInformationHistory(List<TransactionHistoryDTO> transactionHistoryDTOS) {
        Customer customer = this.getCustomerLogin();

        // get transactionId
        List<String> transactionCodes = transactionHistoryDTOS.stream()
                .map(TransactionHistoryDTO::getReferenceNumber)
                .collect(Collectors.toList());

        // query transaction by transactionId and enrich to dto
        List<Transaction> transactions = this.transactionRepository.findByTransactionCodes(transactionCodes,
                customer.getCustomerId(), Collections.singletonList(TransactionStatus.SUCCESS));

        // get bank list
        List<String> bankCodes = transactions.stream().map(Transaction::getBankCode).collect(Collectors.toList());

        List<Bank> banks = this.bankRepository.findAllByBankCodeInAndStatus(bankCodes, EntityStatus.ACTIVE.getStatus());

        // get beneficiary account list
        List<String> accountNumbers = transactions.stream()
                .map(Transaction::getTarget)
                .distinct()
                .collect(Collectors.toList());

        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                .findAllByMoneyAccountsAndStatus(accountNumbers, EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        // Tài khoản bên phía lapnet thì sẽ không tồn tại trong hệ thống nên sẽ chỉ lấy thông tin tài khoản để xử lý
        List<String> accountNumberInternal = moneyAccountDTOS.stream()
                .map(MoneyAccountDTO::getAccountNumber)
                .collect(Collectors.toList());

        accountNumbers.removeAll(accountNumberInternal);
        accountNumbers.forEach(accountNumber -> moneyAccountDTOS.add(new MoneyAccountDTO(accountNumber)));

        List<Long> customerIds = moneyAccounts.stream().map(MoneyAccount::getCustomerId).collect(Collectors.toList());
        List<Customer> customers = this.customerRepository.findAllByCustomerIdInAndStatus(customerIds,
                EntityStatus.ACTIVE.getStatus());

        // map information of customer to money account
        // nếu tài khoản tồn tại trong hệ thống => get thông tin từ bảng customers
        // nếu tài khoản ko tồn tại trong hệ thống => get thông tin từ transactions
        moneyAccountDTOS.forEach(item -> {
            Optional<Customer> optionalCustomer = customers.stream()
                    .filter(c -> Validator.equals(Long.valueOf(c.getCustomerId()), item.getCustomerId()))
                    .findFirst();
            if (optionalCustomer.isPresent()) {
                item.setCustomerName(optionalCustomer.get().getFullname());
            } else {
                Optional<Transaction> optionalTransaction = transactions.stream()
                        .filter(t -> Validator.equals(t.getTarget(), item.getAccountNumber()))
                        .findFirst();

                optionalTransaction.ifPresent(t -> item.setCustomerName(t.getBeneficiaryCustomerName()));
            }
        });

        // enrich các thông tin vào lịch sử giao dịch
        transactionHistoryDTOS
                .forEach(item -> this.processHistoryTransaction(item, transactions, moneyAccountDTOS, banks));

    }

    /**
     * enrich thêm các thông tin như nội dung thu phí, thông tin ngân hàng nhận hoặc các thông tin thêm (nếu có)
     *
     * @param otpVerifyTransferRequest OtpVerifyTransferRequest
     * @param transaction              void
     */
    private void enrichVerifyTransferRequest(OtpVerifyTransferRequest otpVerifyTransferRequest,
                                             TransactionDTO transaction) {
        List<AddInfoList> addInfoList = new ArrayList<>();

        AddInfoList benAcc = new AddInfoList();

        benAcc.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNo());
        benAcc.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());
        benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());

        AddInfoList benCustomer = new AddInfoList();

        benCustomer.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforName());
        benCustomer.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());

        // @TODO khách hàng confirm với tài khoản <= 11 ký tự phải cộng thêm chuỗi ở đằng trước
        int customerAccountLength = transaction.getCustomerAccNumber().length();

        if (customerAccountLength <= Integer.parseInt(StringPool.MIN_CUSTOMER_ACCOUNT_LENGTH)) {
            int endLength = Integer.parseInt(StringPool.CUSTOMER_ACCOUNT_LENGTH) - customerAccountLength;
            benCustomer.setValue(UUIDUtil.generateRandomAlpha(endLength) + transaction.getCustomerAccNumber());
        } else {
            benCustomer.setValue(transaction.getCustomerAccNumber());
        }

        // enrich creditBank nếu là ck lapnet (bankCode != MB)
        if (Validator.equals(transaction.getTransferType(), TransferType.TRANSFER_MONEY) &&
                !Validator.equals(
                        transaction.getBankCode(), this.mbApiConstantProperties.getInBanKTransfer().getChannel())) {
            CreditBank creditBank = new CreditBank();

            creditBank.setCode(transaction.getBankCode());

            otpVerifyTransferRequest.setCreditBank(creditBank);

            benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());
        }

        addInfoList.add(benAcc);
        addInfoList.add(benCustomer);

        // enrich chargeInfo
        ChargeInfo chargeInfo = new ChargeInfo();

        chargeInfo.setAmount(String.valueOf(Math.round(transaction.getTransactionFee())));
        chargeInfo.setCurrency(transaction.getTransactionCurrency());
        chargeInfo.setDescription(transaction.getMessage());

        otpVerifyTransferRequest.setAddInfoList(addInfoList);
        otpVerifyTransferRequest.setChargeInfo(chargeInfo);
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }

    private BeneficiaryInfoResponse interbank(BeneficiaryConfirmRequest beneficiaryConfirmRequest) {
        BeneficiaryInfoResponse response;
        String accountNumber = beneficiaryConfirmRequest.getAccountNumber();
        String bankCode = beneficiaryConfirmRequest.getBankCode();
        String beneficiaryAccNumber = beneficiaryConfirmRequest.getBeneficiaryAccountNumber();
        AccountType type = beneficiaryConfirmRequest.getType();
        String currency;

        Customer customer = this.getCustomerLogin();

        if (Validator.isNotNull(accountNumber)) {
            // kiem tra so tai khoan ghi no va so tai khoan ghi co
            // tài khoản ghi nợ
            MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                    accountNumber, customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                    () -> new BadRequestAlertException(ErrorCode.MSG1038));

            if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
                throw new BadRequestAlertException(ErrorCode.MSG1151);
            }
        } else {
            // lấy tk trong danh sách tk
            List<MoneyAccount> moneyAccounts = this.moneyAccountRepository.findByCustomerIdAndStatus(
                    customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!moneyAccounts.isEmpty()) {
                MoneyAccount defaultAccount =
                        moneyAccounts.stream().filter(MoneyAccount::isDefault).findFirst().orElse(null);

                if (Objects.isNull(defaultAccount)) {
                    defaultAccount = moneyAccounts.get(QueryUtil.FIRST_INDEX);
                }

                accountNumber = defaultAccount.getAccountNumber();
            }
        }

        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .toAccount(beneficiaryAccNumber)
                .toMember(bankCode)
                .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                .fromAccount(accountNumber)
                .fromUser(customer.getFullname())
                .fromUserFullName(customer.getFullname())
                .fromAccountType(FromAccountType.PERSONAL.name())
                .internationalQuery(InternationalQuery.N.name())
                .build();

        currency = this.getCurrencyInterPayment(request);
        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        response = BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .currency(Validator.isNotNull(currency) ? currency : accNumberVerifyDTO.getAccountCurrency())
                .feeList(accNumberVerifyDTO.getFeeList().getLak())
                .build();

        if (Validator.isNotNull(accNumberVerifyDTO.getCrossBorderExchangeRate())) {
            Double rate = Double.parseDouble(accNumberVerifyDTO.getCrossBorderExchangeRate().getRate());

            response.setRate(rate);
            response.setTransactionId(UUID.randomUUID().toString());

            IntlPaymentCacheDTO dto = IntlPaymentCacheDTO.builder()
                    .qrCodeValue(beneficiaryConfirmRequest.getBeneficiaryAccountNumber())
                    .build();

            this.intlPaymentCacheRepository.put(response.getTransactionId(), dto);
        }

        return response;
    }

    private String getCurrencyInterPayment(AccNumberVerifyRequest request) {
        List<Common> commons = this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.NATION_ID.name(), EntityStatus.ACTIVE.getStatus());

        Optional<Common> common = commons.stream()
                .filter(item -> Validator.equals(item.getDescription(), request.getToMember()))
                .findFirst();

        if (!common.isPresent()) {
            return null;
        }

        request.setInternationalQuery(InternationalQuery.Y.name());
        request.setToNation(common.get().getValue());
        request.setToMember(common.get().getDescription());

        return common.get().getCode();
    }

    private void validateInterPayment(OtpTransferTransRequest otpTransRequest, AccNumberVerifyRequest request, TransactionDTO transactionDTO) {
        if (!Validator.equals(otpTransRequest.getCustomerCurrency(), CurrencyType.LAK.name())) {
            throw new BadRequestAlertException(ErrorCode.MSG100047);
        }

        if (!Validator.equals(otpTransRequest.getBeneficiaryCurrency(), CurrencyType.LAK.name())) {
            Common common = getCommon(otpTransRequest.getBeneficiaryCurrency());

            request.setInternationalQuery(InternationalQuery.Y.name());
            request.setToNation(common.getValue());
            request.setToMember(common.getDescription());

            transactionDTO.setBankCode(otpTransRequest.getBeneficiaryBankCode());
        }
    }

    @NotNull
    private Common getCommon(String otpTransRequest) {
        Optional<Common> common = this.commonRepository
                .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(),
                        otpTransRequest, EntityStatus.ACTIVE.getStatus());

        if (!common.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100056);
        }
        return common.get();
    }

    private BeneficiaryInfoResponse getCustomerInterBankInfo(BeneficiaryConfirmRequest beneficiaryConfirmRequest) {
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        String accountNumber = "";
        String accountName = "";
        if (Objects.equals(clientName, "umoney")) {
            CashoutAccount cashoutAccount = cashoutAccountRepository.findFirstByStatusIs(EntityStatus.ACTIVE.getStatus())
                    .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101054));
            accountNumber = cashoutAccount.getAccountNumber();
            accountName = cashoutAccount.getAccountName();
        }
        String bankCode = beneficiaryConfirmRequest.getBankCode();
        String beneficiaryAccNumber = beneficiaryConfirmRequest.getBeneficiaryAccountNumber();
        AccountType type = beneficiaryConfirmRequest.getType();

        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .fromAccount(accountNumber)
                .fromUser(accountName)
                .toAccount(beneficiaryAccNumber)
                .toMember(bankCode)
                .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                .fromUserFullName(accountName)
                .fromAccountType(FromAccountType.PERSONAL.name())
                .internationalQuery(InternationalQuery.N.name())
                .build();

        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        return BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .currency(accNumberVerifyDTO.getAccountCurrency())
                .feeList(accNumberVerifyDTO.getFeeList().getLak())
                .referenceNumber(accNumberVerifyDTO.getReferenceNumber())
                .build();
    }

    private BeneficiaryInfoResponse internalBankMB(String beneficiaryAccNumber) {

        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .accountNumber(beneficiaryAccNumber)
                .build();

        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndStatus(
                beneficiaryAccNumber, EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null);

        return BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .referenceNumber(accNumberVerifyDTO.getReferenceNumber())
                .currency(Validator.isNotNull(creditAccount) ? creditAccount.getCurrency() : null)
                .build();
    }

    public OtpTransferTransResponse makeTransferToMerchant(TransactionDTO transactionDTO, String merchantCode,
                                                           Customer customer, boolean isSetDefaultAccount) {
        Merchant merchant = null;

        // xử lý cho umoney
        if (Validator.equals(merchantCode, MerchantCode.UMONEY.name())) {
            merchant = this.merchantRepository.findByMerchantCodeAndStatusAndOtherType(merchantCode, EntityStatus.ACTIVE.getStatus());

            // xử lý cho billing
        } else if (Validator.equals(MasterMerchantType.BILLING.name(), transactionDTO.getTransferType().name())) {
            //kiểm tra xem nó là merchant hay master merchant
            if (this.merchantRepository.existsByMerchantCodeAndStatusAndParentIdNotNull(merchantCode, EntityStatus.ACTIVE.getStatus())) {
                merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(merchantCode, EntityStatus.ACTIVE.getStatus());
            } else {
                merchant = this.merchantRepository.findByMerchantCodeAndServiceTypeAndStatus(merchantCode,
                        MasterMerchantType.BILLING.toString(), EntityStatus.ACTIVE.getStatus());
            }
        } else {
            // xử lý cho tài khoản merchant, không phải tài khoản master merchant
            merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(merchantCode, EntityStatus.ACTIVE.getStatus());
        }

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // kiem tra so tai khoan ghi no va ewallet
        Optional<MoneyAccount> moneyAccountOptional = this.moneyAccountRepository
                .findByAccountNumberAndCustomerIdAndStatus(transactionDTO.getCustomerAccNumber(), customer.getCustomerId(),
                        EntityStatus.ACTIVE.getStatus());

        if (!moneyAccountOptional.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG1038);
        }

        if (!Validator.equals(moneyAccountOptional.get().getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        } else {
            transactionDTO.setCustomerId(moneyAccountOptional.get().getCustomerId());

            if (Validator.isNotNull(moneyAccountOptional.get().getCurrency())) {
                transactionDTO.setTransactionCurrency(moneyAccountOptional.get().getCurrency());
            } else {
                transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
            }
        }
        transactionDTO.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());

        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        this.createTransactionMerchant(transferSaved.getTransferTransactionId(),
                merchant.getMerchantId(), transactionDTO.getMerchantType());

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (isSetDefaultAccount) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), transferSaved.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("request Otp, after request OTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .transactionId(transferSaved.getTransactionId())
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }

    private void processHistoryTransaction(TransactionHistoryDTO transactionHistoryDTO, List<Transaction> transactions,
                                           List<MoneyAccountDTO> moneyAccountDTOS, List<Bank> banks) {
        Optional<Transaction> optionalTransaction = transactions.stream()
                .filter(transaction -> Validator.equals(transaction.getTransactionCode(),
                        transactionHistoryDTO.getReferenceNumber()))
                .findFirst();

        optionalTransaction.ifPresent(transaction -> {
            transactionHistoryDTO.setToBankCode(transaction.getBankCode());
            transactionHistoryDTO.setTransferType(transaction.getTransactionType().name());

            // Neu la giao dich cong tien => enrich thong tin nguoi gui
            if (Validator.equals(transactionHistoryDTO.getAccountNumber(), transaction.getTarget())) {
                transactionHistoryDTO.setTransactionType(TransactionType.PLUS.name());
                transactionHistoryDTO.setFromAccount(transaction.getTarget());

                Optional<MoneyAccountDTO> moneyAccountDTOOptional = moneyAccountDTOS.stream()
                        .filter(account -> Validator.equals(transaction.getCustomerAccNumber(),
                                account.getAccountNumber()))
                        .findFirst();
                moneyAccountDTOOptional.ifPresent(
                        account -> transactionHistoryDTO.setFromAccountName(account.getCustomerName()));
            } else {
                transactionHistoryDTO.setTransactionType(TransactionType.MINUS.name());
                transactionHistoryDTO.setToBeneficiaryAccount(transaction.getTarget());

                Optional<MoneyAccountDTO> moneyAccountDTOOptional = moneyAccountDTOS.stream()
                        .filter(account -> Validator.equals(account.getAccountNumber(),
                                transaction.getTarget()))
                        .findFirst();

                moneyAccountDTOOptional.ifPresent(
                        account -> transactionHistoryDTO.setToBeneficiaryName(account.getCustomerName()));
            }

            Optional<Bank> bankTransaction = banks.stream()
                    .filter(bank -> Validator.equals(bank.getBankCode(), transactionHistoryDTO.getToBankCode()))
                    .findFirst();
            bankTransaction.ifPresent(bank -> transactionHistoryDTO.setToBankName(bank.getBankName()));
        });
    }

    /**
     * call API request OTP bên T24 và cập nhật transactionId cho giao dịch
     *
     * @param transactionDTO TransactionDTO
     * @return Transaction
     */
    private Transaction requestOtpT24(TransactionDTO transactionDTO) {

        if (transactionDTO.getTransactionId() != null) {
            transactionDTO.setTransactionId(transactionDTO.getTransactionId());
        } else {
            transactionDTO.setTransactionId(UUIDUtil.generateUUID(20));
        }

        if (!OtpConfirmType.DOTP.equals(transactionDTO.getOtpType())) {
            this.consumerOtpService.checkSpam(transactionDTO.getPhoneNumber(), OtpType.TRANSFER);

            ReqFundTransferDTO response = this.apiGeeTransferService.requestFundTransfer(
                    OtpTransferRequest.builder()
                            .accountNumber(transactionDTO.getCustomerAccNumber())
                            .accountType(AccountType.ACCOUNT.name())
                            .apiGeeTransactionId(transactionDTO.getTransactionId())
                            .build());
            transactionDTO.setTransactionId(response.getReferenceNumber());
        }

        this.consumerOtpService.requestOtp(transactionDTO.getTransactionId(),
                transactionDTO.getPhoneNumber(), OtpType.TRANSFER);
        Transaction transaction = this.transactionMapper.toEntity(transactionDTO);

        return this.transactionRepository.save_(transaction);

    }

    private void makeBillPayment(MBOtpTransConfirmResponse response, Optional<Telco> optionalTelco, TransactionDTO transactionDTO, TransactionMerchant transactionMerchant) {
        Telco telco = optionalTelco.orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1105));

        TopupUnitelResponse topupUnitelResponse = null;
        TopupEtlResponse topupEtlResponse = null;
        BestTelecomResponse bestTelecomResponse = null;
        String transactionId = "";
        PaymentInternetLtcResponse paymentInternetLtcResponse = null;
        PaymentPostPaidLtcResponse paymentPostPaidLtcResponse = null;
        PaymentPstnLtcResponse paymentPstnLtcResponse = null;
        String phoneUser = NumberUtil.formatPhoneNumber(this.getCustomerLogin().getUsername());
        String msisdn = NumberUtil.formatPhoneNumber(transactionDTO.getTarget());

        //LTC
        if (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.name()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.name())) {
            transactionId = RandomGenerator.generateRandomNumber(1, 23);

            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {

                PaymentPostpaidLtcRequest paymentLtcRequest = PaymentPostpaidLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type("POSTPAID")
                        .build();

                paymentLtcRequest.setApiGeeTransactionId(transactionId);
                paymentPostPaidLtcResponse = this.apiMmoneyService.paymentPostPaid(paymentLtcRequest);

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.INTERNET)) {

                PaymentInternetLtcRequest paymentInternetLtcRequest = PaymentInternetLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type(String.valueOf(transactionDTO.getBillingType()))
                        .build();

                paymentInternetLtcRequest.setApiGeeTransactionId(transactionId);
                paymentInternetLtcResponse = this.apiMmoneyService.paymentInternet(paymentInternetLtcRequest);

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.PSTN)) {

                PaymentPstnLtcRequest paymentPstnLtcRequest = PaymentPstnLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type(String.valueOf(transactionDTO.getBillingType()))
                        .build();

                paymentPstnLtcRequest.setApiGeeTransactionId(transactionId);
                paymentPstnLtcResponse = this.apiMmoneyService.paymentPstn(paymentPstnLtcRequest);
            }
        }
        //Unitel
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.UNITEL.name())) {
            transactionId = this.apiUnitelService.generateTransactionId();
            ApiUnitelRequest topupUnitelRequest = ApiUnitelRequest.builder()
                    .mti(consumerProperties.getApiUnitel().getMti())
                    .billingType(transactionDTO.getBillingType())
                    .transTime(InstantUtil.formatStringLongTimestamp(Instant.now(), ZoneId.of(StringPool.UTC)))
                    .transAmount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                    .systemTrace(transactionId)
                    .clientId(consumerProperties.getApiUnitel().getClientId())
                    .build();

            if (transactionDTO.getBillingType().equals(BillingType.INTERNET)) {
                topupUnitelRequest.setCustomerCode(transactionDTO.getTarget());
            } else {
                topupUnitelRequest.setMsisdn(transactionDTO.getTarget());
            }
            topupUnitelResponse = this.apiUnitelService.topup(topupUnitelRequest);
        }
        //Etl
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.ETL.name())) {
            transactionId = this.apiETLService.generateTransactionId();
            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {
                // thanh toán di động trả sau
                topupEtlResponse = this.apiETLService.paymentPostPaid(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.INTERNET)) {
                // thanh toán internet
                topupEtlResponse = this.apiETLService.topupInternetPrepaid(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.PSTN)) {
                // thanh toán di động cố định
                topupEtlResponse = this.apiETLService.paymentPSTN(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));
            }
            // Best TELECOM
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.BTC.name())) {
            transactionId = RandomGenerator.generateRandomNumber(8, 10);
            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {
                // thanh toán di động trả sau
                bestTelecomResponse = this.apiBestTelecomService.mobilePayment(this.bestTelecomPaymentRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, transactionId));
            }
        } else {
            // Nhà mạng khác
            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1170);
        }

        // save lai transaction Id phia nha mang de trace log
        transactionMerchant.setMerchantTransactionId(transactionId);
        transactionMerchantRepository.save(transactionMerchant);

        boolean isSuccess = Validator.isNotNull(paymentInternetLtcResponse) && paymentInternetLtcResponse.isSuccess()
                || Validator.isNotNull(paymentPostPaidLtcResponse) && paymentPostPaidLtcResponse.isSuccess()
                || Validator.isNotNull(paymentPstnLtcResponse) && paymentPstnLtcResponse.isSuccess();

        // Nếu Call API thất bại -> revert giao dịch
        if (!(isSuccess
                || Validator.isNotNull(topupUnitelResponse) && topupUnitelResponse.isSuccess()
                || Validator.isNotNull(topupEtlResponse) && topupEtlResponse.isSuccess()
                || Validator.isNotNull(bestTelecomResponse)
                && Validator.equals(this.consumerProperties.getApiBestTelecom().getSuccessPayment(), bestTelecomResponse.getValidity())
        )) {
            boolean isSuccessTrans = false;

            if (Validator.isNotNull(telco)
                    && (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.toString()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.toString()))) {
                isSuccessTrans = this.checkTransactionMmoney(transactionId, MmoneyServiceName.TELECOM.getServiceName());

                if (Validator.equals(false, isSuccessTrans)
                        || Validator.equals(Objects.requireNonNull(paymentPostPaidLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                        || Validator.equals(Objects.requireNonNull(paymentInternetLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                        || Validator.equals(Objects.requireNonNull(paymentPstnLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                ) {
                    this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                            .t24ReferenceNumber(response.getT24ReferenceNumber())
                            .transactionId(response.getReferenceNumber())
                            .build());

                    transactionDTO.setTransactionStatus(TransactionStatus.FAILED);

                    this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                    throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
                }
            }
        }
    }

    @Override
    public boolean checkTransactionMmoney(String transactionId, String serviceName) {
        CheckTransactionMmoneyResponse checkTransactionMmoneyResponse = this.apiMmoneyService.checkTransaction(
                CheckTransactionMmoneyRequest.builder()
                        .serviceName(serviceName)
                        .transactionId(transactionId)
                        .build()
        );

        if (!(Validator.isNotNull(checkTransactionMmoneyResponse) && checkTransactionMmoneyResponse.isSuccess())) {
            return false;
        }

        return true;
    }

    public MBOtpTransConfirmResponse confirmFundTransferMBCurrencyInternal(TransactionDTO transaction, String otp, String deviceId) {
        // get information customer
        Customer customer = getCustomerLogin();

        // debit account
        AccountMoney debitAccount = new AccountMoney();

        debitAccount.setAccountNumber(transaction.getCustomerAccNumber());
        debitAccount.setAccountName(customer.getFullname());
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        debitAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // credit account
        AccountMoney creditAccount = new AccountMoney();

        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.getType())) {
            creditAccount.setAccountType(this.mbApiConstantProperties.getOtpGen().getQrType());
            creditAccount.setAccountNumber(transaction.getQrCodeValue());
        } else {
            creditAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
            creditAccount.setAccountNumber(transaction.getBeneficiaryAccountNumber());
        }
        creditAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // amount
        Amount amount = new Amount();

        double amountTransaction = this.getAmountTransactionCurrencyInternal(transaction);

        amount.setAmount(StringUtil.formatNumberDoubleValue(amountTransaction));
        amount.setCurrency(transaction.getTransactionCurrency());

        String userId = customer.getCif() + customer.getIdCardNumber();
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(otp)
                .type(transaction.getOtpType().name() != null ? transaction.getOtpType() : OtpConfirmType.SMS)
                .userId(userId)
                .transData(MD5Generator.md5(transaction.getTransData()))
                .deviceId(deviceId)
                .build();
        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            authenMethod.setToken(dotpService.getCustomerDevice(deviceId).getToken());
        }
        // create request send to t24 system
        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp)
                .transferType(transaction.getT24TransferType())
                .channel(transaction.getT24Channel())
                .serviceType(transaction.getT24ServiceType())
                .requestId(transaction.getTransactionId())
                .remark(transaction.getMessage())
                .branchCode(transaction.getBranchCode())
                .creditAccount(creditAccount)
                .debitAccount(debitAccount)
                .amount(amount)
                .authenMethod(authenMethod)
                .build();

        this.enrichVerifyTransferRequestCurrencyForeign(otpVerifyTransferRequest, transaction);

        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                this.apiGeeTransferService::verifyFundTransfer,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber())
                .referenceNumber(reqFundTransferDTO.getReferenceNumber())
                .transactionStatus(reqFundTransferDTO.getTransactionStatus())
                .transactionId(reqFundTransferDTO.getTransactionId())
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode())
                .build();
    }

    private TelcoType getTelcoByTransactionHistory(Transaction transaction) {
        TransactionMerchant transactionMerchant = transactionMerchantRepository.findByTransferTransactionId(transaction.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100010));

        // FIX TH: merchant bị xoá trước đấy không hiển thị được lịch sử
        Merchant merchant = this.merchantRepository.getOne(transactionMerchant.getMerchantId());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        Telco telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        return TelcoType.valueOf(telco.getTelcoCode());
    }

    private TelcoType getTelcoByTransaction(Transaction transaction) {

        TransactionMerchant transactionMerchant = transactionMerchantRepository.findByTransferTransactionId(transaction.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100010));

        Merchant merchant = this.merchantRepository.findByMerchantIdAndStatus(transactionMerchant.getMerchantId(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        Telco telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        return TelcoType.valueOf(telco.getTelcoCode());

    }

    private ETLPaymentTopupWSRequest etlPaymentTopupWSRequest(String phoneNumber, String amount, String
            transactionId) {
        return ETLPaymentTopupWSRequest.builder()
                .msisdn(phoneNumber)
                .transactionId(transactionId)
                .dateTimeProcess(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_PATTERN)))
                .amount(amount)
                .build();
    }

//    private void enrichVerifyTransferRequestForBilling(OtpVerifyTransferRequest otpVerifyTransferRequest,
//                                                       TransactionDTO transaction) {
//        // Nghiệp vụ luồng Billing:
//        // Tiền phí sẽ chuyển thẳng vào tài khoản merchent cùng với tiền hóa đơn rồi sau đó 2 bên sẽ đối soát tiền phí sau
//        // Discount cũng sẽ trừ thẳng vào amount
//        otpVerifyTransferRequest.getAmount().setAmount(StringUtil.valueOf(transaction.getActualTransactionAmount()));
//        otpVerifyTransferRequest.getChargeInfo().setAmount(StringUtil.valueOf(0));
//
//    }

    private ValidateMobileNumberRequest bestTelecomRequest(String phoneNumber) {
        return ValidateMobileNumberRequest.builder()
                .phoneNumber(phoneNumber)
                .displayAddon(this.consumerProperties.getApiBestTelecom().getBillingCode())
                .build();
    }

    private MobilePaymentRequest bestTelecomPaymentRequest(String phoneNumber, Long amount, String transactionId) {
        return MobilePaymentRequest.builder()
                .phoneNumber(phoneNumber)
                .amount(amount)
                .transId(transactionId)
                .serviceType(this.consumerProperties.getApiBestTelecom().getBillingType())
                .build();
    }

    private double getAmountTransactionCurrencyInternal(TransactionDTO transaction) {
        double transactionAmount = transaction.getTransactionAmount();
        long discountFixed = Validator.isNull(transaction.getDiscountFixed()) ? 0L : transaction.getDiscountFixed();

        transactionAmount = transactionAmount - discountFixed - transactionAmount * transaction.getDiscount() / 100;

        return transactionAmount;
    }

    private void enrichVerifyTransferRequestCurrencyForeign(OtpVerifyTransferRequest otpVerifyTransferRequest,
                                                            TransactionDTO transaction) {
        List<AddInfoList> addInfoList = new ArrayList<>();

        AddInfoList benAcc = new AddInfoList();

        benAcc.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNo());
        benAcc.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());
        benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());

        AddInfoList benCustomer = new AddInfoList();

        benCustomer.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforName());
        benCustomer.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());

        // @TODO khách hàng confirm với tài khoản <= 11 ký tự phải cộng thêm chuỗi ở đằng trước
        int customerAccountLength = transaction.getCustomerAccNumber().length();

        if (customerAccountLength <= Integer.parseInt(StringPool.MIN_CUSTOMER_ACCOUNT_LENGTH)) {
            int endLength = Integer.parseInt(StringPool.CUSTOMER_ACCOUNT_LENGTH) - customerAccountLength;
            benCustomer.setValue(UUIDUtil.generateRandomAlpha(endLength) + transaction.getCustomerAccNumber());
        } else {
            benCustomer.setValue(transaction.getCustomerAccNumber());
        }

        addInfoList.add(benAcc);
        addInfoList.add(benCustomer);

        // enrich chargeInfo
        ChargeInfo chargeInfo = new ChargeInfo();

        chargeInfo.setAmount(Validator.equals(String.valueOf(transaction.getTransactionFee()), StringPool.NUMBER_DOUBLE_0) ? StringPool.NUMBER_0 : String.valueOf(transaction.getTransactionFee()));
        chargeInfo.setCurrency(transaction.getTransactionCurrency());
        chargeInfo.setDescription(transaction.getMessage());

        otpVerifyTransferRequest.setAddInfoList(addInfoList);
        otpVerifyTransferRequest.setChargeInfo(chargeInfo);

    }

    private void checkTransactionLimitCustomerAndValidateMoneyAccount(MoneyAccount moneyAccountOfCustomer, OtpTransferTransRequest request, Customer customer,
                                                                      FeeTransactionResponse feeTransactionResponse, MoneyAccount moneyAccountOfBenefit) {
        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        }

        if (!Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getCustomerCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        if (Validator.isNotNull(moneyAccountOfBenefit) && !Validator.equals(moneyAccountOfBenefit.getCurrency(), request.getBeneficiaryCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        try {
            if (Validator.equals(moneyAccountOfCustomer.getCurrency(), customerProperties.getDefaultCurrency())) {
                Long amount = request.getAmount().longValue();
            }
        } catch (Exception e) {
            throw new BadRequestAlertException(ErrorCode.MSG101239);
        }

        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && (!Validator.equals(request.getBeneficiaryCurrency(), customerProperties.getDefaultCurrency())
                || !Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency()))
                && (!Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getBeneficiaryCurrency())
                || !Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getCustomerCurrency()))) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(moneyAccountOfCustomer.getCurrency(), "USD")
                && moneyAccountOfCustomer.getAvailableAmount() < this.customerProperties.getTransactionLimitUsd()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                            new Object[]{this.customerProperties.getTransactionLimitUsd(),
                                    moneyAccountOfCustomer.getCurrency()}),
                    Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
        } else if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(moneyAccountOfCustomer.getCurrency(), "THB")
                && moneyAccountOfCustomer.getAvailableAmount() < this.customerProperties.getTransactionLimitThb()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                            new Object[]{this.customerProperties.getTransactionLimitThb(),
                                    moneyAccountOfCustomer.getCurrency()}),
                    Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
        }

        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && !Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency())
                && !Arrays.asList(Sector.SECTOR_1740.getId(), Sector.SECTOR_1890.getId()).contains(customer.getCustomerSectorId())) {
            throw new BadRequestAlertException(ErrorCode.MSG101217);
        }

        if (Validator.isNotNull(feeTransactionResponse)) {
            double totalAmount = moneyAccountOfCustomer.getAvailableAmount() - (request.getAmount() + feeTransactionResponse.getFee());
            if (Validator.equals(moneyAccountOfCustomer.getCurrency(), "USD") && totalAmount < this.customerProperties.getTransactionLimitUsd()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitUsd(),
                                        moneyAccountOfCustomer.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            } else if (Validator.equals(moneyAccountOfCustomer.getCurrency(), "THB") && totalAmount < this.customerProperties.getTransactionLimitThb()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitThb(),
                                        moneyAccountOfCustomer.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            }
        }
    }

    private void validateDebitTransferUmoney(DebitMoneyRequest request) {
        if (Validator.isNull(request.getDebitAccount().getAccountName())
                || Validator.isNull(request.getDebitAccount().getAccountNumber())
                || Validator.isNull(request.getDebitAccount().getAccountType())
                || Validator.isNull(request.getCreditAccount().getAccountName())
                || Validator.isNull(request.getCreditAccount().getAccountNumber())
                || Validator.isNull(request.getCreditAccount().getAccountType())) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (!request.getDebitAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || !request.getCreditAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || (Validator.isNotNull(request.getFromMember())
                && !request.getFromMember().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getChargeInfo().getDescription())
                && !request.getChargeInfo().getDescription().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getRemark())
                && !request.getRemark().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.isNotNull(request.getAmount().getAmount()) && Double.parseDouble(request.getAmount().getAmount()) < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.equals(request.getDebitAccount().getAccountNumber(), request.getCreditAccount().getAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        }
    }

    private void validateDepositTransferUmoney(DepositMoneyRequest request) {
        if (Validator.isNull(request.getDebitAccount().getAccountName())
                || Validator.isNull(request.getDebitAccount().getAccountNumber())
                || Validator.isNull(request.getDebitAccount().getAccountType())
                || Validator.isNull(request.getCreditAccount().getAccountName())
                || Validator.isNull(request.getCreditAccount().getAccountNumber())
                || Validator.isNull(request.getCreditAccount().getAccountType())) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.isNotNull(request.getAmount().getAmount()) && Double.parseDouble(request.getAmount().getAmount()) < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (!request.getDebitAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || !request.getCreditAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || (Validator.isNotNull(request.getFromMember())
                && !request.getFromMember().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getChargeInfo().getDescription())
                && !request.getChargeInfo().getDescription().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getRemark())
                && !request.getRemark().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.equals(request.getDebitAccount().getAccountNumber(), request.getCreditAccount().getAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        }
    }

}
