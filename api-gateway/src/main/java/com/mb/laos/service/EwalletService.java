package com.mb.laos.service;

import com.mb.laos.api.request.TransferAccountRequest;
import com.mb.laos.api.response.TransferAccountResponse;
import com.mb.laos.api.response.VerifyEwalletAccountResponse;
import com.mb.laos.api.response.VerifyUmoneyAccountResponse;

public interface EwalletService {
    VerifyUmoneyAccountResponse verifyCashAccount(String phoneNumber);

    TransferAccountResponse transferUmoney(TransferAccountRequest request);

    VerifyEwalletAccountResponse verifyCashAccountPartner(String accountNumber, String partnerId);

    TransferAccountResponse transferAccountPartner(TransferAccountRequest request);
}

