package com.mb.laos.service.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.Resource;
import com.mb.laos.model.FileEntry;
import com.mb.laos.model.FileExtra;
import com.mb.laos.model.ImageLoginApp;
import com.mb.laos.model.dto.ImageLoginAppDTO;
import com.mb.laos.repository.ImageLoginAppRepository;
import com.mb.laos.security.configuration.AuthenticationProperties;
import com.mb.laos.service.ImageLoginAppService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.ImageLoginAppMapper;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Transactional
@RequiredArgsConstructor
public class ImageLoginAppServiceImpl implements ImageLoginAppService {

    private final ImageLoginAppRepository imageLoginAppRepository;

    private final ImageLoginAppMapper imageLoginAppMapper;

    private final StorageService storageService;

    private final AuthenticationProperties properties;

    @Override
    public ResponseEntity<InputStreamResource> getIcon() {
        try {
            ImageLoginApp imageLoginApp = this.imageLoginAppRepository.findAllByStatus(EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null);

            if (Validator.isNull(imageLoginApp)) {
                return null;
            }

            List<FileEntry> fileEntries = this.storageService.getFileEntries(ImageLoginApp.class.getName(), imageLoginApp.getImageLoginAppId(), Resource.ICON);

            if (Validator.isNull(fileEntries)) {
                return null;
            }

            FileEntry fileEntry = fileEntries.stream().findFirst().get();

            FileExtra file = this.storageService.getFileExtra(fileEntry.getFileId(), fileEntry.getNormalizeName(),
                    ImageLoginApp.class.getName(), fileEntry.getClassPk(), Resource.ICON);

            return ResponseEntity.ok().contentType(MediaType.parseMediaType(file.getContentType()))
                    .cacheControl(CacheControl.maxAge(this.properties.getCacheMaxAge(), TimeUnit.SECONDS))
                    .body(new InputStreamResource(file.getInputStream()));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public ImageLoginAppDTO getInfoImageLogin() {
        ImageLoginAppDTO imageLoginAppDTO = this.imageLoginAppMapper.toDto(this.imageLoginAppRepository.findAllByStatus(EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null));

        if (Validator.isNull(imageLoginAppDTO)) {
            return null;
        }

        List<FileEntry> fileEntries = storageService.getFileEntries(ImageLoginApp.class.getName(), imageLoginAppDTO.getImageLoginAppId(), Resource.ICON);

        if (Validator.isNotNull(fileEntries)) {
            fileEntries.stream().findFirst().ifPresent(item -> imageLoginAppDTO.setIcon(storageService.getFileInfo(item)));
        }

        return imageLoginAppDTO;
    }

}
