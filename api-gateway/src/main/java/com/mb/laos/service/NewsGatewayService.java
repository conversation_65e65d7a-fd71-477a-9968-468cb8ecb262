package com.mb.laos.service;

import com.mb.laos.model.dto.NewsDTO;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.model.search.NewsSearch;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

public interface NewsGatewayService {

    /**
     * tìm kiếm tin tuc theo keyword
     *
     * @param newsSearch
     * @return
     */
    Page<NewsDTO> search(NewsSearch newsSearch, Pageable pageable);

    /**
     * chi tiết
     *
     * @param newsId
     * @return
     */
    NewsDTO detail(Long newsId);

    /**
     * getIcon
     *
     * @param search FileEntrySearch
     * @return ResponseEntity<InputStreamResource>
     */
    ResponseEntity<InputStreamResource> getIcon(FileEntrySearch search);

}
