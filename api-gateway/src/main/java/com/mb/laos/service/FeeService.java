package com.mb.laos.service;

import com.mb.laos.gateway.request.FeeTransactionRequest;
import com.mb.laos.gateway.response.FeeTransactionResponse;
import com.mb.laos.model.dto.FeeDTO;
import com.mb.laos.request.FeeCreateOrUpdateRequest;

public interface FeeService {
    /**
     * get phi theo loại giao dịch và theo giá trị giao dịch
     *
     * @param request: FeeTransactionRequest
     * @return: FeeTransactionResponse
     */
    FeeTransactionResponse getFeeOfTransaction(FeeTransactionRequest request);

    /**
     * get phi theo loại giao dịch và theo giá trị giao dịch
     *
     * @param request: FeeTransactionRequest
     * @return: FeeTransactionResponse
     */
    FeeTransactionResponse getFeeOfTransactionV2(FeeTransactionRequest request);

    FeeDTO create(FeeCreateOrUpdateRequest request);
}
