package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.AccountType;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.BranchCode;
import com.mb.laos.enums.ConfigurationFeeType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.gateway.request.OtpBillingRequest;
import com.mb.laos.gateway.request.OtpBillingV2Request;
import com.mb.laos.gateway.request.OtpCashInRequest;
import com.mb.laos.gateway.request.OtpPremiumAccNumberRequest;
import com.mb.laos.gateway.request.OtpQrBillRequest;
import com.mb.laos.gateway.request.OtpSavingAccountRequest;
import com.mb.laos.gateway.request.OtpTopupRequest;
import com.mb.laos.gateway.request.OtpTransferInsuranceRequest;
import com.mb.laos.gateway.request.OtpTransferTransRequest;
import com.mb.laos.gateway.request.OtpUtilityRequest;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Merchant;
import com.mb.laos.request.SmsBalanceRequest;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 5345824526030526378L;

    private Long transferTransactionId;

    private TransferType transferType;

    private Long customerId;

    private String customerAccNumber;

    private Long beneficiaryCustomerId;

    private String beneficiaryAccountNumber;

    private String bankCode;

    private String transactionId;

    private Double transactionAmount;

    private LocalDate payDate;

    private String message;

    private TransactionStatus transactionStatus;

    private String failureCause;

    private double transactionFee;

    private double discount;

    private long discountFixed;

    private ConfigurationFeeType configurationFeeType;

    private Instant transactionStartTime;

    private Instant transactionFinishTime;

    private String transactionCurrency;

    private String target;

    private String phoneNumber;

    private String branchCode;

    private Integer status;

    private String description;

    private TransactionType transactionType;

    private String beneficiaryCustomerName;

    private String beneficiaryCurrency;

    private String transactionCode;

    private Integer merchantType;

    private BillingType billingType;

    // các giá trị mặc định khi chuyển tiền T24
    private String t24TransferType;

    private String t24Channel;

    private String t24ServiceType;

    private String extraTransactionCode; // mã giao dịch trả về từ các bên như: Umoney, LTC

    private TransferTransactionType type;

    private String qrCodeValue;

    private OtpConfirmType otpType;

    private AccountType accountType;

    private String bankName;

    private Merchant merchant;

    private String transactionStartTimeStr;

    private String transactionFinishTimeStr;

    private double actualTransactionAmount; // chỉ dùng cho thông báo

    private SavingAccountType savingAccountType;

    private String clientMessageId;

    private Double totalAmount;

    private double foreignAmount;

    private String nation;

    public String formatTransactionFinishTime() {
        return InstantUtil.formatStringLongDate(this.transactionFinishTime, Labels.getDefaultZoneId());
    }

    public TransactionDTO(OtpTransferTransRequest request) {
        this.customerAccNumber = request.getCustomerAccNumber();
        this.target = Validator.equals(request.getType(), AccountType.ACCOUNT) ? request.getBeneficiaryAccountNumber() : request.getBeneficiaryQrValue();
        this.transactionCurrency = request.getCustomerCurrency();
        this.transferType = TransferType.TRANSFER_MONEY;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.bankCode = request.getBeneficiaryBankCode();
        this.message = request.getRemark();
        this.transactionAmount = request.getAmount();
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.beneficiaryCustomerName = request.getBeneficiaryCustomerName();
        this.qrCodeValue = request.getBeneficiaryQrValue();
        this.otpType = request.getOtpConfirmType();
        this.accountType = request.getType();
        this.beneficiaryAccountNumber = Validator.equals(request.getType(), AccountType.ACCOUNT) ? request.getBeneficiaryAccountNumber() : null;
//        this.type = request.getType();
    }

    public TransactionDTO(SmsBalanceRequest request) {
        this.status = EntityStatus.ACTIVE.getStatus();
        this.target = request.getCustomerAccountNumber();
        this.customerAccNumber = request.getCustomerAccountNumber();
        this.transactionCurrency = request.getCurrency();
        this.transactionStartTime = Instant.now();
        this.transactionType = TransactionType.MINUS;
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.transactionId = UUIDUtil.generateUUID(20);
        this.otpType = OtpConfirmType.SMS;
        this.branchCode = BranchCode.LA0010001.name();
    }

    public TransactionDTO(OtpPremiumAccNumberRequest request) {
        this.transferType = TransferType.PREMIUM_ACCOUNT_NUMBER;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.target = request.getPremiumAccNumber();
        this.transactionCurrency = request.getCurrency();
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.customerAccNumber = request.getPaymentAccNumber();
        this.transactionId = UUIDUtil.generateUUID(20);
        this.transactionFee = Validator.isNotNull(request.getFee()) ? request.getFee() : 0;
    }

    public TransactionDTO(OtpTransferInsuranceRequest request) {
        this.customerAccNumber = request.getCustomerAccNumber();
        this.target = request.getBeneficiaryAccountNumber();
        this.beneficiaryAccountNumber = request.getBeneficiaryAccountNumber();
        this.transactionCurrency = request.getCustomerCurrency();
        this.transferType = TransferType.INSURANCE;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.bankCode = request.getBeneficiaryBankCode();
        this.message = request.getRemark();
        this.transactionAmount = Validator.isNotNull(request.getAmount()) ? request.getAmount().doubleValue() : 0D;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.beneficiaryCustomerName = request.getBeneficiaryCustomerName();
        this.otpType = request.getOtpConfirmType();
        this.accountType = request.getType();
//        this.type = request.getType();
    }


    public TransactionDTO(OtpCashInRequest request, String umoneyAccountName) {
        this.customerAccNumber = request.getAccountNumber();
        this.transferType = TransferType.CASH_IN;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.transactionAmount = Validator.isNotNull(request.getAmount()) ? request.getAmount().doubleValue() : 0D;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.target = request.getEwalletAccountNumber();
        this.beneficiaryCustomerName = umoneyAccountName;
        this.otpType = request.getOtpConfirmType();
    }

    public TransactionDTO(OtpTopupRequest request, TelcoCardDTO telcoCardDTO) {
        this.customerAccNumber = request.getCustomerAccount();
        this.transferType = TransferType.TOPUP;
        this.target = request.getPhoneNumber();
        this.transactionAmount = Validator.isNotNull(telcoCardDTO.getDenomination()) ? (double) telcoCardDTO.getDenomination() : 0D;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.otpType = request.getOtpConfirmType();
        this.merchantType = BillingType.TOPUP.getType();
//        this.transactionFee = (double) telcoCardDTO.getDenomination() * (double) telcoCardDTO.getDiscount() / 100;
    }

    public TransactionDTO(OtpQrBillRequest request, String merchantId, String merchantName) {
        this.customerAccNumber = request.getCustomerAccount();
        this.transferType = TransferType.BILLING;
        this.target = merchantId;
        this.transactionAmount = Validator.isNotNull(request.getAmount()) ? request.getAmount().doubleValue() : 0D;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.otpType = request.getOtpConfirmType();
        this.beneficiaryCustomerName = merchantName;
    }

    public TransactionDTO(OtpBillingRequest request) {
        this.customerAccNumber = request.getAccountNumber();
        this.transferType = TransferType.BILLING;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.beneficiaryAccountNumber = request.getBillCode();
        this.merchantType = request.getType();
        this.target = request.getBillCode();
        this.otpType = request.getOtpConfirmType();
    }

    public TransactionDTO(OtpBillingV2Request request) {
        this.customerAccNumber = request.getAccountNumber();
        this.transferType = TransferType.BILLING;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.merchantType = request.getBillingType();
        this.target = request.getBillCode();
        this.otpType = request.getOtpConfirmType();
        this.billingType = BillingType.getBillingType(request.getBillingType());
    }

    public TransactionDTO(OtpUtilityRequest request) {
        this.customerAccNumber = request.getAccountNumber();
        this.transferType = TransferType.BILLING;
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.transactionType = TransactionType.MINUS;
        this.merchantType = request.getBillingType();
        this.target = request.getBillCode();
        this.otpType = request.getOtpConfirmType();
        this.billingType = BillingType.getBillingType(request.getBillingType());
    }


    public TransactionDTO(OtpSavingAccountRequest request) {
        this.transferType = TransferType.SAVING_ACCOUNT;
        this.target = request.getSourceAccount();
        this.transactionCurrency = request.getAmountCurrency();
        this.transactionStartTime = Instant.now();
        this.transactionStatus = TransactionStatus.PROCESSING;
        this.status = EntityStatus.ACTIVE.getStatus();
        this.otpType = OtpConfirmType.DOTP;
        this.branchCode = BranchCode.LA0010001.name();
        this.transactionFee = 0;
        this.savingAccountType = request.getSavingAccountType();
    }

    public String getTransData() {
        if (transferType == TransferType.TRANSFER_MONEY || transferType == TransferType.INSURANCE) {
            if (Validator.equals(AccountType.QR, accountType) || TransferTransactionType.getQrTransaction().contains(type)) {
                return String.format("%s%s%s", customerAccNumber, qrCodeValue, transactionAmount.toString());
            }
            return String.format("%s%s%s", customerAccNumber, target, transactionAmount.toString());
        } else if (transferType == TransferType.BILLING || transferType == TransferType.CASH_IN || transferType == TransferType.TOPUP) {
            return String.format("%s%s%s", customerAccNumber, beneficiaryAccountNumber, transactionAmount.toString());
        } else {
            return "";
        }
    }
}
