package com.mb.laos.model;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.mb.laos.enums.SaveStatus;
import com.mb.laos.enums.TransferTransactionType;
import org.hibernate.annotations.Type;
import org.hibernate.search.annotations.Analyze;
import org.hibernate.search.annotations.Analyzer;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Fields;
import org.hibernate.search.annotations.Index;
import org.hibernate.search.annotations.Indexed;
import org.hibernate.search.annotations.Normalizer;
import org.hibernate.search.annotations.SortableField;
import org.hibernate.search.annotations.Store;
import org.hibernate.search.annotations.TermVector;
import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Indexed
@Entity
@Table(name = "T_BENEFICIARY")
@Data
@EqualsAndHashCode(callSuper = false)
public class Beneficiary extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7020271006196714571L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BENEFICIARY_SEQ")
    @SequenceGenerator(sequenceName = "BENEFICIARY_SEQ", name = "BENEFICIARY_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "BENEFICIARY_ID")
    private long beneficiaryId;

    @Field
    @Column(name = "CUSTOMER_ID")
    private long customerId;

    @Fields({
                    @Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES,
                                    analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM),
                                    store = Store.YES),
                    @Field(name = SortName.BENEFICIARY_CUS_NAME_SORT, termVector = TermVector.YES, index = Index.YES,
                                    analyze = Analyze.NO,
                                    normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE),
                                    store = Store.YES)})
    @SortableField(forField = SortName.BENEFICIARY_CUS_NAME_SORT)
    @Column(name = "BENEFICIARY_CUSTOMER_NAME")
    private String beneficiaryCustomerName;

    @Column(name = "CUSTOMER_ACCOUNT_NUMBER")
    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String customerAccNumber;

    @Fields({
                    @Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES,
                                    analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM),
                                    store = Store.YES),
                    @Field(name = SortName.BENEFICIARY_ACCOUNT_SORT, termVector = TermVector.YES, index = Index.YES,
                                    analyze = Analyze.NO,
                                    normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE),
                                    store = Store.YES)})
    @SortableField(forField = SortName.BENEFICIARY_ACCOUNT_SORT)
    @Column(name = "BENEFICIARY_ACCOUNT_NUMBER")
    private String beneficiaryAccountNumber;

    @Column(name = "BENEFICIARY_BANK_CODE")
    private String beneficiaryBankCode;

    @Column(name = "BENEFICIARY_BANK_NAME")
    private String beneficiaryBankName;

    @Column(name = "BENEFICIARY_BANK_ID")
    private long beneficiaryBankId;

    @Fields({
                    @Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES,
                                    analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM),
                                    store = Store.YES),
                    @Field(name = SortName.REMINISCENT_NAME_SORT, termVector = TermVector.YES, index = Index.YES,
                                    analyze = Analyze.NO,
                                    normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE),
                                    store = Store.YES)})
    @SortableField(forField = SortName.REMINISCENT_NAME_SORT)
    @Column(name = "REMINISCENT_NAME")
    private String reminiscentName;

    @Column(name = "TYPE")
    private TransferTransactionType type;

    @Field
    @Column(name = "STATUS")
    private int status;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "TRANSFER_TRANSACTION_ID")
    private long transferTransactionId;

    @Column(name = "IS_SAVED")
    private SaveStatus isSaved;

    @Fields({
                    @Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, store = Store.YES),
                    @Field(name = SortName.LAST_MODIFIED_DATE_SORT, index = Index.YES, analyze = Analyze.NO,
                                    store = Store.YES)})
    @SortableField(forField = SortName.LAST_MODIFIED_DATE_SORT)
    @Column(name = "LAST_MODIFIED_DATE", updatable = false, insertable = false)
    private LocalDateTime lastModifiedDateTime;

    public interface FieldName {
        public static final String BENEFICIARY_CUSTOMER_NAME = "beneficiaryCustomerName";
        
        public static final String BENEFICIARY_ACCOUNT_NUMBER = "beneficiaryAccountNumber";
        
        public static final String REMINISCENT_NAME = "reminiscentName";
        
        public static final String STATUS = "status";
        
        public static final String CUSTOMER_ID = "customerId";
    }
    
    public interface SortName {
        public static final String BENEFICIARY_CUS_NAME_SORT = "beneficiaryCusNameSort";
        
        public static final String BENEFICIARY_ACCOUNT_SORT = "beneficiaryAccountSort";
        
        public static final String REMINISCENT_NAME_SORT = "reminiscentNameSort";
        
        public static final String LAST_MODIFIED_DATE_SORT = "lastModifiedDateSort";
    }
}
