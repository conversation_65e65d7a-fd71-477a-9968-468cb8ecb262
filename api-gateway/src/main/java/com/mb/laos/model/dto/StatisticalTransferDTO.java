package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.TransferType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StatisticalTransferDTO implements Serializable {

    private static final long serialVersionUID = -7114557376841607431L;

    private Double totalTransfer;

    private Double totalBilling;

    private Double totalSavingAccount;

    private Double totalPremiumAccountNumber;

    private Double total;

    private TransferType transferType;

    private String text;

    private List<StatisticalDTO> statistical;

    private String currency;
}
