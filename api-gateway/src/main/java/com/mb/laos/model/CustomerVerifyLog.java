
/**
 * 
 */
package com.mb.laos.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import org.hibernate.annotations.Type;
import com.mb.laos.enums.VerifyType;
import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "CUSTOMER_VERIFY_LOG")
public class CustomerVerifyLog extends AbstractAuditingEntity {
	/**
	* 
	*/
	private static final long serialVersionUID = -963941409881038524L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_VERIFY_LOG_SEQ")
	@SequenceGenerator(sequenceName = "CUSTOMER_VERIFY_LOG_SEQ", name = "CUSTOMER_VERIFY_LOG_SEQ",
			initialValue = 1, allocationSize = 1)
	@Column(name = "CUSTOMER_VERIFY_LOG_ID", length = 19)
	private long customerVerifyLogId;

	@Column(name = "PHONE_NUMBER", length = 20)
	@Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
	private String phoneNumber;

	@Column(name = "TYPE", length = 20)
	@Enumerated(EnumType.STRING)
	private VerifyType type;

	@Column(name = "SUCCESS", nullable = false)
	private boolean success;
}
