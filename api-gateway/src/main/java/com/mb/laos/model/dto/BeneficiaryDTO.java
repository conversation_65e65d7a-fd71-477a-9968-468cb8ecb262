package com.mb.laos.model.dto;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.request.BeneficiaryRequest;
import com.mb.laos.util.Validator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeneficiaryDTO implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    private Long beneficiaryId;

    @Exclude
    private Long customerId;

    private String beneficiaryCustomerName;

    private Long beneficiaryCustomerId;

    @Exclude
    private String customerAccNumber;

    @Exclude
    private String beneficiaryAccountNumber;

    private String beneficiaryBankCode;

    private String beneficiaryBankName;

    private Long beneficiaryBankId;

    private String reminiscentName;

    private Integer status;

    private TransferTransactionType type;

    private String description;

    private DocumentDTO icon;

    private String transactionCurrency;

    private String beneficiaryCurrency;

    private String nationCode;

    private TransferType transferType;

    private String bankCode;

    private Long transferTransactionId;

    public BeneficiaryDTO(TransactionDTO request) {
        this.beneficiaryAccountNumber = request.getTarget();
        this.customerId = request.getCustomerId();
        this.beneficiaryCustomerId = request.getBeneficiaryCustomerId();
        this.customerAccNumber = request.getCustomerAccNumber();
        this.beneficiaryBankCode = request.getBankCode();
        this.status = EntityStatus.ACTIVE.getStatus();
        this.beneficiaryCustomerName = request.getBeneficiaryCustomerName();
        this.reminiscentName = request.getBeneficiaryCustomerName();
        this.type = request.getType();
        this.transferTransactionId = Validator.isNotNull(request.getTransferTransactionId()) ? request.getTransferTransactionId() : null;
    }

    public void delete() {
        this.status = EntityStatus.DELETED.getStatus();
    }

    public void update(BeneficiaryRequest request) {
        this.reminiscentName = request.getReminiscentName();
    }
}
