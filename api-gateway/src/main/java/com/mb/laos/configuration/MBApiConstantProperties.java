package com.mb.laos.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "application.constants.mb-api")
public class MBApiConstantProperties {

    private OtpGenApi otpGen;

    private InBanKTransferApi inBanKTransfer;

    private CashInTransferApi cashInTransferApi;

    private LapnetTransfer lapnetTransfer;

    private TopupTransfer topupTransfer;

    private QrPayment qrPayment;

    private BillingPayment billingPayment;

    private InsurancePayment insurancePayment;

    private Cashout cashout;

    private DepositDebit depositDebit;

    @Getter
    @Setter
    public static class OtpGenApi {
        private String accountType;
        private String qrType;
    }

    @Getter
    @Setter
    public static class InBanKTransferApi {
        private String transferType;

        private String accountType;

        private String channel;

        private String serviceType;

        private String serviceTypeQr;

        private String currency;
    }

    @Getter
    @Setter
    public static class CashInTransferApi {
        private String transferType;

        private String accountType;

        private String channel;

        private String serviceType;

        private String currency;

        private String defaultUmoneyAccount;
    }

    @Getter
    @Setter
    public static class LapnetTransfer {
        private String transferType;

        private String serviceType;

        private String channel;

        private String addInforNo;

        private String addInforName;

        private String addInforType;

        private String addInforNoValue;

        private Integer feeFreeNumberTransactionInMonth;

        private Long feeFreeTotalAmountInMonth;
    }

    @Getter
    @Setter
    public static class TopupTransfer {
        private String transferType;

        private String serviceType;

        private String channel;

        private String defaultMerchantAccount;
    }

    @Getter
    @Setter
    public static class QrPayment {
        private String transferType;

        private String serviceType;

        private String channel;
    }

    @Getter
    @Setter
    public static class BillingPayment {
        private String transferType;

        private String serviceType;

        private String channel;
    }

    @Getter
    @Setter
    public static class InsurancePayment {
        private String transferType;

        private String serviceType;

        private String channel;
    }

    @Getter
    @Setter
    public static class Cashout {
        private String transferType;

        private String serviceType;

        private String channelLapnet;

        private List<String> mbCodes;
    }

    @Getter
    @Setter
    public static class DepositDebit {
        private String transferType;

        private String channel;

        private String serviceTypeDebit;

        private String serviceTypeDeposit;
    }

}
