package com.mb.laos.repository;


import com.mb.laos.cache.util.OtpCacheConstants;
import com.mb.laos.model.IntlPaymentCacheDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;

public interface IntlPaymentCacheRepository {
    @CachePut(cacheNames = OtpCacheConstants.Others.INTERNATIONAL_PAYMENT, key = "#key", unless = "#result == null")
    default IntlPaymentCacheDTO put(String key, IntlPaymentCacheDTO otp) {
        return otp;
    }

    @Cacheable(cacheNames = OtpCacheConstants.Others.INTERNATIONAL_PAYMENT, key = "#key", unless = "#result == null")
    default IntlPaymentCacheDTO get(String key) {
        return null;
    }

    @CacheEvict(cacheNames = OtpCacheConstants.Others.INTERNATIONAL_PAYMENT, key = "#key")
    default String evict(String key) {
        return key;
    }
}
