package com.mb.laos.security.configuration;

import java.util.Arrays;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;
import org.springframework.security.web.session.SessionManagementFilter;
import org.springframework.web.filter.CorsFilter;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;

import com.mb.laos.api.handler.FilterChainExceptionHandler;
import com.mb.laos.security.UserPrincipal;
import com.mb.laos.security.jwt.JWTConfigurer;
import com.mb.laos.security.jwt.JWTTokenProvider;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableWebSecurity
@Import(SecurityProblemSupport.class)
@RequiredArgsConstructor
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

	private final JWTTokenProvider<UserPrincipal> jwtTokenProvider;

	private final CorsFilter corsFilter;

	private final FilterChainExceptionHandler filterChainExceptionHandler;

	private final SecurityProblemSupport problemSupport;

	private static final String[] IGNOR_URLS = {"/app/**/*.{js,html}", "/i18n/**", "/content/**", "/test/**",};

	private static final String[] PUBLIC_URLS = {"/**/authenticate/**", "/**/public/**"};

	private static final String[] AUTHENTICATED_CLIENT_URL = {"/**/client/**"};

	private static final String[] PUBLIC_POST_URLS = {

	};

	private static final String[] PUBLIC_GET_URLS = {

	};

	private static final List<String> ALLOW_METHODS = Arrays
                    .asList(HttpMethod.POST.name(), HttpMethod.GET.name(), HttpMethod.OPTIONS.name());

	private static final String[] AUTHENTICATED_URLS = {"/**"};

	private static final String[] GATEWAY_URLS = {"/api/gateway/**"};

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder(11);
	}

	@Override
	public void configure(WebSecurity web) throws Exception {
		web.ignoring().antMatchers(HttpMethod.OPTIONS, "/**").antMatchers(IGNOR_URLS);
	}

	@Override
	public void configure(HttpSecurity http) throws Exception {
	// @formatter:off
        http
                	.csrf()
                    .csrfTokenRepository(csrfTokenRepository())
                    .requireCsrfProtectionMatcher(req -> !ALLOW_METHODS.contains(req.getMethod()))
                .and()
                    .addFilterBefore(this.corsFilter, SessionManagementFilter.class)
                    .addFilterBefore(this.filterChainExceptionHandler, CorsFilter.class)
                    //.addFilterBefore(new JWTCookieFilter(jwtTokenProvider), UsernamePasswordAuthenticationFilter.class)
                    .exceptionHandling()
                    .authenticationEntryPoint(this.problemSupport)
                    .accessDeniedHandler(this.problemSupport)
                .and()
                    .headers()
                    .contentSecurityPolicy("default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:")
                .and()
                    .referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
                .and()
                    .featurePolicy("geolocation 'none'; midi 'none'; sync-xhr 'none'; microphone 'none'; camera 'none'; magnetometer 'none'; gyroscope 'none'; speaker 'none'; fullscreen 'self'; payment 'none'")
                .and()
                    .frameOptions()
                    .deny()
                .and()
                    .sessionManagement()
                    .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                    .authorizeRequests()
                    .antMatchers(PUBLIC_URLS).permitAll()
                    .antMatchers(HttpMethod.POST, PUBLIC_POST_URLS).permitAll()
                    .antMatchers(HttpMethod.GET, PUBLIC_GET_URLS).permitAll()
                    .antMatchers(AUTHENTICATED_CLIENT_URL).hasRole("CLIENT")
                    //.antMatchers(ADMIN_URLS).hasAnyRole("USER", "SUB_ADMIN", "ADMIN", "SUPER_ADMIN")
                    .antMatchers(AUTHENTICATED_URLS).hasRole("USER")
                .and()
                    .httpBasic()
                .and()
                    .apply(this.securityConfigurerAdapter())
				.and()
                    .formLogin().disable();
//
        // @formatter:on
	}

	private JWTConfigurer securityConfigurerAdapter() {
		return new JWTConfigurer(this.jwtTokenProvider);
	}

	private CsrfTokenRepository csrfTokenRepository() {
        CookieCsrfTokenRepository repository = CookieCsrfTokenRepository.withHttpOnlyFalse();

        repository.setCookiePath("/");

        return repository;
    }
}
